from nonebot import on_command
from nonebot.adapters.onebot.v11 import Message
from nonebot.adapters.onebot.v11.permission import GROUP_ADMIN, GROUP_OWNER
from nonebot.matcher import Matcher
from nonebot.params import Arg<PERSON>lainText, CommandArg
from nonebot.permission import <PERSON><PERSON><PERSON>US<PERSON>
from nonebot.rule import to_me

from .. import my_trigger as tr
from ..rss_class import Rss

ADD_COOKIES = on_command(
    "add_cookies",
    aliases={"添加cookies"},
    rule=to_me(),
    priority=5,
    permission=GROUP_ADMIN | GROUP_OWNER | SUPERUSER,
)


@ADD_COOKIES.handle()
async def handle_first_receive(matcher: Matcher, args: Message = CommandArg()) -> None:
    plain_text = args.extract_plain_text()
    if len(plain_text.split(" ", 1)) > 1:
        matcher.set_arg("COOKIES", args)


prompt = """\
请输入：
    名称 cookies
空格分割

获取方式：
    PC端 Chrome 浏览器按 F12
    找到Console选项卡，输入:
        document.cookie
    输出的字符串就是了\
"""


@ADD_COOKIES.got("COOKIES", prompt=prompt)
async def handle_add_cookies(rss_cookies: str = ArgPlainText("COOKIES")) -> None:
    name, cookies = rss_cookies.split(" ", 1)

    # 判断是否有该名称订阅
    rss = Rss.get_one_by_name(name=name)
    if rss is None:
        await ADD_COOKIES.finish(f"❌ 不存在该订阅: {name}")
    else:
        rss.name = name
        rss.set_cookies(cookies)
        await tr.add_job(rss)
        await ADD_COOKIES.finish(f"👏 {rss.name}的Cookies添加成功！")
