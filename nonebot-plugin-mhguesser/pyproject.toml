[project]
name = "nonebot-plugin-mhguesser"
version = "0.2.3"
description = "怪物猎人猜BOSS"
authors = [
    {name = "Proito666", email = "<EMAIL>"},
]
license = {text = "MIT"}
readme = "README.md"
packages = [{include = "nonebot_plugin_mhguesser"}]
repository = "https://github.com/Proito666/nonebot-plugin-mhguesser"
dependencies = [
    "nonebot2<3.0.0,>=2.3.0",
    "nonebot-plugin-htmlrender<1.0.0,>=0.2.0",
    "nonebot-plugin-alconna<1.0.0,>=0.25.0",
    "pypinyin>=0.54.0",
    "nonebot-plugin-uninfo>=0.7.3",
    "jinja2>=3.1.6",
]
requires-python = "<4.0,>=3.9"

[tool.pdm]
distribution = true
[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"
