from nonebot import get_driver, on_message, require
from nonebot.adapters import Event
from nonebot.matcher import Matcher
from nonebot.plugin import PluginMetadata, inherit_supported_adapters
from nonebot.rule import Rule

require("nonebot_plugin_alconna")
require("nonebot_plugin_uninfo")
require("nonebot_plugin_htmlrender")

from nonebot_plugin_alconna import Alconna, Args, Image, Match, UniMessage, on_alconna
from nonebot_plugin_uninfo import Uninfo

from .config import Config
from .game import MonsterGuesser
from .render import render_correct_answer, render_guess_result

__plugin_meta__ = PluginMetadata(
    name="nonebot-plugin-mhguesser",
    description="怪物猎人猜BOSS游戏",
    usage="""指令:
mhstart - 开始游戏
结束 - 结束游戏
直接输入怪物名猜测""",
    homepage="https://github.com/Proito666/nonebot-plugin-mhguesser",
    supported_adapters=inherit_supported_adapters("nonebot_plugin_alconna", "nonebot_plugin_uninfo"),
    type="application",
    config=Config,
)
game = MonsterGuesser()
driver = get_driver()


def is_playing() -> Rule:
    async def _checker(uninfo: Uninfo) -> bool:
        return bool(game.get_game(uninfo))

    return Rule(_checker)


start_cmd = on_alconna(
    Alconna(
        "mhstart",
        Args["easy?", str],
    ),
    aliases={"怪物猎人开始"},
)
end_cmd = on_alconna("结束", rule=is_playing())
guess_matcher = on_message(rule=is_playing(), priority=15)
query_cmd = on_alconna(
    Alconna(
        "查表",
        Args["keywords?", str],
    ),
    aliases={"怪物猎人查表"},
    rule=is_playing(),
)


@start_cmd.handle()
async def handle_start(uninfo: Uninfo, matcher: Matcher, easy: Match[str]):
    if game.get_game(uninfo):
        await matcher.finish("游戏已在进行中！")
    can_easy = False
    if easy.available and easy.result in ("宝宝模式", "easy", "简单"):
        can_easy = True
        await matcher.send("已开启宝宝模式，线索足够时将获得提醒")
    else:
        await matcher.send("当前为无提醒的高玩模式，适合32yy，如果你想打开宝宝模式，请在命令后跟easy")
    game.start_new_game(uninfo, easy=can_easy)
    await matcher.send(f"游戏开始！你有{game.max_attempts}次猜测机会，直接输入怪物名即可")


@end_cmd.handle()
async def handle_end(uninfo: Uninfo):
    monster = game.get_game(uninfo)["monster"]
    game.end_game(uninfo)
    img = await render_correct_answer(monster)
    await UniMessage(Image(raw=img)).send()


@guess_matcher.handle()
async def handle_guess(uninfo: Uninfo, event: Event):
    # 检查游戏状态
    game_data = game.get_game(uninfo)
    if not game_data:
        return
    guess_name = event.get_plaintext().strip()
    if not guess_name or guess_name in ("结束", "mhstart"):
        return
    # 检查重复猜测
    if any(g["name"] == guess_name for g in game_data["guesses"]):
        await UniMessage.text(f"已经猜过【{guess_name}】了，请尝试其他怪物").send()
        return

    correct, guessed, comparison, maybe = game.guess(uninfo, guess_name)

    if correct:
        game.end_game(uninfo)
        img = await render_correct_answer(guessed)
        await UniMessage(["猜对了！正确答案：", Image(raw=img)]).send()
        return

    if not guessed:
        similar = game.find_similar_monsters(guess_name)
        if not similar:
            return
        err_msg = f"未找到怪物【{guess_name}】！\n尝试以下结果：" + "、".join(similar)
        await guess_matcher.finish(err_msg)

    if len(maybe) > 0 and len(maybe) < 20 and game_data["easy"]:
        msg = "根据目前猜测的结果，可能的怪物有：\n"
        for monster in maybe:
            msg += f"{monster['name']} - {monster['species']}\n"
        await guess_matcher.send(msg)

    attempts_left = game.max_attempts - len(game_data["guesses"])
    # 检查尝试次数
    if attempts_left <= 0:
        monster = game_data["monster"]
        game.end_game(uninfo)
        img = await render_correct_answer(monster)
        await UniMessage(["尝试次数已用尽！正确答案：", Image(raw=img)]).send()
        return

    img = await render_guess_result(guessed, comparison, attempts_left)
    await UniMessage(Image(raw=img)).send()


@query_cmd.handle()
async def handle_query(uninfo: Uninfo, matcher: Matcher, keywords: Match[str]):
    if not keywords.available:
        await matcher.finish("请输入关键词")
    results = game.get_monster_by_keyword(keywords.result)
    if not results:
        await matcher.finish("未找到相关怪物")
    msg = "找到以下怪物：\n"
    for monster in results:
        msg += f"{monster['name']} - {monster['species']}\n"
    await matcher.finish(msg)
