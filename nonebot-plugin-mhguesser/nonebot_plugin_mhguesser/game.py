from datetime import datetime
import difflib
import json
from pathlib import Path
import random

from nonebot_plugin_uninfo import Uninfo
from pypinyin import lazy_pinyin

from .config import plugin_config


class MonsterGuesser:
    def __init__(self):
        self.games: dict[str, dict] = {}
        self.data_path = Path(__file__).parent / "resources/data/monsters.json"
        self.monsters = self._load_data()
        self.max_attempts = plugin_config.mhguesser_max_attempts
        self.monster_names = [m["name"] for m in self.monsters]  # 预加载怪物名称列表
        self.pinyin_monsters = [
            "".join(lazy_pinyin(monster)) for monster in self.monster_names
        ]  # 预加载怪物名称拼音列表

    def _load_data(self) -> list[dict]:
        with open(self.data_path, encoding="utf-8") as f:
            return json.load(f)

    def get_session_id(self, uninfo) -> str:
        return f"{uninfo.scope}_{uninfo.self_id}_{uninfo.scene_path}"

    def get_game(self, uninfo: Uninfo) -> dict | None:
        return self.games.get(self.get_session_id(uninfo))

    def start_new_game(self, uninfo: Uninfo, easy: bool = False) -> dict:
        session_id = self.get_session_id(uninfo)
        self.games[session_id] = {
            "monster": random.choice(self.monsters),
            "easy": easy,
            "guesses": [],
            "start_time": datetime.now(),
        }
        return self.games[session_id]

    def guess(self, uninfo: Uninfo, name: str) -> tuple[bool, dict | None, dict, list[dict]]:
        game = self.get_game(uninfo)
        if not game or len(game["guesses"]) >= self.max_attempts:
            raise ValueError("游戏已结束")

        guessed = next((m for m in self.monsters if m["name"] == name), None)
        if not guessed:
            return False, None, {}, []

        game["guesses"].append(guessed)
        current = game["monster"]

        comparison = {
            "species": guessed["species"] == current["species"],
            "debut": guessed["debut"] == current["debut"],
            "baseId": guessed["baseId"] == current["baseId"],
            "variants": guessed["variants"] == current["variants"],
            "variantType": guessed["variantType"] == current["variantType"],
            "size": "higher"
            if guessed["size"] > current["size"]
            else "lower"
            if guessed["size"] < current["size"]
            else "same",
            "attributes": self._compare_attributes(guessed["attributes"], current["attributes"]),
        }

        # 对所有历史猜测做一个聚合，得到一个字典，里面是所有正确的字段
        full_comparison = {}
        # 对不可能的字段做过滤
        impossible = {}
        min_size = 0
        max_size = int(1e9)
        for guess in game["guesses"]:
            for key, value in comparison.items():
                if key == "attributes":
                    v = self._compare_attributes(guess["attributes"], current["attributes"])
                    full_comparison[key] = list(set(full_comparison.get(key, []) + v["common"]))
                    impossible.setdefault(key, [])
                    # 这些是不可能的属性列表
                    impossible[key].extend(list(set(v["guess"]) - set(v["target"])))
                elif key == "size":
                    # 如果当前这个比目标怪物大，允许和max_size比较
                    if guess["size"] > current["size"]:
                        max_size = min(max_size, guess["size"])
                    elif guess["size"] <= current["size"]:
                        min_size = max(min_size, guess["size"])
                else:
                    full_comparison[key] = guess[key] == current[key] or full_comparison.get(key, False)
                    if guess[key] != current[key]:
                        impossible.setdefault(key, [])
                        impossible[key].append(guess[key])
        if max_size == 0:
            max_size = int(1e9)
        if min_size == int(1e9):
            min_size = 0
        print("min:" + str(min_size) + " max:" + str(max_size))
        maybe = self.get_monster_from_full_comparison(full_comparison, current, impossible, min_size, max_size)
        print("可能的怪物：" + ",".join([m["name"] for m in maybe]))
        return guessed["name"] == current["name"], guessed, comparison, maybe

    def get_monster_from_full_comparison(
        self, full_comparison: dict, current: dict, impossible, min_size, max_size
    ) -> list[dict]:
        # 根据全比较结果筛选怪物
        results = []
        # 根据full_comparison里为true的字段获取current的值
        right_keys = [k for k, v in full_comparison.items() if v and k != "attributes"]
        attributes = full_comparison.get("attributes", [])
        for monster in self.monsters:
            not_this = False
            for key in impossible:
                if key == "attributes":
                    monster_attributes = monster["attributes"].split("/")
                    if any(attr in monster_attributes for attr in impossible[key]):
                        not_this = True
                        break
                else:
                    if monster[key] in impossible[key]:
                        not_this = True
                        break

            if not_this:
                continue

            if min_size and max_size:
                if monster["size"] < min_size or monster["size"] > max_size:
                    continue

            for key in right_keys:
                if monster[key] != current[key]:
                    break
            else:
                if attributes:
                    monster_attributes = monster["attributes"].split("/")
                    if not all(attr in monster_attributes for attr in attributes):
                        continue
                results.append(monster)
        return results

    def find_similar_monsters(self, name: str, n: int = 3) -> list[str]:
        # 使用difflib找到相似的怪物名称
        difflib_matches = difflib.get_close_matches(
            name,
            self.monster_names,
            n=n,
            cutoff=0.6,  # 相似度阈值（0-1之间）
        )
        # 通过拼音精确匹配读音一样的怪物名称
        name_pinyin = "".join(lazy_pinyin(name))  # 转换输入名称为拼音
        pinyin_matches = [
            self.monster_names[i] for i, pinyin in enumerate(self.pinyin_monsters) if pinyin == name_pinyin
        ]

        all_matches = list(dict.fromkeys(pinyin_matches + difflib_matches))
        return all_matches

    def _compare_attributes(self, guess_attr: str, target_attr: str) -> dict:
        guess_attrs = guess_attr.split("/") if guess_attr else []
        target_attrs = target_attr.split("/") if target_attr else []
        common = set(guess_attrs) & set(target_attrs)
        return {"guess": guess_attr, "target": target_attr, "common": list(common) if common else []}

    def end_game(self, uninfo: Uninfo):
        try:
            self.games.pop(self.get_session_id(uninfo))
        except (AttributeError, KeyError):
            pass

    def get_monster_by_keyword(self, keyword: str) -> list[dict]:
        keyword = keyword.lower()
        results = []
        for monster in self.monsters:
            if (
                monster["variantType"].lower() in keyword
                and monster["debut"].lower() in keyword
                and monster["species"].lower() in keyword
                and any(attribute in keyword for attribute in monster["attributes"].split("/"))
            ):
                results.append(monster)
        return results
