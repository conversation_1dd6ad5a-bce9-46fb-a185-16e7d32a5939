from nonebot import logger, require, get_driver
from nonebot.plugin import PluginMetadata, inherit_supported_adapters
from nonebot.exception import FinishedException
require("nonebot_plugin_uninfo")
require("nonebot_plugin_alconna")
from nonebot_plugin_uninfo import Uninfo

from .config import Config

__plugin_meta__ = PluginMetadata(
    name="Nonebot MCP",
    description="基于pydantic-ai的MCP插件，提供简易的模型接入，mcp-sse接入，mcp-stdio接入",
    usage="用法：/mcp [问题] --model [模型名称]，例如：/mcp 今天天气怎么样 --model openai:gpt-4o",
    type="application",
    homepage="https://github.com/X-Zero-L/nonebot-plugin-mcp",
    config=Config,
    supported_adapters=inherit_supported_adapters("nonebot_plugin_alconna", "nonebot_plugin_uninfo"),
    extra={
        "author": "X-Zero-L <<EMAIL>>",
    },
)

from arclet.alconna import Al<PERSON>na, <PERSON>rg<PERSON>, Option
from nonebot_plugin_alconna import AlconnaMatcher, Match, MsgId, on_alconna
from nonebot_plugin_alconna.uniseg import UniMessage

from . import core
from .config import plugin_config

# 无上下文的单任务模式，适合只需要单次交互的场景
no_history_task = on_alconna(
    Alconna(
        "/mcp_no_history",
        Args["prompt", str],
        Option("--model|-m|--model_name", Args["model", str], default=plugin_config.mcp_default_model, help_text="指定模型"),
        Option("--system|-s|--system_prompt", Args["system_prompt", str], default=plugin_config.default_system_prompt, help_text="自定义系统提示词"),
    ),
    use_cmd_start=True,
    priority=5,
    block=True,
    aliases={"mcps", "mcp_no_history"},
)

# 带上下文的模式
task = on_alconna(
    Alconna(
        "/mcp",
        Args["prompt", str],
        Option("--model|-m|--model_name", Args["model", str], default=plugin_config.mcp_default_model, help_text="指定模型"),
        Option("--system|-s|--system_prompt", Args["system_prompt", str], default=plugin_config.default_system_prompt, help_text="自定义系统提示词"),
    ),
    use_cmd_start=True,
    priority=5,
    block=True,
    aliases={"mcp"},
)

# 清除历史记录
clear_history = on_alconna(
    Alconna(
        "/mcp_clear",
    ),
    use_cmd_start=True,
    priority=5,
    block=True,
    aliases={"mcp_clear", "mcpc"},
)


async def process_task(
    matcher: AlconnaMatcher, 
    msg_id: MsgId, 
    prompt: str, 
    model: str, 
    system_prompt: str,
    user_id: str, 
    group_id: str = None,
    no_history: bool = False,
):
    if model not in plugin_config.allowed_models:
        await matcher.finish(
            UniMessage(f"不支持的模型，请使用以下模型之一：{', '.join(plugin_config.allowed_models)}").reply(
                msg_id
            )
        )
    
    superusers = get_driver().config.superusers
    is_admin = user_id in superusers
    if is_admin:
        logger.info(f"用户 {user_id} 是超级用户")

    # 发送处理中提示
    await matcher.send(UniMessage("正在处理，请稍候...").reply(msg_id))
    
    try:
        result = await core.run(
            user_id=user_id, 
            group_id=group_id,
            message=prompt, 
            model=model, 
            no_history=no_history,
            system_prompt=system_prompt,
            is_admin=is_admin,
        )
        await matcher.finish(result.reply(msg_id))
    except FinishedException as e:
        pass
    except Exception as e:
        logger.error(f"处理请求时出错: {e}")
        await matcher.finish(UniMessage(f"处理请求时出错: {str(e)}").reply(msg_id))


@no_history_task.handle()
async def handle_no_history_task(
    prompt: Match[str], 
    model: Match[str], 
    system_prompt: Match[str],
    session: Uninfo, 
    msg_id: MsgId
):
    await process_task(
        no_history_task, 
        msg_id, 
        prompt.result, 
        model.result, 
        system_prompt.result,
        session.user.id, 
        session.group.id if hasattr(session, "group") and session.group else None,
        no_history=True
    )


@task.handle()
async def handle_task(
    prompt: Match[str], 
    model: Match[str], 
    system_prompt: Match[str],
    session: Uninfo, 
    msg_id: MsgId
):
    await process_task(
        task, 
        msg_id, 
        prompt.result, 
        model.result, 
        system_prompt.result,
        session.user.id, 
        session.group.id if session.group else None,
    )


@clear_history.handle()
async def handle_clear_history(session: Uninfo, msg_id: MsgId):
    user_id = session.user.id
    if user_id in core.user_history:
        del core.user_history[user_id]
        await clear_history.finish(UniMessage("已清除您的聊天历史").reply(msg_id))
    else:
        await clear_history.finish(UniMessage("没有找到您的聊天历史").reply(msg_id))
