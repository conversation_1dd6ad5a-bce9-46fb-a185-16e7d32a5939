import time
import json
import os
from typing import Optional, List, Dict

import logfire
from nonebot import logger
from nonebot_plugin_alconna import UniMessage
from pydantic import BaseModel, Field, model_validator
from pydantic_ai import Agent
from pydantic_ai.mcp import MCPServerHTTP, MCPServerStdio
from pydantic_ai.messages import ModelMessage

from .config import plugin_config

# 仅在配置启用时使用logfire追踪模型的调用过程
if plugin_config.logfire_enabled:
    logfire.configure(send_to_logfire="if-token-present")
    logfire.instrument_pydantic_ai()


# MCP服务器配置的Pydantic模型
class MCPServerConfig(BaseModel):
    """单个MCP服务器配置"""
    command: Optional[str] = None
    args: List[str] = Field(default_factory=list)
    env: Dict[str, str] = Field(default_factory=dict)
    only_admin: bool = False
    url: Optional[str] = None
    headers: Dict[str, str] = Field(default_factory=dict)
    enable: bool = False
    @model_validator(mode='after')
    def validate_config(self) -> 'MCPServerConfig':
        """验证配置的有效性"""
        if not self.command and not self.url:
            raise ValueError("必须提供command或url参数")
        return self


class MCPConfig(BaseModel):
    """MCP配置文件结构"""
    mcpServers: Dict[str, MCPServerConfig] = Field(default_factory=dict)


def load_mcp_config() -> MCPConfig:
    """加载MCP配置"""
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        mcp_json_path = os.path.join(current_dir, "mcp.json")
        
        if os.path.exists(mcp_json_path):
            with open(mcp_json_path, "r", encoding="utf-8") as f:
                config_data = json.load(f)
            r = MCPConfig.model_validate(config_data)
            r.mcpServers = {k: v for k, v in r.mcpServers.items() if v.enable}
            return r
        return MCPConfig()
    except Exception as e:
        logger.error(f"读取mcp.json时出错: {e}")
        return MCPConfig()


def get_mcp_servers(is_admin: bool = False) -> list[MCPServerHTTP | MCPServerStdio]:
    """获取MCP服务器列表
    
    Args:
        is_admin: 当前用户是否为管理员
    """
    mcp_servers = []
    
    # 从mcp.json读取MCP服务器配置
    try:
        mcp_config = load_mcp_config()
        
        # 根据配置创建MCP服务器
        for server_name, server_config in mcp_config.mcpServers.items():
            # 检查是否只允许管理员使用
            if server_config.only_admin and not is_admin:
                continue
                
            # 根据配置创建相应的MCP服务器
            if server_config.command:
                # 创建MCPServerStdio
                mcp_servers.append(
                    MCPServerStdio(
                        command=server_config.command,
                        args=server_config.args,
                        env=server_config.env,
                    )
                )
            elif server_config.url:
                # 创建MCPServerHTTP
                mcp_servers.append(
                    MCPServerHTTP(
                        url=server_config.url,
                        headers=server_config.headers,
                    )
                )
        
        if mcp_servers:
            logger.info(f"从mcp.json加载了{len(mcp_servers)}个MCP服务器")
    except Exception as e:
        logger.error(f"加载MCP服务器时出错: {e}")
    
    # 支持简单的sse接入
    if plugin_config.mcp_servers:
        for server in plugin_config.mcp_servers:
            if not any(isinstance(s, MCPServerHTTP) and s.url == server for s in mcp_servers):
                mcp_servers.append(MCPServerHTTP(url=server))
    
    return mcp_servers


class UserHistory(BaseModel):
    """用户历史记录模型"""

    user_id: str
    messages: list[ModelMessage] = Field(default_factory=list)
    timestamp: float = Field(default_factory=time.time)

    class Config:
        arbitrary_types_allowed = True


user_history: dict[str, UserHistory] = {}


def get_user_history(user_id: str) -> UserHistory:
    """获取或创建用户历史记录"""
    if user_id not in user_history or (user_history[user_id].timestamp + plugin_config.history_expire_minutes * 60 < time.time()):
        user_history[user_id] = UserHistory(user_id=user_id)
    return user_history[user_id]


def set_user_history(user_id: str, messages: list[ModelMessage]):
    """设置用户历史记录"""
    if user_id not in user_history:
        user_history[user_id] = UserHistory(user_id=user_id)
    if len(messages) > plugin_config.max_history_messages:
        messages = messages[-plugin_config.max_history_messages:]
    user_history[user_id].messages = messages
    user_history[user_id].timestamp = time.time()


async def run(
    user_id: str,
    group_id: str | None = None,
    message: str | None = None,
    model: str | None = None,
    no_history: bool = False,
    system_prompt: str | None = None,
    is_admin: bool = False,
) -> UniMessage:
    """
    Args:
        user_id: 用户ID
        group_id: 群组ID
        message: 用户消息
        model: 模型名称
        no_history: 是否不使用历史记录
        system_prompt: 系统提示词
        is_admin: 用户是否为管理员
        
    Returns:
        模型回复
    """
    if not model:
        model = plugin_config.mcp_default_model
    
    if not system_prompt:
        system_prompt = plugin_config.default_system_prompt
    
    mcp_servers = get_mcp_servers(is_admin=is_admin)
    
    if not mcp_servers:
        logger.warning("未配置MCP服务器，请检查配置")
        return UniMessage("未配置MCP服务器，请检查配置")

    # 创建Agent
    agent = Agent(
        model=model,
        mcp_servers=mcp_servers,
        retries=plugin_config.retries,
        system_prompt=system_prompt,
    )
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        async with agent.run_mcp_servers():
            logger.info(f"Running MCP agent for user {user_id} with model {model}")
            response = await agent.run(
                message, 
                message_history=get_user_history(user_id).messages if not no_history else None,
            )
            
            # 更新历史记录
            if not no_history:
                set_user_history(user_id, response.all_messages())
            
            # 记录结束时间和处理时间
            end_time = time.time()
            process_time = end_time - start_time
            logger.info(f"MCP agent completed in {process_time:.2f} seconds")
            
            return UniMessage(response.data)
    except Exception as e:
        logger.error(f"MCP agent error: {e}")
        return UniMessage(f"处理过程中出现错误: {str(e)}")
