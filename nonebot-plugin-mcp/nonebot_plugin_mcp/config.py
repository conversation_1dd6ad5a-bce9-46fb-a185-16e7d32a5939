from nonebot import get_driver, get_plugin_config
from pydantic import BaseModel, Field


class Config(BaseModel):
    mcp_default_model: str = "openai:gpt-4o"  # agent使用的默认模型
    enable_example_mcp_server: bool = (
        True  # 是否启用示例MCP服务器（通过本地子进程运行）,目前为一个需要npx安装的run-python-mcp
    )
    mcp_servers: list[str] = []  # MCP服务器列表，默认全启用
    allowed_models: list[str] = [
        "openai:gpt-4o",
        "openai:gpt-4.1",
    ]  # 允许的模型列表
    default_system_prompt: str = "你是一个有帮助的AI助手，请简洁、准确地回答问题。"  # 默认系统提示词
    logfire_enabled: bool = True  # 是否启用logfire，用于追踪模型调用过程
    timeout_seconds: int = 60  # MCP请求超时时间，单位秒
    retries: int = 3  # MCP请求重试次数
    max_history_messages: int = 100  # 保存的最大历史消息数
    history_expire_minutes: int = 30  # 历史记录过期时间，单位分钟


# 配置加载
plugin_config: Config = get_plugin_config(Config)
global_config = get_driver().config
