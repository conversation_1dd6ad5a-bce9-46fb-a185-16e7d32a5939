<div align="center">
    <a href="https://v2.nonebot.dev/store">
    <img src="https://raw.githubusercontent.com/fllesser/nonebot-plugin-template/refs/heads/resource/.docs/NoneBotPlugin.svg" width="310" alt="logo"></a>

## ✨ nonebot-plugin-mcp ✨

<a href="./LICENSE">
    <img src="https://img.shields.io/github/license/X-Zero-L/nonebot-plugin-mcp.svg" alt="license">
</a>
<a href="https://pypi.python.org/pypi/nonebot-plugin-mcp">
    <img src="https://img.shields.io/pypi/v/nonebot-plugin-mcp.svg" alt="pypi">
</a>
<img src="https://img.shields.io/badge/python-3.10+-blue.svg" alt="python">
<a href="https://github.com/astral-sh/ruff">
    <img src="https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/charliermarsh/ruff/main/assets/badge/v2.json" alt="ruff">
</a>
<a href="https://github.com/astral-sh/uv">
    <img src="https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/astral-sh/uv/main/assets/badge/v0.json" alt="uv">
</a>
</div>

> [!IMPORTANT]
> **收藏项目** ～⭐️

<img width="100%" src="https://starify.komoridevs.icu/api/starify?owner=X-Zero-L&repo=nonebot-plugin-mcp" alt="starify" />


## 📖 介绍

基于 [pydantic-ai](https://ai.pydantic.dev) 的 MCP插件

## 💿 安装

<details open>
<summary>使用 nb-cli 安装</summary>
在 nonebot2 项目的根目录下打开命令行, 输入以下指令即可安装

    nb plugin install nonebot-plugin-mcp --upgrade
使用 **pypi** 源安装

    nb plugin install nonebot-plugin-mcp --upgrade -i "https://pypi.org/simple"
使用**清华源**安装

    nb plugin install nonebot-plugin-mcp --upgrade -i "https://pypi.tuna.tsinghua.edu.cn/simple"


</details>

<details>
<summary>使用包管理器安装</summary>
在 nonebot2 项目的插件目录下, 打开命令行, 根据你使用的包管理器, 输入相应的安装命令

<details open>
<summary>uv</summary>

    uv add nonebot-plugin-mcp
安装仓库 master 分支

    uv add git+https://github.com/X-Zero-L/nonebot-plugin-mcp@master
</details>

<details>
<summary>pdm</summary>

    pdm add nonebot-plugin-mcp
安装仓库 master 分支

    pdm add git+https://github.com/X-Zero-L/nonebot-plugin-mcp@master
</details>
<details>
<summary>poetry</summary>

    poetry add nonebot-plugin-mcp
安装仓库 master 分支

    poetry add git+https://github.com/X-Zero-L/nonebot-plugin-mcp@master
</details>

打开 nonebot2 项目根目录下的 `pyproject.toml` 文件, 在 `[tool.nonebot]` 部分追加写入

    plugins = ["nonebot_plugin_mcp"]

</details>

## ⚙️ 配置

在 nonebot2 项目的`.env`文件中添加下表中的必填配置

| 配置项 | 必填 | 默认值 | 说明 |
| :-----: | :---: | :----: | :------: |
| MCP_DEFAULT_MODEL | 否 | openai:gpt-4o | 默认使用的模型 |
| ENABLE_EXAMPLE_MCP_SERVER | 否 | True | 是否启用示例MCP服务器 |
| MCP_SERVERS | 否 | [] | MCP服务器URL列表 |
| ALLOWED_MODELS | 否 | 多种模型 | 允许使用的模型列表 |
| DEFAULT_SYSTEM_PROMPT | 否 | "你是..." | 默认系统提示词 |
| TIMEOUT_SECONDS | 否 | 60 | 请求超时时间(秒) |
| RETRIES | 否 | 3 | 请求重试次数 |
| MAX_HISTORY_MESSAGES | 否 | 10 | 最大历史消息数 |
| HISTORY_EXPIRE_MINUTES | 否 | 30 | 历史记录过期时间(分钟) |

### mcp.json 配置

除了使用环境变量配置外，现在也支持使用 `mcp.json` 文件配置 MCP 服务器。
`mcp.json` 文件位于插件目录下，格式如下：

```json
{
  "mcpServers": {
    "perplexity-ask": {
      "command": "docker",
      "args": [
        "run",
        "-i",
        "--rm",
        "-e",
        "PERPLEXITY_API_KEY",
        "mcp/perplexity-ask"
      ],
      "env": {
        "PERPLEXITY_API_KEY": "your-api-key"
      },
      "only_admin": false
    },
    "run-python": {
      "command": "npx",
      "args": [
        "-y",
        "@pydantic/mcp-run-python",
        "stdio"
      ],
      "env": {},
      "only_admin": false
    }
  }
}
```

配置说明：
- `mcpServers`: MCP 服务器配置
  - 每个服务器配置项：
    - `command`: 命令
    - `args`: 参数列表
    - `env`: 环境变量
    - `only_admin`: 是否只允许管理员使用
    - `url`: HTTP服务器地址（与command二选一）
    - `api_key`: API密钥（仅HTTP服务器需要）

## 🎉 使用
### 指令表
| 指令 | 权限 | 需要@ | 范围 | 说明 |
| :---: | :---: | :---: | :---: | :------: |
| /mcp [问题] | 所有人 | 否 | 私聊/群聊 | 向大语言模型提问，保留上下文 |
| /mcp [问题] --model [模型] | 所有人 | 否 | 私聊/群聊 | 使用指定模型回答问题 |
| /mcp [问题] --system [提示词] | 所有人 | 否 | 私聊/群聊 | 使用自定义系统提示词 |
| /mcps [问题] | 所有人 | 否 | 私聊/群聊 | 单次提问，不保留上下文 |
| /mcp_clear | 所有人 | 否 | 私聊/群聊 | 清除聊天历史记录 |

### 🎨 效果图

![MCP效果示例](https://raw.githubusercontent.com/X-Zero-L/nonebot-plugin-mcp/master/docs/example.png)
