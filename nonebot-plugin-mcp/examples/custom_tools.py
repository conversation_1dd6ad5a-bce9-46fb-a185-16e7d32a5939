"""
自定义工具示例

这个示例演示了如何为MCP插件添加自定义工具函数
"""

import asyncio
import httpx
from typing import Dict, List, Optional
from pydantic import BaseModel, Field

from nonebot_plugin_mcp.core import register_tool, RunContext, UserDependencies


# 自定义工具函数示例

@register_tool
async def get_weather(ctx: RunContext[UserDependencies], city: str) -> Dict[str, str]:
    """
    获取指定城市的天气信息
    
    Args:
        city: 城市名称，如"北京"、"上海"等
        
    Returns:
        包含天气信息的字典
    """
    # 这里应该调用天气API，这里仅为示例
    await asyncio.sleep(1)  # 模拟API调用
    return {
        "city": city,
        "temperature": "25°C",
        "weather": "晴天",
        "humidity": "40%",
        "wind": "东北风3级"
    }


@register_tool
async def search_web(ctx: RunContext[UserDependencies], query: str, max_results: int = 3) -> List[Dict[str, str]]:
    """
    在网络上搜索信息
    
    Args:
        query: 搜索关键词
        max_results: 返回的最大结果数，默认为3
        
    Returns:
        搜索结果列表
    """
    # 这里应该调用搜索API，这里仅为示例
    await asyncio.sleep(1.5)  # 模拟API调用
    results = [
        {"title": f"关于{query}的第一个结果", "url": f"https://example.com/1", "snippet": f"{query}的相关信息1"},
        {"title": f"关于{query}的第二个结果", "url": f"https://example.com/2", "snippet": f"{query}的相关信息2"},
        {"title": f"关于{query}的第三个结果", "url": f"https://example.com/3", "snippet": f"{query}的相关信息3"},
        {"title": f"关于{query}的第四个结果", "url": f"https://example.com/4", "snippet": f"{query}的相关信息4"},
    ]
    return results[:max_results]


@register_tool
async def translate_text(ctx: RunContext[UserDependencies], text: str, target_language: str) -> str:
    """
    翻译文本到指定语言
    
    Args:
        text: 需要翻译的文本
        target_language: 目标语言，例如"英语"、"日语"、"法语"等
        
    Returns:
        翻译后的文本
    """
    # 这里应该调用翻译API，这里仅为示例
    language_map = {
        "英语": "This is a translated text example.",
        "日语": "これは翻訳されたテキストの例です。",
        "法语": "Ceci est un exemple de texte traduit.",
        "德语": "Dies ist ein Beispiel für übersetzten Text.",
    }
    
    await asyncio.sleep(0.8)  # 模拟API调用
    return language_map.get(target_language, f"不支持的语言: {target_language}")


# 自定义输出模型示例
class DetailedResponse(BaseModel):
    """详细的响应模型"""
    content: str = Field(description="主要回复内容")
    sources: List[str] = Field(default_factory=list, description="信息来源")
    additional_info: Optional[Dict[str, str]] = Field(default=None, description="附加信息")
    sentiment: str = Field(default="neutral", description="情感倾向: positive, neutral, negative")


# 演示如何在命令行中测试工具函数
if __name__ == "__main__":
    async def test():
        # 创建一个测试依赖
        deps = UserDependencies(user_id="test_user")
        ctx = RunContext(deps=deps)
        
        # 测试天气工具
        weather = await get_weather(ctx, "北京")
        print(f"天气结果: {weather}")
        
        # 测试搜索工具
        search_results = await search_web(ctx, "人工智能", 2)
        print(f"搜索结果: {search_results}")
        
        # 测试翻译工具
        translation = await translate_text(ctx, "你好，世界", "英语")
        print(f"翻译结果: {translation}")

    # 运行测试
    asyncio.run(test()) 