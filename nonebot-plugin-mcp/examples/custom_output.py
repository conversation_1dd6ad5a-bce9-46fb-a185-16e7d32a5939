"""
自定义输出模型示例

这个示例演示了如何为MCP插件创建自定义输出模型，
以及如何使用自定义系统提示词来引导模型产生符合要求的输出
"""

from typing import Dict, List, Optional
from pydantic import BaseModel, Field
from nonebot_plugin_mcp.core import run, UserDependencies


# 基本分析结果模型
class AnalysisResult(BaseModel):
    """文本分析结果模型"""
    content: str = Field(description="回复内容")
    keywords: List[str] = Field(description="关键词列表")
    sentiment: str = Field(description="情感分析 (positive, neutral, negative)")
    summary: str = Field(description="文本摘要")


# 新闻分析模型
class NewsAnalysis(BaseModel):
    """新闻分析结果"""
    content: str = Field(description="基本回复内容")
    headline: str = Field(description="新闻标题")
    main_topics: List[str] = Field(description="主要话题")
    entities: List[Dict[str, str]] = Field(description="实体识别结果，包含名称和类型")
    bias_assessment: str = Field(description="偏见评估，包含政治偏向等")
    fact_check: List[Dict[str, str]] = Field(description="事实核查结果")
    summary: str = Field(description="简短总结")


# 商品评论分析模型
class ProductReviewAnalysis(BaseModel):
    """商品评论分析结果"""
    content: str = Field(description="回复内容")
    product_name: str = Field(description="商品名称")
    rating: float = Field(description="评分(1-5)", ge=1, le=5)
    pros: List[str] = Field(description="优点列表")
    cons: List[str] = Field(description="缺点列表")
    sentiment: str = Field(description="总体情感 (positive, neutral, negative)")
    purchase_recommendation: bool = Field(description="是否推荐购买")
    summary: str = Field(description="简短总结")


# 自定义系统提示词
ANALYSIS_SYSTEM_PROMPT = """
你是一个专业的文本分析助手。
你需要分析用户提供的文本，提取关键信息，并以结构化的方式返回分析结果。
请确保返回的每个字段都准确、客观，特别是情感分析部分。
"""

NEWS_ANALYSIS_SYSTEM_PROMPT = """
你是一个专业的新闻分析助手。
你需要分析用户提供的新闻文本，识别主要话题、实体、潜在偏见，并进行事实核查。
你的分析应当客观、公正，不掺杂个人观点。
对于事实核查，应当指出可能的虚假或误导性信息。
"""

PRODUCT_REVIEW_SYSTEM_PROMPT = """
你是一个专业的商品评论分析助手。
你需要分析用户提供的商品评论，识别商品名称、优缺点，并给出推荐建议。
评分应该基于评论的整体情感和内容，范围为1-5分。
你的分析应当客观、实用，帮助用户做出购买决策。
"""


# 示例函数：如何在你的代码中使用这些模型
async def analyze_text(text: str) -> AnalysisResult:
    """基本文本分析"""
    # 实际使用时应该通过nonebot的matcher调用
    # 这里仅作为示例说明如何使用
    result = await run(
        user_id="example_user",
        message=f"请分析以下文本：\n\n{text}",
        model="openai:gpt-4o",
        system_prompt=ANALYSIS_SYSTEM_PROMPT,
        output_type=AnalysisResult,
    )
    return result.output


async def analyze_news(news_text: str) -> NewsAnalysis:
    """新闻分析"""
    result = await run(
        user_id="example_user",
        message=f"请分析以下新闻：\n\n{news_text}",
        model="openai:gpt-4o",
        system_prompt=NEWS_ANALYSIS_SYSTEM_PROMPT,
        output_type=NewsAnalysis,
    )
    return result.output


async def analyze_product_review(review_text: str) -> ProductReviewAnalysis:
    """商品评论分析"""
    result = await run(
        user_id="example_user",
        message=f"请分析以下商品评论：\n\n{review_text}",
        model="openai:gpt-4o",
        system_prompt=PRODUCT_REVIEW_SYSTEM_PROMPT,
        output_type=ProductReviewAnalysis,
    )
    return result.output


# 如果直接运行此文件，显示使用说明
if __name__ == "__main__":
    print("""
自定义输出模型示例
    
此模块演示了如何为MCP插件创建自定义输出模型，以及如何使用自定义系统提示词。
实际使用时，应通过nonebot的matcher调用run函数，这里仅作为示例说明。

示例使用方法：
1. 在你的机器人插件中导入这些模型
2. 根据需要修改系统提示词
3. 使用run函数时指定output_type参数为对应的模型类

例如：
```python
from examples.custom_output import AnalysisResult, ANALYSIS_SYSTEM_PROMPT

@matcher.handle()
async def handle_analysis(prompt: str):
    result = await run(
        user_id=session.user.id,
        message=prompt,
        system_prompt=ANALYSIS_SYSTEM_PROMPT,
        output_type=AnalysisResult,
    )
    # 处理结果...
```
    """) 