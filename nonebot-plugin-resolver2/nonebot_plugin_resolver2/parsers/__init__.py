from .acfun import AcfunParser as AcfunParser
from .bilibili import BilibiliParser as BilibiliParser
from .douyin import <PERSON><PERSON>inPars<PERSON> as DouyinParser
from .kuaishou import <PERSON><PERSON>houParser as KuaishouParser
from .kugou import <PERSON>GouParser as KuGouParser
from .magnet import MagnetParser as MagnetParser
from .ncm import NCMParser as NCMParser
from .utils import get_redirect_url as get_redirect_url
from .weibo import WeiBoParser as WeiBoParser
from .xiaohongshu import XiaoHongShuParser as XiaoHongShuParser

__all__ = [
    "AcfunParser",
    "BilibiliParser",
    "<PERSON>uyinParser",
    "KuGouParser",
    "KuaishouParser",
    "MagnetParser",
    "NCMParser",
    "WeiBoParser",
    "XiaoHongShuParser",
    "get_redirect_url",
]
