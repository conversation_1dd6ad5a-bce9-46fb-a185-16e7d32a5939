[project]
name = "nonebot-plugin-resolver2"
version = "1.9.2"
description = "NoneBot2 链接分享解析器自动解析, BV号/链接/小程序/卡片 | B站/抖音/网易云/微博/小红书/youtube/tiktok/twitter/acfun"
authors = [{ "name" = "fllesser", "email" = "<EMAIL>" }]
urls = { Repository = "https://github.com/fllesser/nonebot-plugin-resolver2" }
readme = "README.md"
requires-python = ">=3.10"
keywords = [
  "nonebot",
  "nonebot2",
  "resolver",
  "bilibili",
  "youtube",
  "tiktok",
  "twitter",
]
dependencies = [
  "aiohttp>=3.10.5,<4.0.0",
  "curl_cffi>=0.8.0,<1.0.0",
  "tqdm>=4.67.1,<5.0.0",
  "aiofiles>=24.1.0",
  "yt-dlp>=2025.5.22",
  "nonebot2>=2.4.2,<3.0.0",
  "nonebot-adapter-onebot>=2.4.6,<3.0.0",
  "nonebot-plugin-localstore>=0.7.4,<1.0.0",
  "nonebot-plugin-apscheduler>=0.5.0,<1.0.0",
  "bilibili-api-python>=17.2.1,<18.0.0",
]


[dependency-groups]
dev = ["nonebot2[fastapi]>=2.4.2,<3.0.0", "ruff>=0.11.12,<1.0.0"]

test = [
  "nonebot2[fastapi]>=2.4.2,<3.0.0",
  "nonebug>=0.3.7,<1.0.0",
  "pytest-xdist>=3.6.1,<4.0.0",
  "pytest-asyncio>=1.0.0,<1.1.0",
]


[tool.nonebot]
plugins = ["nonebot_plugin_resolver2"]


[tool.pytest.ini_options]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "session"
pythonpath = [".", "nonebot_plugin_resolver2", "tests"]
addopts = [
  "-v",               # 详细输出
  "-s",               # 显示打印信息
  "--tb=short",       # 简短的错误回溯
  "-ra",              # 显示所有测试结果摘要
  "--strict-markers", # 严格标记模式
  # "--doctest-modules", # 运行文档测试
  "--import-mode=prepend", # 导入模式
]

[tool.ruff]
line-length = 120
target-version = "py310"

[tool.ruff.format]
line-ending = "lf"

[tool.ruff.lint]
select = [
  "F",     # Pyflakes
  "W",     # pycodestyle warnings
  "E",     # pycodestyle errors
  "I",     # isort
  "UP",    # pyupgrade
  "ASYNC", # flake8-async
  "C4",    # flake8-comprehensions
  "T10",   # flake8-debugger
  "T20",   # flake8-print
  "PYI",   # flake8-pyi
  "PT",    # flake8-pytest-style
  "Q",     # flake8-quotes
  "TID",   # flake8-tidy-imports
  "RUF",   # Ruff-specific rules
]
ignore = [
  "E402",   # module-import-not-at-top-of-file
  "UP037",  # quoted-annotation
  "RUF001", # ambiguous-unicode-character-string
  "RUF002", # ambiguous-unicode-character-docstring
  "RUF003", # ambiguous-unicode-character-comment
  "W191",   # indentation contains tabs
  # "I001",   # isort: imports are incorrectly sorted
  "TID252", # 相对导入
]


[tool.ruff.lint.isort]
force-sort-within-sections = true
known-first-party = ["tests/*"]
extra-standard-library = ["typing_extensions"]

[tool.ruff.lint.flake8-pytest-style]
fixture-parentheses = false
mark-parentheses = false

[tool.ruff.lint.pyupgrade]
keep-runtime-typing = true


[tool.pyright]
pythonVersion = "3.10"
pythonPlatform = "All"
defineConstant = { PYDANTIC_V2 = true }
executionEnvironments = [
  { root = "./tests", extraPaths = [
    "./",
  ] },
  { root = "./" },
]
typeCheckingMode = "standard"
reportShadowedImports = false
disableBytesTypePromotions = true
