name: pytest

on:
  push:
    branches:
      - 'master'
    paths-ignore:
      - 'README.md'
  pull_request:

jobs:
  plugin-load:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version:
          - "3.10"
          - "3.11"
          - "3.12"
          - "3.13"

    steps:
      - uses: actions/checkout@v4
      - name: setup-pytest
        uses: ./.github/actions/setup-pytest
        with:
          python-version: ${{ matrix.python-version }}
      - name: pytest
        run: uv run pytest tests/test_load.py


  bilibili-test:
    needs: plugin-load
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: setup-pytest
        uses: ./.github/actions/setup-pytest
      
      - name: pytest
        run: uv run pytest tests/test_bilibili.py


  weibo-test:
    needs: plugin-load
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: setup-pytest
        uses: ./.github/actions/setup-pytest
      
      - name: pytest
        run: uv run pytest tests/test_weibo.py


  twitter-test:
    needs: plugin-load
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: setup-pytest
        uses: ./.github/actions/setup-pytest
      
      - name: pytest
        run: uv run pytest tests/test_x.py



  acfun-test:
    needs: plugin-load
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: setup-pytest
        uses: ./.github/actions/setup-pytest
      
      - name: pytest
        run: uv run pytest tests/test_acfun.py

  ncm-test:
    needs: plugin-load
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: setup-pytest
        uses: ./.github/actions/setup-pytest
      
      - name: pytest
        run: uv run pytest tests/test_ncm.py

  douyin-test:
    needs: plugin-load
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: setup-pytest
        uses: ./.github/actions/setup-pytest
      
      - name: pytest
        run: uv run pytest tests/test_douyin.py
      
  xhs-test:
    needs: plugin-load
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: setup-pytest
        uses: ./.github/actions/setup-pytest
      
      - name: pytest
        run: uv run pytest tests/test_xhs.py
  
  kuaishou-test:
    needs: plugin-load
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: setup-pytest
        uses: ./.github/actions/setup-pytest
      
      - name: pytest
        run: uv run pytest tests/test_kuaishou.py
