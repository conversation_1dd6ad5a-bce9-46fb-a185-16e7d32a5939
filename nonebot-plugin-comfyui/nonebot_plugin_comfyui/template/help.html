<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>ComfyUI 插件帮助文档</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <style>
    :root {
        --primary-color: #2c3e50;
        --secondary-color: #3498db;
        --highlight-color: #E4080A;
    }

    body {
        margin: 0; /* 去除默认的 body 边距 */
        padding: 0; /* 去除默认的 body 内边距 */
        background-color: #f8f9fa; /* 背景颜色作为备用 */
        background-image: url('https://www.dmoe.cc/random.php'); /* 背景图片 */
        background-size: cover; /* 图片覆盖整个背景 */
        background-repeat: no-repeat; /* 不重复图片 */
        background-attachment: fixed; /* 固定背景图片 */
        font-family: 'Segoe UI', system-ui, sans-serif; /* 字体设置 */
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5); /* 文字阴影，增加可读性 */
    }

    .container {
        max-width: 1200px;
        padding: 2rem;
        background: white;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        border-radius: 10px;
        margin: 2rem auto;
        font-size: 1.1rem; /* 增大容器内字体大小 */
    }

    .command-card {
        border-left: 4px solid var(--secondary-color);
        margin: 1.5rem 0;
        padding: 1rem 1.5rem;
        background: #f8faff;
        border-radius: 5px;
        font-size: 1.1rem; /* 增大卡片内字体大小 */
    }

    .param-group {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1rem;
        padding: 1rem 0;
    }

    .param-item {
        padding: 0.8rem;
        background: #f8f9fa;
        border-radius: 5px;
        transition: transform 0.2s;
        font-size: 1rem; /* 增大参数项字体大小 */
    }

    .param-item:hover {
        transform: translateX(5px);
        box-shadow: 2px 2px 8px rgba(0,0,0,0.1);
    }

    .shape-preset {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin: 1rem 0;
    }

    .shape-card {
        padding: 1rem;
        background: #e8f4ff;
        border-radius: 8px;
        text-align: center;
        font-size: 1rem; /* 增大形状卡片字体大小 */
    }

    code {
        color: var(--highlight-color);
        background: rgba(231, 76, 60, 0.1);
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 1rem; /* 增大代码字体大小 */
    }

    pre {
        background: #2c3e50;
        color: #ecf0f1;
        padding: 1rem;
        border-radius: 8px;
        overflow-x: auto;
        tab-size: 4;
        font-size: 1rem; /* 增大预格式化文本字体大小 */
    }
</style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4 text-center" style="color: var(--primary-color);">
            <i class="bi bi-palette"></i> ComfyUI 绘图插件文档
        </h1>

        <!-- 命令模块 -->
        <section class="command-card">
            <h2><i class="bi bi-send"></i> 发送 prompt</h2>
            <p>发送 <code>prompt [正面提示词]</code> 进行基础绘图，插件默认不支持中文</p>
            <p>如果提示词包含'(单引号), 需要使用双引号把提示词括起来 <code>prompt "girl's"</code></p>
        </section>

        <!-- 其他命令 -->
        <section class="command-card">
            <h2><i class="bi bi-terminal"></i> 其他命令</h2>
            <div class="param-group">
                {% for cmd in other_commands %}
                <div class="param-item">
                    <code>{{ cmd.command }}</code>
                    <div class="text-muted">{{ cmd.description }}</div>
                    {% if cmd.example %}
                    <pre class="mt-2">{{ cmd.example }}</pre>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </section>


        <!-- 注册命令 -->
        <section class="command-card">
            <h2 class="text-danger"><i class="bi bi-command"></i> 额外注册命令</h2>
            <ul class="list-unstyled param-group">
                {% for cmd in reg_commands %}
                <li class="param-item">
                    <code>{{ cmd[0] }} <br> 说明: {{ cmd [1] }}</code>
                </li>
                {% endfor %}
            </ul>
        </section>

        <!-- 参数配置 -->
        <section class="command-card">
            <h2><i class="bi bi-gear"></i> 参数配置</h2>
            <div class="param-group">
                {% for param in parameters %}
                <div class="param-item">
                    <code>{{ param.flag }}</code> 
                    <span class="text-muted">{{ param.description }}</span>
                    {% if param.example %}
                    <div class="text-muted small mt-1">示例: {{ param.example }}</div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </section>

        <!-- 画幅预设 -->
        <section class="command-card">
            <h2><i class="bi bi-aspect-ratio"></i>画幅预设</h2>
            <div class="shape-preset">
                {% for preset in shape_presets %}
                <div class="shape-card">
                    <div class="fw-bold">{{ preset.name }}</div>
                    <div class="text-secondary">{{ preset.width }}×{{ preset.height }}</div>
                </div>
                {% endfor %}
            </div>
        </section>

        <!-- 队列命令 -->
        <section class="command-card">
            <h2><i class="bi bi-list-task"></i> 队列管理 - queue </h2>
            <div class="param-group">
                {% for param in queue_params %}
                <div class="param-item">
                    <code>{{ param.flag }}</code>
                    <div class="text-muted">{{ param.description }}</div>
                    {% if param.example %}
                    <pre class="mt-2">{{ param.example }}</pre>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </section>

        <!-- ComfyUI API 操作 -->
        <section class="command-card">
            <h2><i class="bi bi-list-task"></i> Comfyui其他API操作 - capi </h2>
            <div class="param-group">
                {% for param in capi_params %}
                <div class="param-item">
                    <code>{{ param.flag }}</code>
                    <div class="text-muted">{{ param.description }}</div>
                    {% if param.example %}
                    <pre class="mt-2">{{ param.example }}</pre>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </section>

        <footer class="mt-4 text-center text-muted">
            <hr>
            <small>Powered by nonebot-plugin-comfyui • Version {{ version }}</small>
        </footer>
    </div>
</body>
</html>