{"3": {"inputs": {"seed": 147582405082614, "steps": 20, "cfg": 8, "sampler_name": "euler", "scheduler": "normal", "denoise": 0.6, "model": ["4", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["11", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "sd_xl_base_1.0.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "6": {"inputs": {"text": "", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "text, watermark", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "nb_comfyui/img2img/img2img", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "10": {"inputs": {"image": "00063.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "11": {"inputs": {"pixels": ["12", 0], "vae": ["4", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "12": {"inputs": {"size": 1024, "mode": true, "images": ["10", 0]}, "class_type": "easy imageScaleDownToSize", "_meta": {"title": "Image Scale Down To Size"}}}