{"85": {"inputs": {"image": "00063.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "86": {"inputs": {"model": "wd-vit-tagger-v3", "threshold": 0.35, "character_threshold": 0.85, "replace_underscore": false, "trailing_comma": false, "exclude_tags": "", "image": ["85", 0]}, "class_type": "WD14Tagger|pysssss", "_meta": {"title": "WD14 Tagger 🐍"}}, "87": {"inputs": {"text": ["86", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}}