[project]
name = "nonebot-plugin-comfyui"
version = "0.7"
description = "适用于NoneBot2的Comfyui绘图插件"
authors = [
    {name = "DiaoDaiaC<PERSON>", email = "<PERSON><PERSON><PERSON><PERSON><PERSON>@qq.com"},
]
dependencies = [
    "aiohttp>=3.10.10",
    "tqdm>=4.66.5",
    "aiofiles>=24.1.0",
    "nonebot_plugin_alconna>=0.53.1",
    "pydantic>=1.10.0",
    "nonebot2>=2.2.0",
    "pillow>=11.0.0",
    "nonebot-plugin-htmlrender>=*******",
    "filetype~=1.2.0",
    "packaging~=24.2",
    "Jinja2~=3.1.4",
    "nest-asyncio>=1.6.0",
]
requires-python = ">=3.10"
readme = "README.md"
license = {text = "MIT"}

[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"


[tool.pdm]
distribution = true
