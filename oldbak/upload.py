class AlistClient:
    def __init__(self):
        self.url = alist_base_url
        self.username = "zero"
        self.password = "8eXb2iSg"
        self.token = None

    async def login(self):
        res = await aiorequests.post(
            f"{self.url}/api/auth/login",
            json={"username": self.username, "password": self.password},
        )
        res_json = await res.json()
        self.token = res_json["data"]["token"]
        logger.info(f"alist token: {self.token}")
        return self.token

    async def list(self):
        if not self.token:
            await self.login()
        res = await aiorequests.get(
            f"{self.url}/api/fs/list",
            headers={"Authorization": self.token},
        )
        res_json = await res.json()
        return res_json

    async def upload(self, file: bytes, file_name: str):
        if not self.token:
            await self.login()
        data = MultipartEncoder(
            fields={
                "file": ("test.zip", file),
            }
        )
        from urllib.parse import quote

        filename_new = quote(file_name)
        headers = {
            "Authorization": self.token,
            "Content-Type": data.content_type,
            "file-name": f"test.zip",
        }
        logger.info(f"uploading {file_name}, {len(file)} bytes ({len(file) / 1024 / 1024:.2f} MB)")
        res = await aiorequests.put(
            f"{self.url}/api/fs/form",
            data=data,
            headers=headers,
        )
        return await res.text


class AliPanWebDav:
    def __init__(self, username, password):
        self.username = username
        self.password = password
        self.base_url = "https://openapi.alipan.com/dav"
        self.auth = (self.username, self.password)

    async def upload(self, file: bytes, file_name: str):
        url = f"{self.base_url}/zero/{file_name}"
        headers = {"Content-Type": "application/octet-stream"}
        response = await aiorequests.put(url, data=file, headers=headers, auth=self.auth)
        r = await response.text
        return r

    async def download(self, file_name: str):
        url = f"{self.base_url}/zero/{file_name}"
        response = await aiorequests.get(url, auth=self.auth)
        r = await response.content
        return r

    async def delete(self, file_name: str):
        url = f"{self.base_url}/zero/{file_name}"
        response = await aiorequests.delete(url, auth=self.auth)
        r = await response.text
        return r

    async def list_files(self):
        url = f"{self.base_url}/zero/"
        response = await aiorequests.get(url, auth=self.auth)
        r = await response.text
        return r


import easywebdav


class AliWebDavV2:
    def __init__(self, username, password):
        self.username = username
        self.password = password
        self.base_url = "8.210.236.227"
        self.auth = (self.username, self.password)
        self.client = easywebdav.Client(self.base_url, port=9001, auth=self.auth, path="/zero")

    async def upload(self, file: bytes, file_name: str):
        self.client.upload(file, f"{file_name}")
        return True

    async def download(self, file_name: str):
        return self.client.download(file_name)

    async def delete(self, file_name: str):
        self.client.delete(file_name)
        return True


from minio import Minio


class MinioClient:
    def __init__(self):
        self.url = "8.210.236.227:9000"
        self.access_key = "5RGP9ItsRq2xowWubUAr"
        self.secret_key = "KkjSVpRU5VsBtzWeLrA0oPjN7fi8zlP3RkFMcdOj"
        self.client = Minio(
            self.url,
            access_key=self.access_key,
            secret_key=self.secret_key,
            secure=False,
        )
        self.bucket_name = "zero"
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=10)

    async def upload(self, file: BytesIO, file_name: str):
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(self.executor, self._sync_upload, file, file_name)
        return result

    def _sync_upload(self, file: BytesIO, file_name: str):
        # 调用同步的 client 方法
        r = self.client.put_object(self.bucket_name, file_name, file, len(file.getvalue()))
        return True if r else False


minio_client = MinioClient()
