[project]
name = "nonebot-plugin-setu-collection"
version = "0.4.0r3"
description = ""
authors = [{ name = "<PERSON><PERSON><PERSON><PERSON>", email = "<EMAIL>" }]
readme = "README.md"
requires-python = ">=3.11,<4.0.0"
dependencies = [
    "pydantic (>=2.11.4,<3.0.0)",
    "nonebot2 (>=2.4.2,<3.0.0)",
    "clovers (>=0.4.6,<0.5)",
    "nonebot-plugin-clovers (>=0.3.4,<0.4)",
    "clovers-setu-collection (>=0.2.3,<0.3.0)",

]
[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
[tool.poetry.dependencies]
clovers = { path = "D:/CLOVERS/clovers", develop = true }
nonebot-plugin-clovers = { path = "D:/CLOVERS/nonebot-plugin-clovers", develop = true }
clovers-setu-collection = { path = "D:/CLOVERS_PLUGINS/clovers-setu-collection", develop = true }
