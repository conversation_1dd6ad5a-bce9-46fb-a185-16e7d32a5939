[project]
name = "nonebot-plugin-setu-collection"
version = "0.4.0r3"
description = "从多个api获取色图并根据场景整合的色图插件"
authors = [{ name = "<PERSON><PERSON><PERSON><PERSON>", email = "<EMAIL>" }]
readme = "README.md"
requires-python = ">=3.11,<4.0.0"
dependencies = [
    "pydantic (>=2.11.4,<3.0.0)",
    "nonebot2 (>=2.4.2,<3.0.0)",
    "nonebot-plugin-alconna (>=0.52.0,<1.0.0)",
    "httpx (>=0.27.0,<1.0.0)",
]
[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
