#!/usr/bin/env python3
"""
简单的API测试脚本，不依赖NoneBot配置
"""

import asyncio
import json

import httpx


class TestSetuAPI:
    def __init__(self):
        self.client = httpx.AsyncClient()

    async def test_lolicon_api(self):
        """测试Lolicon API"""
        print("测试 Lolicon API...")
        try:
            resp = await self.client.get("https://api.lolicon.app/setu/v2?num=1&r18=0&excludeAI=1")
            if resp.status_code == 200:
                data = json.loads("".join(x for x in resp.text if x.isprintable()))
                if data.get("data"):
                    urls = [x["urls"]["original"] for x in data["data"]]
                    print(f"✓ Lolicon API 成功，获取到 {len(urls)} 个URL")
                    print(f"  示例URL: {urls[0][:50]}...")
                    return urls
                else:
                    print("✗ Lolicon API 返回空数据")
            else:
                print(f"✗ Lolicon API HTTP错误: {resp.status_code}")
        except Exception as e:
            print(f"✗ Lolicon API 测试失败: {e}")
        return []

    async def test_anosu_api(self):
        """测试Anosu API"""
        print("测试 Anosu API...")
        try:
            resp = await self.client.get("https://image.anosu.top/pixiv/json")
            if resp.status_code == 200:
                data = json.loads("".join(x for x in resp.text if x.isprintable()))
                if data:
                    urls = [x["url"] for x in data]
                    print(f"✓ Anosu API 成功，获取到 {len(urls)} 个URL")
                    print(f"  示例URL: {urls[0][:50]}...")
                    return urls
                else:
                    print("✗ Anosu API 返回空数据")
            else:
                print(f"✗ Anosu API HTTP错误: {resp.status_code}")
        except Exception as e:
            print(f"✗ Anosu API 测试失败: {e}")
        return []

    async def test_mirlkoi_api(self):
        """测试MirlKoi API"""
        print("测试 MirlKoi API...")
        try:
            resp = await self.client.get(
                "https://dev.iw233.cn/api.php?sort=iw233&type=json&num=1", headers={"Referer": "http://www.weibo.com/"}
            )
            if resp.status_code == 200:
                data = json.loads("".join(x for x in resp.text if x.isprintable()))
                if data.get("pic"):
                    urls = data["pic"]
                    print(f"✓ MirlKoi API 成功，获取到 {len(urls)} 个URL")
                    print(f"  示例URL: {urls[0][:50]}...")
                    return urls
                else:
                    print("✗ MirlKoi API 返回空数据")
            else:
                print(f"✗ MirlKoi API HTTP错误: {resp.status_code}")
        except Exception as e:
            print(f"✗ MirlKoi API 测试失败: {e}")
        return []

    async def test_download(self, url):
        """测试图片下载"""
        print(f"测试下载图片: {url[:50]}...")
        try:
            resp = await self.client.get(url, headers={"Referer": "http://www.weibo.com/"})
            if resp.status_code == 200:
                print(f"✓ 图片下载成功，大小: {len(resp.content)} bytes")
                return resp.content
            else:
                print(f"✗ 图片下载失败: HTTP {resp.status_code}")
        except Exception as e:
            print(f"✗ 图片下载失败: {e}")
        return None

    async def run_tests(self):
        """运行所有测试"""
        print("开始测试色图API...")

        # 测试各个API
        lolicon_urls = await self.test_lolicon_api()
        anosu_urls = await self.test_anosu_api()
        mirlkoi_urls = await self.test_mirlkoi_api()

        # 测试下载
        print("\n测试图片下载...")
        test_urls = []
        if lolicon_urls:
            test_urls.append(lolicon_urls[0])
        if anosu_urls:
            test_urls.append(anosu_urls[0])
        if mirlkoi_urls:
            test_urls.append(mirlkoi_urls[0])

        for url in test_urls[:2]:  # 只测试前两个
            await self.test_download(url)

        await self.client.aclose()
        print("\n测试完成!")


async def main():
    tester = TestSetuAPI()
    await tester.run_tests()


if __name__ == "__main__":
    asyncio.run(main())
