#!/usr/bin/env python3
"""
简单的插件测试脚本
"""

import asyncio
from pathlib import Path
import sys

# 添加插件路径到sys.path
plugin_path = Path(__file__).parent / "nonebot_plugin_setu_collection"
sys.path.insert(0, str(plugin_path.parent))

# 直接导入API类进行测试
from nonebot_plugin_setu_collection.api.Anosu import API as AnosuAPI
from nonebot_plugin_setu_collection.api.Lolicon import API as LoliconAPI
from nonebot_plugin_setu_collection.api.MirlKoi import API as MirlKoiAPI


async def test_api():
    """测试API功能"""
    print("测试API功能...")

    # 测试Lolicon API
    try:
        api = LoliconAPI()
        print(f"✓ {api.name} 实例创建成功")

        # 测试获取图片URL
        urls = await api.api(1, 0, "")
        if urls:
            print(f"✓ 获取到 {len(urls)} 个图片URL")
            print(f"  第一个URL: {urls[0][:50]}...")
        else:
            print("✗ 未获取到图片URL")

    except Exception as e:
        print(f"✗ Lolicon API 测试失败: {e}")

    # 测试Anosu API
    try:
        api = AnosuAPI()
        print(f"✓ {api.name} 实例创建成功")

        urls = await api.api(1, 0, "")
        if urls:
            print(f"✓ 获取到 {len(urls)} 个图片URL")
            print(f"  第一个URL: {urls[0][:50]}...")
        else:
            print("✗ 未获取到图片URL")

    except Exception as e:
        print(f"✗ Anosu API 测试失败: {e}")

    # 测试MirlKoi API
    try:
        api = MirlKoiAPI()
        print(f"✓ {api.name} 实例创建成功")

        urls = await api.api(1, 0, "")
        if urls:
            print(f"✓ 获取到 {len(urls)} 个图片URL")
            print(f"  第一个URL: {urls[0][:50]}...")
        else:
            print("✗ 未获取到图片URL")

    except Exception as e:
        print(f"✗ MirlKoi API 测试失败: {e}")


async def test_download():
    """测试图片下载功能"""
    print("\n测试图片下载功能...")

    try:
        api = LoliconAPI()
        images = await api.call(1, 0, "")

        if images:
            print(f"✓ 成功下载 {len(images)} 张图片")
            print(f"  第一张图片大小: {len(images[0])} bytes")
        else:
            print("✗ 未下载到图片数据")

    except Exception as e:
        print(f"✗ 图片下载测试失败: {e}")


async def main():
    """主测试函数"""
    print("开始测试 nonebot_plugin_setu_collection...")

    await test_api()
    await test_download()

    print("\n测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
