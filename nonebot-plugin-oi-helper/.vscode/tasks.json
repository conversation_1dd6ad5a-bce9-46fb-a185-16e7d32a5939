{"version": "2.0.0", "tasks": [{"label": "🗑️ Clean Dist", "type": "shell", "command": "node", "args": ["${workspaceFolder}/scripts/clean-dist.js"], "group": {"kind": "build", "isDefault": true}, "problemMatcher": []}, {"label": "🗑️ Clean Bytecode Of Python", "type": "shell", "command": "node", "args": ["${workspaceFolder}/scripts/clean-bytecode.js"], "group": {"kind": "none", "isDefault": true}, "problemMatcher": []}, {"label": "📦 Build", "type": "shell", "command": "uv", "args": ["build"], "group": {"kind": "build", "isDefault": true}}, {"label": "🗑️ Clean nonebot cache", "type": "shell", "command": "uv", "args": ["run", "${workspaceFolder}/scripts/clean-cache.py"], "group": {"kind": "none", "isDefault": true}, "problemMatcher": []}]}