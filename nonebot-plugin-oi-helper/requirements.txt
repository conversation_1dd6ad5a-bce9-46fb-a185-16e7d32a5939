# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.11.18
    # via nonebot-plugin-oi-helper (pyproject.toml)
aiosignal==1.3.2
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via nonebot2
apscheduler==3.11.0
    # via nonebot-plugin-apscheduler
attrs==25.3.0
    # via aiohttp
colorama==0.4.6
    # via loguru
exceptiongroup==1.3.0
    # via nonebot2
frozenlist==1.6.0
    # via
    #   aiohttp
    #   aiosignal
idna==3.10
    # via
    #   anyio
    #   yarl
loguru==0.7.3
    # via nonebot2
multidict==6.4.3
    # via
    #   aiohttp
    #   yarl
nonebot-plugin-apscheduler==0.5.0
    # via nonebot-plugin-oi-helper (pyproject.toml)
nonebot-plugin-localstore==0.7.4
    # via nonebot-plugin-oi-helper (pyproject.toml)
nonebot2==2.4.2
    # via
    #   nonebot-plugin-oi-helper (pyproject.toml)
    #   nonebot-plugin-apscheduler
    #   nonebot-plugin-localstore
nonestorage==0.1.0
    # via nonebot-plugin-localstore
propcache==0.3.1
    # via
    #   aiohttp
    #   yarl
pydantic==2.11.4
    # via
    #   nonebot-plugin-apscheduler
    #   nonebot-plugin-localstore
    #   nonebot2
pydantic-core==2.34.1
    # via pydantic
pygtrie==2.5.0
    # via nonebot2
python-dotenv==1.1.0
    # via nonebot2
sniffio==1.3.1
    # via anyio
typing-extensions==4.13.2
    # via
    #   anyio
    #   nonebot-plugin-localstore
    #   nonebot2
    #   pydantic
    #   pydantic-core
    #   typing-inspection
typing-inspection==0.4.0
    # via pydantic
tzdata==2025.2
    # via tzlocal
tzlocal==5.3.1
    # via apscheduler
win32-setctime==1.2.0
    # via loguru
yarl==1.20.0
    # via
    #   aiohttp
    #   nonebot2
