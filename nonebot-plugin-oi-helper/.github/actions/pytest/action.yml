name: Test next release with pytest
description: 'Run pytest tests'

inputs:
  CLIST__USERNAME:
    description: 'Username for the Clist API'
    required: true
  CLIST__USER_KEY:
    description: 'API key for the Clist API'
    required: true

runs:
  using: composite
  steps:
    - name: Install uv
      uses: astral-sh/setup-uv@v6
      with:
        enable-cache: true

    - name: Install the project
      run: uv sync --all-extras --dev
      shell: bash

    - name: Edit the configuration file
      run: |
        cp .env.example .env
      shell: bash

    - name: Run tests
      run: |
        export ENVIRONMENT=dev
        export CLIST__USERNAME=${{ inputs.CLIST__USERNAME }}
        export CLIST__USER_KEY=${{ inputs.CLIST__USER_KEY }}
        uv run pytest -v -s tests --junitxml=junit/test-results.xml
      shell: bash

    - name: Upload pytest results
      uses: actions/upload-artifact@v4
      with:
        name: pytest-results
        path: junit/test-results.xml
      if: ${{ always() }}
