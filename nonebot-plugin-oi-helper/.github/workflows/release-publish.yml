name: Publish Release

on:
  push:
    tags:
      - "*.*.*"

permissions:
  contents: write

jobs:
  version-test:
    name: Test next release with pytest
    runs-on: ubuntu-latest
    environment: pypi
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Execute the pytest action
        uses: ./.github/actions/pytest
        with:
          CLIST__USERNAME: ${{ secrets.CLIST__USERNAME }}
          CLIST__USER_KEY: ${{ secrets.CLIST__USER_KEY }}

  release-publish:
    needs: version-test
    name: Publish Release
    runs-on: ubuntu-latest
    environment: pypi
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true

      - name: Install the project
        run: uv sync --all-groups --dev

      - name: Build a binary wheel and a source tarball
        run: |
          uv build --sdist --wheel --out-dir dist/ .

      - name: Get version
        id: get_version
        run: echo "VERSION=${GITHUB_REF/refs\/tags\//}" >> $GITHUB_OUTPUT

      - name: Create a release
        uses: softprops/action-gh-release@v2
        if: startsWith(github.ref, 'refs/tags/')
        with:
          name: ${{ steps.get_version.outputs.VERSION }}
          tag_name: ${{ steps.get_version.outputs.VERSION }}
          draft: true
          fail_on_unmatched_files: true
          generate_release_notes: true
          prerelease: false
          files: dist/*
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.get_version.outputs.VERSION }}-dist
          path: dist/*
          retention-days: 7
          if-no-files-found: error

  pypi-publish:
    name: Publish to PyPI
    runs-on: ubuntu-latest
    environment: pypi
    needs: release-publish
    steps:
      - name: checkout
        uses: actions/checkout@v4

      - name: Get version
        id: get_version
        run: echo "VERSION=${GITHUB_REF/refs\/tags\//}" >> $GITHUB_OUTPUT

      - name: Publish the release to PyPI
        uses: ./.github/actions/pypi-publish
        with:
          UV_PUBLISH_TOKEN: ${{ secrets.UV_PUBLISH_TOKEN }}
          ENV_VERSION: ${{ steps.get_version.outputs.VERSION }}

      - name: Notify Status
        run: echo "Published ${{ steps.get_version.outputs.VERSION }} to PyPI"
