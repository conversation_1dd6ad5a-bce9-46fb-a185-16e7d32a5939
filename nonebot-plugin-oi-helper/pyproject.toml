[project]
name = "nonebot-plugin-oi-helper"
version = "0.7.0"
description = "A NoneBot2 plugin for querying OI/ACM related information, including LeetCode daily question."
readme = "README.md"
requires-python = ">=3.10, <4.0"
authors = [{ name = "talentestors", email = "<EMAIL>" }]
license = { text = "AGPL-3.0-or-later" }

keywords = ["nonebot", "plugin", "OI", "ACM", "LeetCode"]

classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: GNU Affero General Public License v3 or later (AGPLv3+)",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Framework :: AsyncIO",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Software Development :: Libraries",
]

dependencies = [
    "aiohttp>=3.11.14",
    "nonebot-plugin-apscheduler>=0.5.0",
    "nonebot-plugin-localstore>=0.7.4",
    "nonebot2>=2.4.2",
]

[dependency-groups]
dev = [
    "nonebot-adapter-console>=0.6.0",
    "nonebug>=0.4.3",
    "pip>=24.3.1",
    "pytest-asyncio>=0.25.3",
]

[project.urls]
homepage = "https://github.com/talentestors/nonebot-plugin-oi-helper"
repository = "https://github.com/talentestors/nonebot-plugin-oi-helper"
documentation = "https://github.com/talentestors/nonebot-plugin-oi-helper#readme"

[tool.pytest.ini_options]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "session"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build]
include = ["pyproject.toml", "README.md", "LICENSE", "nonebot_plugin_oi_helper"]
exclude = ["tests", "docs", "nonebot_plugin_oi_helper/.cache"]
