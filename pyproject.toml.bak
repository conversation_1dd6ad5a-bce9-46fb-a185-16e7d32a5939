[project]
name = "neuro-sama-turbo"
version = "0.1.0"
description = "neuro-sama-turbo"
readme = "README.md"
requires-python = ">=3.10, <4.0"
dependencies = [
    "aiofiles>=24.1.0",
    "aiohttp[speedups]~=3.12.13",
    "arclet-cithun>=0.2.0",
    "arrow~=1.3.0",
    "async-timeout~=4.0.3",
    "bbcode~=1.1.0",
    "bs4>=0.0.2",
    "cachetools~=5.5.2",
    "deep-translator~=1.11.4",
    "e2b-code-interpreter>=1.2.0",
    "emoji~=2.14.1",
    "feedparser~=6.0.11",
    "filetype>=1.2.0",
    "google-genai>=1.7.0",
    "google-generativeai>=0.8.4",
    "imagehash~=4.3.2",
    "img2pdf>=0.6.0",
    "jieba>=0.42.1",
    "jmcomic>=2.5.32",
    "langchain-community>=0.3.26",
    "logfire>=3.5.3",
    "magneturi~=1.3",
    "mammoth>=1.9.1",
    "markdownify>=1.1.0",
    "matplotlib>=3.10.3",
    "motor>=3.7.1",
    "nb-cli~=1.4.2",
    "nest-asyncio>=1.6.0",
    "nonebot-adapter-onebot==2.4.6",
    "nonebot-plugin-alconna>=0.55.1",
    "nonebot-plugin-apscheduler>=0.5.0",
    "nonebot-plugin-chatrecorder>=0.7.0",
    "nonebot-plugin-guild-patch>=0.2.3",
    "nonebot-plugin-htmlrender>=0.6.0",
    "nonebot-plugin-mhguesser>=0.2.3",
    "nonebot-plugin-orm[default]>=0.7.6",
    "nonebot-plugin-resolver2",
    "nonebot-plugin-send-anything-anywhere>=0.7.1",
    "nonebot-plugin-session>=0.3.2",
    "nonebot-plugin-session-orm>=0.2.2",
    "nonebot-plugin-uninfo>=0.7.2",
    "nonebot-plugin-user>=0.4.4",
    "nonebot-plugin-userinfo>=0.2.6",
    "nonebot-plugin-waiter>=0.8.1",
    "nonebot-session-to-uninfo>=0.0.2",
    "nonebot2[fastapi]>=2.4.1",
    "numpy>=2.0.2",
    "onnxruntime>=1.20.1",
    "packaging>=24.2",
    "pandas>=2.2.3",
    "pathvalidate>=3.3.1",
    "pdfminer>=20191125",
    "peewee>=3.18.1",
    "pikpakapi~=0.1.11",
    "pillow>=11.1.0",
    "pydantic>=1.10.22,!=2.5.0,!=2.5.1,<3.0.0",
    "pydantic-ai[mcp]>=0.0.46",
    "pymongo>=4.11.1",
    "pymongolite>=0.1.7",
    "pypdf2>=3.0.1",
    "pyquery~=2.0.1",
    "python-qbittorrent~=0.4.3",
    "pyyaml>=6.0.2",
    "pyzipper>=0.3.6",
    "redis>=6.2.0",
    "reportlab>=4.3.1",
    "serpapi>=0.1.5",
    "smolagents>=1.19.0",
    "tabulate>=0.9.0",
    "tavily-python>=0.5.1",
    "tenacity~=8.5.0",
    "tinydb~=4.8.2",
    "tqdm>=4.67.1",
    "tzlocal>=5.3",
    "yarl~=1.20.1",
]

[tool.nonebot]
adapters = [
    { name = "OneBot V11", module_name = "nonebot.adapters.onebot.v11" },
]
plugins = [
    "nonebot_plugin_alconna",
    "nonebot_plugin_saa",
    "nonebot_plugin_guild_patch",
]
plugin_dirs = [
    "neuro_sama_turbo/plugins",
    "nonebot-plugin-comfyui",
    "gemini-vision",
    "nonebot-plugin-mhguesser",
    "nonebot-plugin-aniguessr/src",
    "nonebot-plugin-gpt-image/src",
    "nonebot-plugin-resolver2",
]
builtin_plugins = []

[tool.uv.workspace]
members = ["publish/jmhelper"]

[tool.uv.sources]
nonebot-plugin-resolver2 = { path = "nonebot-plugin-resolver2" }

[tool.ruff]
line-length = 120
target-version = "py310"

[tool.ruff.format]
line-ending = "lf"

[tool.ruff.lint]
select = [
    "F",     # Pyflakes
    "W",     # pycodestyle warnings
    "E",     # pycodestyle errors
    "I",     # isort
    "UP",    # pyupgrade
    "ASYNC", # flake8-async
    "C4",    # flake8-comprehensions
    "T10",   # flake8-debugger
    "T20",   # flake8-print
    "PYI",   # flake8-pyi
    "PT",    # flake8-pytest-style
    "Q",     # flake8-quotes
    "TID",   # flake8-tidy-imports
    "RUF",   # Ruff-specific rules
]
ignore = [
    "E402",   # module-import-not-at-top-of-file
    "UP037",  # quoted-annotation
    "RUF001", # ambiguous-unicode-character-string
    "RUF002", # ambiguous-unicode-character-docstring
    "RUF003", # ambiguous-unicode-character-comment
    "W191",   # indentation contains tabs
    "TID252", # relative import
]


[tool.ruff.lint.isort]
force-sort-within-sections = true
known-first-party = ["tests/*"]
extra-standard-library = ["typing_extensions"]

[tool.ruff.lint.flake8-pytest-style]
fixture-parentheses = false
mark-parentheses = false

[tool.ruff.lint.pyupgrade]
keep-runtime-typing = true
