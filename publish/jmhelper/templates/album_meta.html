<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ album.name }} - 漫画信息</title>
    <style>
        :root {
            --primary-color: #7659ff;
            --primary-light: #a29bfe;
            --primary-dark: #5e48e8;
            --secondary-color: #ff7eb3;
            --text-color: #2d3748;
            --text-light: #718096;
            --text-lighter: #a0aec0;
            --bg-color: #f8f9fa;
            --card-bg: #ffffff;
            --card-border-radius: 12px;
            --shadow: 0 10px 25px rgba(118, 89, 255, 0.07), 0 6px 12px rgba(0, 0, 0, 0.04);
            --tag-bg: rgba(118, 89, 255, 0.1);
            --tag-color: var(--primary-dark);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'NotoSansSC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            padding: 0;
            margin: 0;
            line-height: 1.5;
            width: 100%;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        html,
        body {
            min-height: 100%;
            overflow-x: hidden;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        /* 主要内容区域 */
        .main-content {
            display: grid;
            grid-template-columns: minmax(300px, 1fr) 2fr;
            gap: 40px;
            padding: 0;
            margin-top: 30px;
        }

        @media (max-width: 900px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        /* 封面区域 */
        .cover-section {
            position: sticky;
            top: 20px;
            align-self: flex-start;
        }

        .album-cover-container {
            position: relative;
            border-radius: var(--card-border-radius);
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 20px;
            background-color: var(--card-bg);
        }

        .album-cover-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .album-cover {
            width: 100%;
            display: block;
            aspect-ratio: 2/3;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .album-cover:hover {
            transform: scale(1.03);
        }

        .download-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--primary-color);
            color: white;
            padding: 15px;
            border-radius: var(--card-border-radius);
            font-size: 18px;
            font-weight: 600;
            text-decoration: none;
            box-shadow: 0 4px 12px rgba(118, 89, 255, 0.3);
            transition: all 0.2s ease;
            width: 100%;
            margin-top: 20px;
        }

        .download-btn:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(118, 89, 255, 0.4);
        }

        /* 统计信息卡片 */
        .stats-card {
            background-color: var(--card-bg);
            border-radius: var(--card-border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            margin-top: 20px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0;
        }

        .stat-item {
            padding: 20px 10px;
            text-align: center;
            transition: transform 0.2s ease, background-color 0.2s ease;
            position: relative;
        }

        .stat-item:not(:last-child)::after {
            content: '';
            position: absolute;
            right: 0;
            top: 25%;
            height: 50%;
            width: 1px;
            background-color: rgba(0, 0, 0, 0.07);
        }

        .stat-item:hover {
            background-color: rgba(118, 89, 255, 0.05);
            transform: translateY(-2px);
        }

        .stat-value {
            font-size: 26px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-light);
        }

        /* 信息区域 */
        .info-section {
            background-color: var(--card-bg);
            border-radius: var(--card-border-radius);
            padding: 30px;
            box-shadow: var(--shadow);
        }

        .album-header {
            margin-bottom: 30px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.07);
            padding-bottom: 20px;
        }

        .album-title {
            font-size: 30px;
            font-weight: 700;
            margin-bottom: 12px;
            color: var(--text-color);
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;
        }

        .id-badge {
            display: inline-block;
            background-color: rgba(118, 89, 255, 0.1);
            padding: 4px 12px;
            border-radius: 8px;
            font-size: 16px;
            color: var(--primary-color);
            font-weight: 600;
        }

        .authors-container {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }

        .author-badge {
            background-color: var(--tag-bg);
            color: var(--primary-color);
            padding: 6px 14px;
            border-radius: 8px;
            font-size: 15px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            transition: all 0.2s ease;
        }

        .author-badge:hover {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .info-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-light);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-value {
            font-size: 16px;
            color: var(--text-color);
            font-weight: 500;
        }

        .emoji {
            font-size: 18px;
        }

        .tags-section {
            margin-top: 30px;
            border-top: 1px solid rgba(0, 0, 0, 0.07);
            padding-top: 25px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .tag {
            display: inline-flex;
            align-items: center;
            background-color: var(--tag-bg);
            color: var(--tag-color);
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .tag:hover {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .footer {
            text-align: center;
            padding: 30px 0;
            font-size: 14px;
            color: var(--text-light);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .main-content {
                gap: 25px;
            }

            .info-section {
                padding: 20px;
            }

            .album-title {
                font-size: 24px;
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .info-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .stats {
                grid-template-columns: 1fr;
            }

            .stat-item::after {
                display: none;
            }

            .stat-item {
                border-bottom: 1px solid rgba(0, 0, 0, 0.07);
            }

            .stat-item:last-child {
                border-bottom: none;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="main-content">
            <!-- 封面区域 -->
            <div class="cover-section">
                <div class="album-cover-container">
                    <img class="album-cover" src="{{ album.cover }}" alt="{{ album.name }} 封面">
                </div>
                
                <div class="stats-card">
                    <div class="stats">
                        <div class="stat-item">
                            <div class="stat-value">{{ album.views }}</div>
                            <div class="stat-label">浏览量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">{{ album.likes }}</div>
                            <div class="stat-label">喜欢</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">{{ album.comment_count }}</div>
                            <div class="stat-label">评论</div>
                        </div>
                    </div>
                </div>
                
                <a href="#" class="download-btn">/jm {{ album.album_id }}</a>
            </div>

            <!-- 信息区域 -->
            <div class="info-section">
                <div class="album-header">
                    <div class="album-title">
                        {{ album.name }}
                        <span class="id-badge">#{{ album.album_id }}</span>
                    </div>
                    
                    <div class="authors-container">
                        <span class="info-label"><span class="emoji">👤</span> 作者:</span>
                        {% if album.authors %}
                        {% for author in album.authors %}
                        <span class="author-badge">{{ author }}</span>
                        {% endfor %}
                        {% else %}
                        <span class="author-badge">未知</span>
                        {% endif %}
                    </div>
                </div>

                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">
                            <span class="emoji">📅</span> 发布日期
                        </div>
                        <div class="info-value">
                            {{ album.pub_date }}
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">
                            <span class="emoji">🔄</span> 更新日期
                        </div>
                        <div class="info-value">
                            {{ album.update_date }}
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">
                            <span class="emoji">📖</span> 页数
                        </div>
                        <div class="info-value">
                            {{ album.page_count }} 页
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">
                            <span class="emoji">📚</span> 章节数
                        </div>
                        <div class="info-value">
                            {{ album.episode_count }} 章
                        </div>
                    </div>
                </div>

                <div class="tags-section">
                    <div class="section-title">
                        <span class="emoji">🏷️</span> 标签
                    </div>
                    <div class="tags-container">
                        {% if album.tags %}
                        {% for tag in album.tags[:15] %}
                        <span class="tag">{{ tag }}</span>
                        {% endfor %}
                        {% if album.tags|length > 15 %}
                        <span class="tag">+{{ album.tags|length - 15 }}</span>
                        {% endif %}
                        {% else %}
                        <span class="tag">无标签</span>
                        {% endif %}
                    </div>
                </div>

                {% if album.works %}
                <div class="tags-section">
                    <div class="section-title">
                        <span class="emoji">🔗</span> 系列作品
                    </div>
                    <div class="tags-container">
                        {% for work in album.works %}
                        <span class="tag">{{ work }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</body>

</html>