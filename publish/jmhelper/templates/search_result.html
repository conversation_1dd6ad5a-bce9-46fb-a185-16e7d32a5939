<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索结果 - {{ search.query }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;600;700&display=swap');

        :root {
            --primary-color: #7659ff;
            --primary-light: #a29bfe;
            --primary-dark: #5e48e8;
            --secondary-color: #ff7eb3;
            --text-color: #2d3748;
            --text-light: #718096;
            --bg-color: #f7f9fc;
            --card-bg: rgba(255, 255, 255, 0.95);
            --card-border-radius: 12px;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            --tag-bg: rgba(118, 89, 255, 0.1);
            --tag-color: var(--primary-dark);
            --gradient-bg: linear-gradient(135deg, #7659ff 0%, #ff7eb3 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.5;
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* 搜索头部 */
        .search-header {
            display: flex;
            flex-direction: column;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: var(--card-border-radius);
            box-shadow: var(--shadow);
        }

        .search-title {
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 12px;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .search-keyword {
            color: var(--primary-color);
        }

        .search-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: var(--text-light);
        }

        .result-count {
            font-weight: 600;
            color: var(--primary-color);
        }

        .page-info {
            display: flex;
            align-items: center;
            gap: 8px;
            background-color: var(--tag-bg);
            padding: 6px 12px;
            border-radius: 8px;
            font-weight: 500;
        }

        /* 特色漫画部分 - 冠军台设计 - 修改成更长的条形 */
        .podium-section {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .podium-album {
            border-radius: var(--card-border-radius);
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            height: 420px;
        }

        .podium-album:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25);
            z-index: 10;
        }

        /* 冠军（第一名）- 中间大的 */
        .podium-first {
            width: 34%;
            height: 420px;
            z-index: 3;
        }

        /* 亚军（第二名）- 左边小的 */
        .podium-second {
            width: 31%;
            height: 390px;
            margin-top: 30px;
            z-index: 2;
        }

        /* 季军（第三名）- 右边小的 */
        .podium-third {
            width: 31%;
            height: 390px;
            margin-top: 30px;
            z-index: 1;
        }

        .podium-cover-wrap {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .podium-cover {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .podium-album:hover .podium-cover {
            transform: scale(1.05);
        }

        .podium-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.5) 50%, transparent 100%);
            padding: 60px 15px 15px;
            color: white;
        }

        .podium-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 16px;
            z-index: 5;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .badge-first {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: white;
        }

        .badge-second {
            background: linear-gradient(135deg, #C0C0C0 0%, #A9A9A9 100%);
            color: white;
        }

        .badge-third {
            background: linear-gradient(135deg, #CD7F32 0%, #8B4513 100%);
            color: white;
        }

        .podium-id {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: var(--primary-color);
            color: white;
            padding: 5px 10px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 2;
        }

        .podium-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .podium-first .podium-title {
            font-size: 18px;
        }

        .podium-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            font-size: 13px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 10px;
        }

        .podium-meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .podium-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .podium-tag {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 11px;
            backdrop-filter: blur(5px);
        }

        /* 列表部分 */
        .list-section {
            background-color: var(--card-bg);
            border-radius: var(--card-border-radius);
            padding: 20px;
            box-shadow: var(--shadow);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 8px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            padding-bottom: 12px;
        }

        .album-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .list-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-radius: 8px;
            background-color: rgba(0, 0, 0, 0.02);
            transition: background-color 0.2s;
        }

        .list-item:hover {
            background-color: rgba(118, 89, 255, 0.05);
        }

        .item-id {
            background-color: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            margin-right: 12px;
            min-width: 50px;
            text-align: center;
            flex-shrink: 0;
        }

        .item-info {
            flex: 1;
            min-width: 0;
            /* 确保弹性项目可以缩小到小于其内容大小 */
        }

        .item-title {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 4px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.3;
        }

        .item-meta {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: var(--text-light);
            flex-wrap: wrap;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
            white-space: nowrap;
        }

        /* 页脚 */
        .footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--card-bg);
            padding: 15px 20px;
            border-radius: var(--card-border-radius);
            box-shadow: var(--shadow);
        }

        .command-hint {
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .pagination {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .next-page {
            background-color: var(--primary-color);
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
        }

        .emoji {
            font-size: 16px;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .podium-section {
                flex-direction: column;
                gap: 15px;
                margin-bottom: 15px;
            }

            .podium-album {
                width: 100%;
                height: 320px;
                margin-top: 0;
            }

            .podium-first {
                height: 350px;
            }

            .podium-second,
            .podium-third {
                height: 300px;
            }

            .item-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 3px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 搜索头部 -->
        <div class="search-header">
            <div class="search-title">
                <span class="emoji">🔍</span>
                搜索: <span class="search-keyword">"{{ search.query }}"</span>
            </div>
            <div class="search-meta">
                <div>找到 <span class="result-count">{{ search.total }}</span> 个结果</div>
                <div class="page-info">
                    <span class="emoji">📄</span> 第 {{ search.page }} 页
                </div>
            </div>
        </div>

        {% if search.albums|length > 0 %}
        <!-- 特色漫画 - 冠军台设计 -->
        <div class="podium-section">
            {% if search.albums|length > 0 %}
            <!-- 冠军（第一名） -->
            <div class="podium-album podium-first">
                <div class="podium-badge badge-first">1</div>
                <div class="podium-id">#{{ search.albums[0].album_id }}</div>
                <div class="podium-cover-wrap">
                    <img class="podium-cover" src="{{ search.albums[0].cover }}" alt="{{ search.albums[0].name }} 封面">
                    <div class="podium-overlay">
                        <div class="podium-title">{{ search.albums[0].name }}</div>
                    </div>
                </div>
            </div>
            {% endif %}

            {% if search.albums|length > 1 %}
            <!-- 亚军（第二名） -->
            <div class="podium-album podium-second">
                <div class="podium-badge badge-second">2</div>
                <div class="podium-id">#{{ search.albums[1].album_id }}</div>
                <div class="podium-cover-wrap">
                    <img class="podium-cover" src="{{ search.albums[1].cover }}" alt="{{ search.albums[1].name }} 封面">
                    <div class="podium-overlay">
                        <div class="podium-title">{{ search.albums[1].name }}</div>
                    </div>
                </div>
            </div>
            {% endif %}

            {% if search.albums|length > 2 %}
            <!-- 季军（第三名） -->
            <div class="podium-album podium-third">
                <div class="podium-badge badge-third">3</div>
                <div class="podium-id">#{{ search.albums[2].album_id }}</div>
                <div class="podium-cover-wrap">
                    <img class="podium-cover" src="{{ search.albums[2].cover }}" alt="{{ search.albums[2].name }} 封面">
                    <div class="podium-overlay">
                        <div class="podium-title">{{ search.albums[2].name }}</div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- 列表部分 - 剩余结果 -->
        {% if search.albums|length > 3 %}
        <div class="list-section">
            <div class="section-title">
                <span class="emoji">📚</span> 更多结果
            </div>

            <div class="album-list">
                {% for album in search.albums[3:] %}
                <div class="list-item">
                    <div class="item-id">#{{ album.album_id }}</div>
                    <div class="item-info">
                        <div class="item-title">{{ album.name }}</div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        {% else %}
        <div
            style="text-align: center; padding: 40px; background-color: var(--card-bg); border-radius: var(--card-border-radius);">
            <div style="font-size: 24px; margin-bottom: 10px;">😔</div>
            <div style="font-size: 18px; font-weight: 600; color: var(--primary-color);">未找到结果</div>
            <div style="font-size: 14px; color: var(--text-light); margin-top: 10px;">请尝试其他关键词</div>
        </div>
        {% endif %}

        <!-- 页脚 -->
        <div class="footer">
            <div class="command-hint">
                <span class="emoji">💾</span> 发送 /jm [ID] 下载漫画
            </div>
            <div class="pagination">
                <div class="next-page">
                    <span class="emoji">➡️</span> /jms {{ search.query }} {{ search.page + 1 }}
                </div>
            </div>
        </div>
    </div>
</body>

</html>