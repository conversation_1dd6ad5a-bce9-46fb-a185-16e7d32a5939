[tool.poetry]
name = "summary-turbo"
version = "0.1.0"
description = ""
authors = ["X-Zero-L <<EMAIL>>"]
readme = "README.md"
packages = [{include = "summary_turbo"}]

[tool.poetry.dependencies]
python = "^3.11"
pydantic-ai = "^0.0.9"
nonebot-plugin-chatrecorder = "^0.6.2"
nonebot-plugin-send-anything-anywhere = "^0.7.1"
nonebot-plugin-alconna = "^0.54.0"
httpx = "^0.28.0"
nonebot-plugin-userinfo = "^0.2.6"
nonebot-plugin-session-orm = "^0.2.0"
nonebot-plugin-orm = "^0.7.6"
openai = "^1.57.0"
tzlocal = "^5.2"
beautifulsoup4 = "^4.12.3"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
