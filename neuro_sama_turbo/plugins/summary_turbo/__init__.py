import asyncio
import calendar
import datetime
import re

import logfire
from nonebot import logger, require
from nonebot.exception import FinishedException
from nonebot.plugin import PluginMetadata

require("nonebot_plugin_alconna")
require("nonebot_plugin_chatrecorder")
require("nonebot_plugin_userinfo")
require("nonebot_plugin_session")
require("nonebot_plugin_session_orm")
require("nonebot_plugin_orm")
require("nonebot_plugin_saa")
require("nonebot_plugin_apscheduler")

from nonebot.adapters import Bot, Event
from nonebot.params import Depends
from nonebot_plugin_alconna import (
    Alconna,
    Args,
    CustomNode,
    Match,
    MsgId,
    Option,
    Reference,
    Target,
    UniMessage,
    on_alconna,
    store_true,
)
from nonebot_plugin_alconna.builtins.extensions import ReplyRecordExtension
from nonebot_plugin_apscheduler import scheduler
from nonebot_plugin_chatrecorder import get_message_records
import nonebot_plugin_saa as saa
from nonebot_plugin_session import Session, extract_session

from ..locks import Locks
from .chat_summary import (
    build_forward_summary_list,
    build_summary_list,
    build_text_summary_list,
    get_forward_summary,
    get_summary,
    get_text_summary,
)
from .config import Config
from .config import config as plugin_config
from .core import (
    custom_analysis_messages,
    extract_onebot_forward_content,
    extract_plain_text_content,
    get_message_record_by_id,
    get_message_records_with_build_and_summary_batch_str,
    get_message_records_with_build_and_summary_batch_str_list,
    get_message_records_with_build_and_summary_forward,
    summarize_forward_message,
    summarize_onebot_forward_message,
    summarize_plain_text,
)
from .time import (
    get_datetime_now_with_timezone,
)

logfire.configure(send_to_logfire="if-token-present", scrubbing=False)

# 图片分析功能通过命令参数控制
logger.info(f"图片分析模型: {plugin_config.openai_vision_model}")

# 群组锁字典
locks: dict[str, asyncio.Lock] = {}

# 记录每个群组的未总结小时数
# 格式：{group_id: {“未总结小时数”: int, “上次总结时间”: datetime}}
unsummarized_hours: dict[str, dict] = {}

# 从配置中获取最小总结消息数量
MIN_MESSAGES_FOR_SUMMARY = plugin_config.min_messages_for_summary

# 从配置中获取最大未总结小时数
MAX_UNSUMMARIZED_HOURS = plugin_config.max_unsummarized_hours

# 从配置中获取群组总结配置
GROUP_CONFIGS = plugin_config.get_group_configs()


def get_lock(group_id: str) -> asyncio.Lock:
    """获取群组锁"""
    if group_id not in locks:
        locks[group_id] = asyncio.Lock()
    return locks[group_id]


async def hourly_summary_for_group(group_id: str):
    """每小时总结功能，带消息数量检查和时间范围扩展逻辑"""
    # 初始化未总结小时记录
    if group_id not in unsummarized_hours:
        unsummarized_hours[group_id] = {
            "未总结小时数": 0,
            "上次总结时间": get_datetime_now_with_timezone().replace(minute=0, second=0, microsecond=0)
            - datetime.timedelta(hours=1),
        }

    # 计算当前时间
    current_time = get_datetime_now_with_timezone().replace(minute=0, second=0, microsecond=0)

    # 计算时间范围
    time_stop = current_time

    # 如果有未总结的小时，则从上次总结时间开始
    if unsummarized_hours[group_id]["未总结小时数"] > 0:
        time_start = unsummarized_hours[group_id]["上次总结时间"]
        hours_diff = int((time_stop - time_start).total_seconds() / 3600)
        logger.info(
            f"群 {group_id} 有 {unsummarized_hours[group_id]['未总结小时数']} 小时未总结"
            f"时间范围扩展为 {hours_diff} 小时"
        )
    else:
        # 正常情况，只总结上一个小时
        time_start = time_stop - datetime.timedelta(hours=1)
        logger.info(f"开始生成群 {group_id} 的每小时总结，时间范围：{time_start} ~ {time_stop}")

    try:
        # 获取消息记录数量
        records = await get_message_records(
            scene_ids=[group_id],
            time_start=time_start,
            time_stop=time_stop,
        )
        message_count = len(records)
        logger.info(f"群 {group_id} 在 {time_start} ~ {time_stop} 时间范围内有 {message_count} 条消息")

        # 判断是否需要总结
        force_summary = unsummarized_hours[group_id]["未总结小时数"] >= MAX_UNSUMMARIZED_HOURS
        if message_count >= MIN_MESSAGES_FOR_SUMMARY or force_summary:
            # 消息数量足够或者强制总结，生成总结
            forward = await get_message_records_with_build_and_summary_forward([group_id], time_start, time_stop)

            # 添加额外信息到总结中
            summary_info = (
                f"时间范围：{time_start.strftime('%Y-%m-%d %H:%M')} ~ {time_stop.strftime('%Y-%m-%d %H:%M')}\n"
            )
            if force_summary:
                summary_info += f"强制总结：未总结小时数超过 {MAX_UNSUMMARIZED_HOURS} 小时\n"
            summary_info += f"消息数量：{message_count} 条"

            # 发送总结
            # await UniMessage(f"{summary_info}\n\n").send(target=Target(group_id))
            await UniMessage(forward).send(target=Target(group_id))
            logger.info(f"群 {group_id} 的总结已发送，消息数量：{message_count}")

            # 重置未总结小时数
            unsummarized_hours[group_id] = {
                "未总结小时数": 0,
                "上次总结时间": current_time,
            }
        else:
            # 消息数量不足，增加未总结小时数
            unsummarized_hours[group_id]["未总结小时数"] += 1
            logger.info(
                f"群 {group_id} 消息数量不足 {MIN_MESSAGES_FOR_SUMMARY} 条，不进行总结，当前未总结小时数："
                f"{unsummarized_hours[group_id]['未总结小时数']}"
            )
    except Exception as e:
        logger.error(f"群 {group_id} 的总结处理失败: {e}")


async def daily_summary_for_group(group_id: str):
    """每日总结功能（前一天凌晨到今天凌晨 4 点的总结）"""
    logger.info(f"开始生成前一天到今天凌晨 4 点的群聊记录总结，群聊 ID：{group_id}")

    # 计算时间范围：前一天的 0点 到 今天凌晨 4点
    dt = get_datetime_now_with_timezone()
    time_stop = dt.replace(hour=4, minute=0, second=0, microsecond=0)  # 今天凌晨 4点

    # 如果当前时间小于 4点，则使用前一天的 4点
    if dt.hour < 4:
        time_stop = time_stop - datetime.timedelta(days=1)

    time_start = time_stop - datetime.timedelta(days=1)  # 往前推 24 小时

    try:
        # 使用合并转发格式
        forward = await get_message_records_with_build_and_summary_forward([group_id], time_start, time_stop)
        await UniMessage(forward).send(target=Target(group_id))
        logger.info(f"群 {group_id} 的每日总结已发送")
    except Exception as e:
        logger.error(f"群 {group_id} 的每日总结发送失败: {e}")


async def run_hourly_summary():
    """执行每小时总结任务"""
    for group_config in GROUP_CONFIGS:
        # 只对启用了每小时总结的群组进行总结
        if group_config.hourly_summary:
            group_id = group_config.group_id
            async with get_lock(group_id):
                await hourly_summary_for_group(group_id)


async def run_daily_summary():
    """执行每日总结任务"""
    for group_config in GROUP_CONFIGS:
        # 只对启用了每日总结的群组进行总结
        if group_config.daily_summary:
            group_id = group_config.group_id
            async with get_lock(group_id):
                await daily_summary_for_group(group_id)


# 每小时整点执行总结
@scheduler.scheduled_job("cron", hour="*", minute="0", second="0")
async def hourly_summary_job():
    await run_hourly_summary()


# 每天早上 8 点执行前一天到今天凌晨 4 点的总结
@scheduler.scheduled_job("cron", hour="8", minute="0", second="0")
async def daily_summary_job():
    await run_daily_summary()


__plugin_meta = PluginMetadata(
    name="summary_turbo",
    description="智能聊天记录总结插件，支持图片分析和纯文本总结",
    usage="""
命令使用说明：
• /今日总结 [时间范围] - 生成今日聊天记录总结
• /昨日总结 [时间范围] - 生成昨日聊天记录总结  
• /月度总结 - 生成月度聊天记录总结
• /总结一下 [true/false] - 总结合并转发消息或纯文本
  - 回复转发消息时：
    · /总结一下 true  - 启用图片分析
    · /总结一下 false - 不分析图片（默认）
  - 回复纯文本消息时：
    · /总结一下 - 对文本内容进行智能总结
• /锐评一下 [分析指引] - 自定义分析从回复消息开始的所有后续消息
  - 回复某条消息后使用此命令
  - 可以指定分析方向，如：
    · /锐评一下 这个争论谁更有道理
    · /分析一下 这件事的来龙去脉
    · /点评一下 大家的观点和态度
  
示例：
• /总结一下 true  （回复转发消息，包含图片分析）
• /总结一下       （回复转发消息或纯文本，不分析图片）
• /锐评一下 这波操作如何 （回复某条消息，分析后续讨论）
""",
    config=Config,
)

daily_summary = on_alconna(
    Alconna(
        "今日总结",
        Args["time?", str],
    ),
    extensions=[ReplyRecordExtension()],
    use_cmd_start=True,
    priority=5,
    block=True,
    aliases={"每日总结"},
)


def get_group_lock(group_id: str, command_name: str = ""):
    return Locks.get_group_lock(group_id, __plugin_meta.name, command_name)


@daily_summary.handle()
async def _(
    time: Match[str],
    session: Session = Depends(extract_session),
):
    group_id = [session.id2]

    async with get_group_lock(session.id2, "今日总结"):
        dt = get_datetime_now_with_timezone()
        time_start = dt.replace(hour=0, minute=0, second=0, microsecond=0)
        time_stop = dt

        if time.available:
            time_ = time.result
            if match := re.match(r"^(\d{1,2})~(\d{1,2})$", time_):
                start = match[1]
                stop = match[2]
                time_start = time_start.replace(hour=int(start))
                time_stop = time_start.replace(hour=int(stop), minute=59, second=59)
        await saa.Text(f"正在为您总结{time_start}~{time_stop}的聊天记录，请稍等~").send()
        if plugin_config.aggregate_message:
            # 使用合并转发格式
            forward = await get_message_records_with_build_and_summary_forward(group_id, time_start, time_stop)
            await UniMessage(forward).finish()
        else:
            # 使用普通文本格式
            response = await get_message_records_with_build_and_summary_batch_str(group_id, time_start, time_stop)
            await saa.Text(response).finish(reply=True)


yesterday_summary = on_alconna(
    Alconna(
        "昨日总结",
        Args["time?", str],
    ),
    extensions=[ReplyRecordExtension()],
    use_cmd_start=True,
    priority=5,
    block=True,
    aliases={"昨日总结"},
)


@yesterday_summary.handle()
async def _(
    time: Match[str],
    session: Session = Depends(extract_session),
):
    group_id = [session.id2]
    async with get_group_lock(session.id2, "昨日总结"):
        dt = get_datetime_now_with_timezone()
        time_stop = dt.replace(hour=0, minute=0, second=0, microsecond=0)
        time_start = time_stop - datetime.timedelta(days=1)

        if time.available:
            time_ = time.result
            if match := re.match(r"^(\d{1,2})~(\d{1,2})$", time_):
                start = match[1]
                stop = match[2]
                time_start = time_start.replace(hour=int(start))
                time_stop = time_start.replace(hour=int(stop), minute=59, second=59)
        await saa.Text(f"正在为您总结{time_start}~{time_stop}的聊天记录，请稍等~").send()
        if plugin_config.aggregate_message:
            # 使用合并转发格式
            forward = await get_message_records_with_build_and_summary_forward(group_id, time_start, time_stop)
            await UniMessage(forward).finish()
        else:
            # 使用普通文本格式
            response = await get_message_records_with_build_and_summary_batch_str(group_id, time_start, time_stop)
            await saa.Text(response).finish(reply=True)


monthly_summary = on_alconna(
    Alconna(
        "月度总结",
        Args["month?", str],
    ),
    extensions=[ReplyRecordExtension()],
    use_cmd_start=True,
    priority=5,
    block=True,
    aliases={"本月总结", "上月总结"},
)


@monthly_summary.handle()
async def _(
    event: Event,
    session: Session = Depends(extract_session),
):
    group_id = [session.id2]
    async with get_group_lock(session.id2, "月度总结"):
        dt = get_datetime_now_with_timezone()

        # 处理上月/本月的逻辑
        if event.get_plaintext().startswith("上月"):
            if dt.month == 1:
                year = dt.year - 1
                month_num = 12
            else:
                year = dt.year
                month_num = dt.month - 1
        else:
            year = dt.year
            month_num = dt.month

        # 获取月份的第一天和最后一天
        _, last_day = calendar.monthrange(year, month_num)
        time_start = dt.replace(year=year, month=month_num, day=1, hour=0, minute=0, second=0, microsecond=0)
        time_stop = dt.replace(year=year, month=month_num, day=last_day, hour=23, minute=59, second=59)

        await saa.Text(
            f"正在为您总结{time_start.strftime('%Y年%m月')}的聊天记录，这可能需要一些时间，请耐心等待~"
        ).send()
        if plugin_config.aggregate_message:
            # 使用合并转发格式
            forward = await get_message_records_with_build_and_summary_forward(group_id, time_start, time_stop)
            await UniMessage(forward).finish()
        else:
            # 使用普通文本格式
            response_list = await get_message_records_with_build_and_summary_batch_str_list(
                group_id, time_start, time_stop
            )
            [await saa.Text(response).send() for response in response_list]


# 添加合并转发消息总结命令
forward_summary = on_alconna(
    Alconna(
        "总结一下",
        Option("--image|-i|image", action=store_true, default=False),
    ),
    extensions=[ReplyRecordExtension()],
    use_cmd_start=True,
    priority=5,
    block=True,
    aliases={"转发总结", "总结合并转发"},
)


@forward_summary.handle()
async def _(
    bot: Bot,
    event: Event,
    ext: ReplyRecordExtension,
    msg_id: MsgId,
    image: bool,
    session: Session = Depends(extract_session),
):
    """处理回复的合并转发消息或纯文本并进行总结"""
    async with get_group_lock(session.id2, "总结转发"):
        # 获取回复的消息
        reply = ext.get_reply(msg_id)
        if not reply:
            await forward_summary.finish(UniMessage("请回复一条消息进行总结").reply(id=msg_id))
        should_analyze_images = image
        # 判断是否需要分析图片
        logger.info(f"should_analyze_images: {should_analyze_images}")
        try:
            # 将回复消息转换为UniMessage
            uni_reply = await UniMessage.generate(message=reply.msg, event=event, bot=bot)

            # 检查是否包含Reference(合并转发)消息
            reference_found = False
            for segment in uni_reply:
                if isinstance(segment, Reference):
                    reference_found = True

                    # 使用原来的方式获取转发消息内容
                    forward: dict = await bot.get_forward_msg(id=segment.id)
                    logger.info(f"forward: {forward}")

                    # 使用新函数处理OneBot格式的数据，传入图片分析参数
                    content = await extract_onebot_forward_content(forward, should_analyze_images)

                    if not content.strip():
                        await forward_summary.finish(UniMessage("转发消息中没有找到可总结的内容").reply(id=msg_id))

                    logger.info(f"开始总结转发消息，内容长度: {len(content)} 字符，图片分析: {should_analyze_images}")

                    # 使用专门的转发消息agent进行总结
                    summary_result = await get_forward_summary(content)
                    str_list = await build_forward_summary_list(summary_result)

                    # 添加头部信息
                    message_count = len(forward.get("messages", []))
                    if should_analyze_images:
                        header = [f"📦 合并转发消息总结（共{message_count}条消息，包含图片分析）", ""]
                    else:
                        header = [f"📦 合并转发消息总结（共{message_count}条消息）", ""]
                    str_list = header + str_list

                    # 创建合并转发消息
                    summary_reference = Reference(
                        nodes=[
                            CustomNode(
                                uid="0",
                                content=UniMessage(
                                    "\n".join(
                                        [
                                            *str_list,
                                            "\nTips: 指令后带-i参数可以开启图片分析"
                                            if not should_analyze_images
                                            else "\nTips: 指令后不带-i参数可以关闭图片分析",
                                        ]
                                    )
                                ),
                                name="总结助手",
                            )
                        ]
                    )
                    await UniMessage(summary_reference).finish()

            # 如果没有找到转发消息，尝试提取纯文本内容
            if not reference_found:
                logger.info("没有找到转发消息，尝试提取纯文本内容")

                # 提取纯文本内容（包括可能的图片分析）
                text_content = await extract_plain_text_content(uni_reply, should_analyze_images)

                if not text_content.strip():
                    await forward_summary.finish(
                        UniMessage("回复的消息中没有找到可总结的内容（需要转发消息或文本内容）").reply(id=msg_id)
                    )

                # 检查文本长度，太短的话不值得总结
                if len(text_content.strip()) < 50 and not should_analyze_images:
                    await forward_summary.finish(UniMessage("文本内容太短，无需总结").reply(id=msg_id))

                logger.info(f"开始总结文本内容，长度: {len(text_content)} 字符，图片分析: {should_analyze_images}")

                # 对文本内容进行总结
                summary_reference = await summarize_plain_text(uni_reply, should_analyze_images)
                await UniMessage(summary_reference).finish()

        except FinishedException:
            pass
        except Exception as e:
            logger.error(f"处理消息总结时出错: {e}")
            await forward_summary.finish(UniMessage(f"处理失败: {e!s}").reply(id=msg_id))


# 添加自定义分析命令
custom_analysis = on_alconna(
    Alconna(
        "锐评一下",
        Args["instruction?", str],
    ),
    extensions=[ReplyRecordExtension()],
    use_cmd_start=True,
    priority=5,
    block=True,
    aliases={"分析一下", "点评一下", "评价一下"},
)


@custom_analysis.handle()
async def _(
    bot: Bot,
    event: Event,
    ext: ReplyRecordExtension,
    msg_id: MsgId,
    instruction: Match[str],
    session: Session = Depends(extract_session),
):
    """处理回复消息并进行自定义分析"""
    # async with get_group_lock(session.id2, "自定义分析"):
    # 获取回复的消息
    reply = ext.get_reply(msg_id)
    reply_time = None
    if not reply:
        # await custom_analysis.finish(UniMessage("请回复一条消息进行分析").reply(id=msg_id))
        # 24小时之内
        reply_time = get_datetime_now_with_timezone() - datetime.timedelta(hours=5)
        await saa.Text("由于您没有回复消息，将分析5小时内的消息").send()
    else:
        pass
    # 获取用户指引
    user_instruction = instruction.result if instruction.available else "请分析这些消息内容"

    try:
        if reply_time is None and reply.id:
            reply_record = await get_message_record_by_id(reply.id)
            if reply_record:
                reply_time = reply_record.time
                logger.info(f"通过消息ID {reply.id} 找到回复消息，时间: {reply_time}")

        # 如果直接查询失败，使用备用方案：通过内容匹配
        if not reply_time:
            logger.warning(f"无法通过消息ID {reply.id} 找到消息记录，尝试内容匹配")

            # 获取当前时间
            current_time = get_datetime_now_with_timezone()

            # 从回复消息获取纯文本内容用于匹配
            reply_text = ""
            if reply.msg:
                # 将回复消息转换为UniMessage获取纯文本
                uni_reply = await UniMessage.generate(message=reply.msg, event=event, bot=bot)
                reply_text = uni_reply.extract_plain_text().strip()

            # 如果没有文本内容，则从当前时间往前推1小时开始
            if not reply_text:
                logger.warning("无法获取回复消息的文本内容，将从1小时前开始分析")
                reply_time = current_time - datetime.timedelta(hours=1)
            else:
                # 查询最近24小时的消息记录来找到匹配的消息
                search_start = current_time - datetime.timedelta(hours=24)
                recent_records = await get_message_records(
                    scene_ids=[session.id2],
                    time_start=search_start,
                    time_stop=current_time,
                )

                # 查找匹配的消息记录
                reply_time = current_time - datetime.timedelta(hours=1)  # 默认值
                for record in recent_records:
                    if record.plain_text.strip() == reply_text:
                        reply_time = record.time
                        logger.info(f"通过内容匹配找到回复消息，时间: {reply_time}")
                        break
                else:
                    logger.warning(f"未找到匹配的回复消息，消息内容: {reply_text[:50]}...")

        # 获取当前时间作为结束时间
        current_time = get_datetime_now_with_timezone()

        # 从回复消息的时间开始到现在的所有消息
        group_id = [session.id2]

        # await saa.Text(
        #    f"正在分析从 {reply_time.strftime('%Y-%m-%d %H:%M:%S')} 开始的消息，分析方向：{user_instruction}"
        # ).send()

        # +8个小时再显示
        await saa.Text(
            f"正在分析从 {reply_time.strftime('%Y-%m-%d %H:%M:%S')} 开始的消息，分析方向：{user_instruction}"
        ).send()

        # 调用自定义分析函数
        result = await custom_analysis_messages(group_id, reply_time, current_time, user_instruction)

        await UniMessage(result).finish()

    except FinishedException:
        pass
    except Exception as e:
        logger.error(f"自定义分析时出错: {e}")
        await custom_analysis.finish(UniMessage(f"分析失败: {e!s}").reply(id=msg_id))
