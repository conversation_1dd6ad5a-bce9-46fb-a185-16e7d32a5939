import asyncio
from datetime import datetime

import logfire
from nonebot import logger
from nonebot_plugin_alconna import CustomNode, Reference, UniMessage
from nonebot_plugin_chatrecorder import get_message_records
from nonebot_plugin_chatrecorder.model import MessageRecord
from nonebot_plugin_orm import get_session
from nonebot_plugin_uninfo.orm import get_session_model
from sqlalchemy import select
from tzlocal import get_localzone

from .chat_summary import (
    ChatTopicList,
    analyze_image,
    build_custom_analysis_list,
    build_summary_list,
    build_summary_str,
    build_text_summary_list,
    get_custom_analysis,
    get_summary,
    get_summary_str,
    get_text_summary,
)

SEMAPHORE = asyncio.Semaphore(10)


async def build_records(records: list[MessageRecord]) -> str:
    s = ""
    local_tz = get_localzone()
    local_now = datetime.now(local_tz)
    offset = local_now.utcoffset()  # Returns a timedelta

    for i in records:
        try:
            smodel = await get_session_model(i.session_persist_id)
            session = await smodel.to_session()
            name = session.user.name if session.user and session.user.name else "未知用户"
            user_id = session.user.id if session.user else "未知ID"
            msg = i.plain_text
            s += f'"{name}({user_id})"在{(i.time + offset).replace(tzinfo=local_tz).strftime("%Y-%m-%d %H:%M:%S")}说:{msg}\n'  # type: ignore
        except Exception as e:
            logger.error(f"处理消息记录时出错: {e}")
            continue

    s += "\n\n现在的时间是" + datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return s


async def get_message_records_with_build(scene_ids, time_start, time_stop) -> str:
    records = await get_message_records(
        scene_ids=scene_ids,
        time_start=time_start,
        time_stop=time_stop,
    )
    return await build_records(records)


async def get_message_records_with_build_and_summary(scene_ids, time_start, time_stop) -> str:
    return await get_summary_str(await get_message_records_with_build(scene_ids, time_start, time_stop))


async def build_records_batch(records: list[MessageRecord]) -> list[str]:
    s = []
    local_tz = get_localzone()
    local_now = datetime.now(local_tz)
    offset = local_now.utcoffset()  # Returns a timedelta
    logger.info(f"共有{len(records)}条记录")
    batch_size = 1000

    for i in range(0, len(records), batch_size):
        batch = records[i : i + batch_size]
        s.append("")
        for j in batch:
            try:
                smodel = await get_session_model(j.session_persist_id)
                session = await smodel.to_session()
                name = session.user.name if session.user and session.user.name else "未知用户"
                user_id = session.user.id if session.user else "未知ID"
                msg = j.plain_text
                s[-1] += (
                    f'"{name}({user_id})"在{(j.time + offset).replace(tzinfo=local_tz).strftime("%Y-%m-%d %H:%M:%S")}说:{msg}\n' # type: ignore
                )
            except Exception as e:
                logger.error(f"处理消息记录时出错: {e}")
                continue
    return s


async def get_message_records_with_build_batch(scene_ids, time_start, time_stop) -> list[str]:
    records = await get_message_records(
        scene_ids=scene_ids,
        time_start=time_start,
        time_stop=time_stop,
    )
    logger.info(f"共有{len(records)}条记录")
    return await build_records_batch(records)


async def get_message_records_with_build_and_summary_batch(scene_ids, time_start, time_stop) -> list[ChatTopicList]:
    record_batches = await get_message_records_with_build_batch(scene_ids, time_start, time_stop)

    async def process_batch(batch: str) -> ChatTopicList:
        async with SEMAPHORE:
            logfire.info(f"开始生成群聊记录总结，群聊记录：{batch}")
            return await get_summary(batch)

    tasks = [process_batch(batch) for batch in record_batches]
    return await asyncio.gather(*tasks)


async def get_message_records_with_build_and_summary_batch_str(scene_ids, time_start, time_stop) -> str:
    data = await get_message_records_with_build_and_summary_batch(scene_ids, time_start, time_stop)
    chat_summary = []
    summary = ""
    for i in data:
        chat_summary.extend(i.chat_summary)
        summary = i.summary
    result = await build_summary_str(ChatTopicList(chat_summary=chat_summary, summary=summary))
    result = (
        f"以下是{time_start.strftime('%Y-%m-%d %H:%M:%S')}~{time_stop.strftime('%Y-%m-%d %H:%M:%S')}的群聊记录总结\n"
        + result
    )
    return result


async def get_message_records_with_build_and_summary_batch_str_list(scene_ids, time_start, time_stop) -> list[str]:
    data = await get_message_records_with_build_and_summary_batch(scene_ids, time_start, time_stop)
    chat_summary = []
    summary = ""
    for i in data:
        chat_summary.extend(i.chat_summary)
        summary = i.summary
    str_list = await build_summary_list(ChatTopicList(chat_summary=chat_summary, summary=summary))
    str_list = [
        f"以下是{time_start.strftime('%Y-%m-%d %H:%M:%S')}~{time_stop.strftime('%Y-%m-%d %H:%M:%S')}的群聊记录总结",
        *[s.strip() for s in str_list],
    ]
    batch_size = 50
    return ["\n".join(str_list[i : i + batch_size]) for i in range(0, len(str_list), batch_size)]


async def get_message_records_with_build_and_summary_forward(scene_ids, time_start, time_stop) -> Reference:
    """生成合并转发格式的总结"""
    data = await get_message_records_with_build_and_summary_batch(scene_ids, time_start, time_stop)
    chat_summary = []
    summary = ""
    for i in data:
        chat_summary.extend(i.chat_summary)
        summary = i.summary
    str_list = await build_summary_list(ChatTopicList(chat_summary=chat_summary, summary=summary))
    # 添加标题
    str_list = [
        f"以下是{time_start.strftime('%Y-%m-%d %H:%M:%S')}~{time_stop.strftime('%Y-%m-%d %H:%M:%S')}的群聊记录总结",
        *str_list,
    ]
    # 创建合并转发消息
    return Reference(nodes=[CustomNode(uid="0", content=UniMessage(text), name="群聊记录总结") for text in str_list])


async def extract_forward_content(reference: Reference) -> str:
    """从合并转发消息中提取文本内容，保持嵌套结构"""
    content_list = []
    local_tz = get_localzone()

    def extract_text_from_content(content, depth=0) -> list[str]:
        """递归提取内容中的文本，保持层级"""
        texts = []
        indent = "  " * depth  # 用缩进表示层级

        logger.info(f"提取内容: {content}, 类型: {type(content)}")

        if isinstance(content, str):
            if content.strip():
                texts.append(f"{indent}{content.strip()}")
        elif isinstance(content, UniMessage):
            for segment in content:
                if hasattr(segment, "text") and segment.text:
                    texts.append(f"{indent}{segment.text.strip()}")
                elif isinstance(segment, Reference):
                    # 递归处理嵌套的Reference
                    for sub_node in segment.children:
                        if isinstance(sub_node, CustomNode):
                            texts.append(
                                f"{indent}├─ {sub_node.name} ({sub_node.time.astimezone(local_tz).strftime('%m-%d %H:%M')}):"
                            )
                            texts.extend(extract_text_from_content(sub_node.content, depth + 1))
                elif hasattr(segment, "extract_plain_text"):
                    plain_text = segment.extract_plain_text().strip()
                    if plain_text:
                        texts.append(f"{indent}{plain_text}")
        elif isinstance(content, list):
            # 处理消息段列表
            for item in content:
                if hasattr(item, "text") and item.text:
                    texts.append(f"{indent}{item.text.strip()}")
                elif hasattr(item, "extract_plain_text"):
                    plain_text = item.extract_plain_text().strip()
                    if plain_text:
                        texts.append(f"{indent}{plain_text}")
                elif isinstance(item, Reference):
                    # 递归处理嵌套的Reference
                    for sub_node in item.children:
                        if isinstance(sub_node, CustomNode):
                            texts.append(
                                f"{indent}├─ {sub_node.name} ({sub_node.time.astimezone(local_tz).strftime('%m-%d %H:%M')}):"
                            )
                            texts.extend(extract_text_from_content(sub_node.content, depth + 1))

        return texts

    logger.info(f"reference: {reference}, 类型: {type(reference)}")
    # 输出dump
    logger.info(f"reference.dump: {reference.dump()}")
    # 输出nodes
    logger.info(f"reference.nodes: {reference.nodes}")
    for node in reference.children:
        logger.info(f"提取节点: {node}, 类型: {type(node)}")
        if isinstance(node, CustomNode):
            # 添加节点头部信息
            node_header = f'"{node.name}"在{node.time.astimezone(local_tz).strftime("%Y-%m-%d %H:%M:%S")}说:'
            content_list.append(node_header)

            # 提取节点内容，保持嵌套结构
            extracted_texts = extract_text_from_content(node.content, depth=1)
            content_list.extend(extracted_texts)

            # 添加分隔符
            content_list.append("")

    result = "\n".join(content_list)
    result += f"\n\n现在的时间是{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    return result


async def summarize_forward_message(reference: Reference) -> Reference:
    """对合并转发消息进行总结并返回合并转发格式的结果"""
    # 提取转发消息内容
    content = await extract_forward_content(reference)

    if not content.strip():
        return Reference(nodes=[CustomNode(uid="0", content=UniMessage("没有找到可总结的内容"), name="总结助手")])

    logger.info(f"开始总结合并转发消息，内容长度: {len(content)} 字符")
    logger.debug(f"提取的内容预览: {content[:500]}...")

    try:
        # 调用AI进行总结
        summary_result = await get_summary(content)
        str_list = await build_summary_list(summary_result)

        # 添加标题和统计信息
        node_count = len(reference.children)
        str_list = [
            f"以下是合并转发消息的总结（共{node_count}个消息节点）",
            "=" * 50,
            *str_list,
        ]

        # 创建合并转发消息
        return Reference(nodes=[CustomNode(uid="0", content=UniMessage(text), name="总结助手") for text in str_list])

    except Exception as e:
        logger.error(f"总结合并转发消息时出错: {e}")
        error_msg = f"总结失败: {str(e)[:100]}..."  # 限制错误信息长度
        return Reference(nodes=[CustomNode(uid="0", content=UniMessage(error_msg), name="总结助手")])


async def extract_onebot_forward_content(forward_data: dict, analyze_images: bool = False) -> str:
    """从OneBot协议的转发消息数据中提取文本内容，支持并发图片分析"""
    if "messages" not in forward_data:
        return "没有找到转发消息内容"

    messages = forward_data["messages"]
    logger.info(f"找到 {len(messages)} 条转发消息")

    if not analyze_images:
        # 如果不需要分析图片，使用简化版本
        return await _extract_content_without_image_analysis(messages)

    # 第一阶段：递归收集所有图片URL
    all_image_urls = []
    await _collect_all_image_urls(messages, all_image_urls)

    # 第二阶段：并发分析所有图片
    image_analysis_results = {}
    if all_image_urls:
        logger.info(f"开始并发分析 {len(all_image_urls)} 张图片")
        try:
            image_tasks = [analyze_image(url) for url in all_image_urls]
            results = await asyncio.gather(*image_tasks, return_exceptions=True)

            for url, result in zip(all_image_urls, results):
                if isinstance(result, Exception):
                    logger.error(f"图片分析失败: {result}")
                    image_analysis_results[url] = "[图片]"
                else:
                    # 构建图片描述文本
                    image_desc = f"[图片: {result.description}"
                    if result.text_content and result.text_content != "无文字内容":
                        image_desc += f", 图片文字: {result.text_content}"
                    if result.key_elements:
                        image_desc += f", 类型: {'/'.join(result.key_elements)}"
                    image_desc += "]"
                    image_analysis_results[url] = image_desc

            logger.info(f"完成 {len(all_image_urls)} 张图片的并发分析")
        except Exception as e:
            logger.error(f"并发图片分析出错: {e}")
            # 如果并发分析失败，将所有图片标记为普通图片
            for url in all_image_urls:
                image_analysis_results[url] = "[图片]"

    # 第三阶段：生成最终内容
    content_list = await _build_content_with_image_results(messages, image_analysis_results)

    result = "\n".join(content_list)
    result += f"\n\n现在的时间是{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    return result


async def _extract_content_without_image_analysis(messages: list) -> str:
    """提取内容但不分析图片的简化版本"""
    content_list = []
    local_tz = get_localzone()

    for msg in messages:
        try:
            # 获取用户信息
            sender = msg.get("sender", {})
            nickname = sender.get("card") or sender.get("nickname", "未知用户")
            user_id = sender.get("user_id", "未知ID")

            # 获取时间
            timestamp = msg.get("time", 0)
            msg_time = datetime.fromtimestamp(timestamp, tz=local_tz).strftime("%Y-%m-%d %H:%M:%S")

            # 提取消息内容
            text_parts = await _extract_message_segments_without_images(msg.get("message", []))

            if text_parts:
                full_message = "".join(text_parts)
                content_list.append(f'"{nickname}({user_id})"在{msg_time}说:{full_message}')

        except Exception as e:
            logger.error(f"解析转发消息时出错: {e}")
            continue

    result = "\n".join(content_list)
    result += f"\n\n现在的时间是{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    return result


async def _extract_message_segments_without_images(message_segments: list, depth: int = 0) -> list[str]:
    """递归提取消息段落，不分析图片"""
    text_parts = []
    indent = "  " * depth

    for segment in message_segments:
        if segment.get("type") == "text":
            text_content = segment.get("data", {}).get("text", "").strip()
            if text_content:
                text_parts.append(text_content)
        elif segment.get("type") == "image":
            text_parts.append("[图片]")
        elif segment.get("type") == "at":
            qq_num = segment.get("data", {}).get("qq", "")
            text_parts.append(f"[提及:{qq_num}]")
        elif segment.get("type") == "forward":
            # 嵌套转发消息
            text_parts.append(f"\n{indent}[嵌套转发消息]")
            nested_content = segment.get("data", {}).get("content", [])
            if nested_content:
                # 递归处理嵌套转发
                nested_parts = []
                for nested_msg in nested_content:
                    nested_segments = await _extract_message_segments_without_images(
                        nested_msg.get("message", []), depth + 1
                    )
                    if nested_segments:
                        # 获取嵌套消息的发送者信息
                        nested_sender = nested_msg.get("sender", {})
                        nested_nickname = nested_sender.get("card") or nested_sender.get("nickname", "未知用户")
                        nested_user_id = nested_sender.get("user_id", "未知ID")
                        nested_timestamp = nested_msg.get("time", 0)
                        local_tz = get_localzone()
                        nested_time = datetime.fromtimestamp(nested_timestamp, tz=local_tz).strftime(
                            "%Y-%m-%d %H:%M:%S"
                        )

                        nested_content_str = "".join(nested_segments)
                        nested_parts.append(
                            f'{indent}  "{nested_nickname}({nested_user_id})"在{nested_time}说:{nested_content_str}'
                        )

                if nested_parts:
                    text_parts.append("\n" + "\n".join(nested_parts))

    return text_parts


async def _collect_all_image_urls(messages: list, image_urls: list) -> None:
    """递归收集所有图片URL"""
    for msg in messages:
        try:
            message_segments = msg.get("message", [])
            for segment in message_segments:
                if segment.get("type") == "image":
                    image_url = segment.get("data", {}).get("url", "")
                    if image_url:
                        # 标准化URL
                        image_url = image_url.replace("https://", "http://")
                        if image_url not in image_urls:
                            image_urls.append(image_url)
                elif segment.get("type") == "forward":
                    # 递归处理嵌套转发
                    nested_content = segment.get("data", {}).get("content", [])
                    if nested_content:
                        await _collect_all_image_urls(nested_content, image_urls)
        except Exception as e:
            logger.error(f"收集图片URL时出错: {e}")
            continue


async def _build_content_with_image_results(messages: list, image_results: dict, depth: int = 0) -> list[str]:
    """使用图片分析结果构建最终内容"""
    content_list = []
    local_tz = get_localzone()
    indent = "  " * depth

    for msg in messages:
        try:
            # 获取用户信息
            sender = msg.get("sender", {})
            nickname = sender.get("card") or sender.get("nickname", "未知用户")
            user_id = sender.get("user_id", "未知ID")

            # 获取时间
            timestamp = msg.get("time", 0)
            msg_time = datetime.fromtimestamp(timestamp, tz=local_tz).strftime("%Y-%m-%d %H:%M:%S")

            # 提取消息内容
            text_parts = []
            message_segments = msg.get("message", [])

            for segment in message_segments:
                if segment.get("type") == "text":
                    text_content = segment.get("data", {}).get("text", "").strip()
                    if text_content:
                        text_parts.append(text_content)
                elif segment.get("type") == "image":
                    image_url = segment.get("data", {}).get("url", "")
                    if image_url:
                        # 标准化URL
                        image_url = image_url.replace("https://", "http://")
                        # 使用预先分析好的结果
                        image_desc = image_results.get(image_url, "[图片]")
                        text_parts.append(image_desc)
                    else:
                        text_parts.append("[图片]")
                elif segment.get("type") == "at":
                    qq_num = segment.get("data", {}).get("qq", "")
                    text_parts.append(f"[提及:{qq_num}]")
                elif segment.get("type") == "forward":
                    # 嵌套转发消息
                    text_parts.append(f"\n{indent}[嵌套转发消息]")
                    nested_content = segment.get("data", {}).get("content", [])
                    if nested_content:
                        # 递归处理嵌套转发
                        nested_content_list = await _build_content_with_image_results(
                            nested_content, image_results, depth + 1
                        )
                        if nested_content_list:
                            # 为嵌套内容添加缩进
                            indented_nested = "\n".join(f"{indent}  {line}" for line in nested_content_list)
                            text_parts.append(f"\n{indented_nested}")

            if text_parts:
                full_message = "".join(text_parts)
                if depth > 0:
                    # 嵌套消息不需要完整的前缀
                    content_list.append(f"{full_message}")
                else:
                    content_list.append(f'"{nickname}({user_id})"在{msg_time}说:{full_message}')

        except Exception as e:
            logger.error(f"构建消息内容时出错: {e}")
            continue

    return content_list


async def get_forward_message_content(bot, forward_id: str) -> dict:
    """通过bot API获取转发消息的完整内容"""
    try:
        # 调用OneBot API获取转发消息内容
        forward_data = await bot.call_api("get_forward_msg", msg_id=forward_id)
        return forward_data
    except Exception as e:
        logger.error(f"获取转发消息内容失败: {e}")
        return {}


async def summarize_onebot_forward_message(bot, forward_id: str) -> Reference:
    """对OneBot转发消息进行总结并返回合并转发格式的结果"""
    try:
        # 获取转发消息的完整内容
        forward_data = await get_forward_message_content(bot, forward_id)

        if not forward_data:
            return Reference(nodes=[CustomNode(uid="0", content=UniMessage("无法获取转发消息内容"), name="总结助手")])

        # 提取转发消息内容
        content = await extract_onebot_forward_content(forward_data)

        if not content.strip():
            return Reference(
                nodes=[CustomNode(uid="0", content=UniMessage("转发消息中没有找到可总结的内容"), name="总结助手")]
            )

        logger.info(f"开始总结OneBot转发消息，内容长度: {len(content)} 字符")
        logger.debug(f"提取的内容预览: {content[:500]}...")

        # 调用AI进行总结
        summary_result = await get_summary(content)
        str_list = await build_summary_list(summary_result)

        # 添加标题和统计信息
        message_count = len(forward_data.get("messages", []))
        str_list = [
            f"以下是合并转发消息的总结（共{message_count}条消息）",
            "=" * 50,
            *str_list,
        ]

        # 创建合并转发消息
        return Reference(nodes=[CustomNode(uid="0", content=UniMessage("".join(str_list)), name="总结助手")])

    except Exception as e:
        logger.error(f"总结OneBot转发消息时出错: {e}")
        error_msg = f"总结失败: {str(e)[:100]}..."
        return Reference(nodes=[CustomNode(uid="0", content=UniMessage(error_msg), name="总结助手")])


async def extract_plain_text_content(uni_message: UniMessage, analyze_images: bool = False) -> str:
    """从UniMessage中提取纯文本内容，支持图片分析"""
    text_parts = []
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # 如果需要分析图片，先收集所有图片URL
    image_urls = []
    if analyze_images:
        for segment in uni_message:
            if hasattr(segment, "url") and segment.url:
                # 处理图片段
                image_url = segment.url.replace("https://", "http://")
                if image_url not in image_urls:
                    image_urls.append(image_url)
    # 如果没有纯文本，则必须分析
    plain_text = uni_message.extract_plain_text()
    if not plain_text:
        analyze_images = True
    # 批量分析图片
    image_analysis_results = {}
    if analyze_images and image_urls:
        logger.info(f"开始分析 {len(image_urls)} 张图片")
        try:
            # 导入图片分析函数
            from .chat_summary import analyze_image

            image_tasks = [analyze_image(url) for url in image_urls]
            results = await asyncio.gather(*image_tasks, return_exceptions=True)

            for url, result in zip(image_urls, results):
                if isinstance(result, Exception):
                    logger.error(f"图片分析失败: {result}")
                    image_analysis_results[url] = "[图片]"
                else:
                    # 构建图片描述文本
                    image_desc = f"[图片: {result.description}"
                    if result.text_content and result.text_content != "无文字内容":
                        image_desc += f", 图片文字: {result.text_content}"
                    if result.key_elements:
                        image_desc += f", 类型: {'/'.join(result.key_elements)}"
                    image_desc += "]"
                    image_analysis_results[url] = image_desc

        except Exception as e:
            logger.error(f"批量图片分析出错: {e}")
            # 如果分析失败，将所有图片标记为普通图片
            for url in image_urls:
                image_analysis_results[url] = "[图片]"

    # 提取文本和图片内容
    for segment in uni_message:
        if hasattr(segment, "text") and segment.text:
            text_parts.append(segment.text.strip())
        elif hasattr(segment, "url") and segment.url:
            # 处理图片段
            image_url = segment.url.replace("https://", "http://")
            if analyze_images and image_url in image_analysis_results:
                text_parts.append(image_analysis_results[image_url])
            else:
                text_parts.append("[图片]")
        elif hasattr(segment, "extract_plain_text"):
            plain_text = segment.extract_plain_text().strip()
            if plain_text:
                text_parts.append(plain_text)

    content = "\n".join(text_parts)
    if content.strip():
        content += f"\n\n（总结时间：{current_time}）"

    return content


async def summarize_plain_text(uni_message: UniMessage, analyze_images: bool = False) -> Reference:
    """对纯文本内容进行总结，返回合并转发格式"""
    text_content = await extract_plain_text_content(uni_message, analyze_images)

    if not text_content.strip():
        return Reference(nodes=[CustomNode(uid="0", content=UniMessage("没有找到可总结的文本内容"), name="总结助手")])

    # 调用文本总结
    try:
        text_summary = await get_text_summary(text_content)
        str_list = await build_text_summary_list(text_summary)

        # 添加头部信息
        if analyze_images:
            header = ["📄 文本内容总结（包含图片分析）", ""]
        else:
            header = ["📄 文本内容总结", ""]
        str_list = header + str_list

        # 创建合并转发消息
        return Reference(
            nodes=[
                CustomNode(
                    uid="0",
                    content=UniMessage(
                        "\n".join(
                            [
                                *str_list,
                                "\nTips: 指令后带-i参数可以开启图片分析"
                                if not analyze_images
                                else "\nTips: 指令后不带-i参数可以关闭图片分析",
                            ]
                        )
                    ),
                    name="总结助手",
                )
            ]
        )
    except Exception as e:
        logger.error(f"文本总结失败: {e}")
        return Reference(nodes=[CustomNode(uid="0", content=UniMessage(f"文本总结失败: {e!s}"), name="总结助手")])


async def custom_analysis_messages(scene_ids: list[str], time_start, time_stop, user_instruction: str) -> Reference:
    """对指定时间范围内的消息进行自定义分析"""
    try:
        # 获取消息记录
        records = await get_message_records(
            scene_ids=scene_ids,
            time_start=time_start,
            time_stop=time_stop,
        )

        if not records:
            return Reference(
                nodes=[CustomNode(uid="0", content=UniMessage("在指定时间范围内没有找到消息记录"), name="分析助手")]
            )

        # 构建消息内容
        message_content = await build_records(records)

        logger.info(f"开始自定义分析，消息数量: {len(records)}, 指引: {user_instruction}")

        # 调用自定义分析
        analysis_result = await get_custom_analysis(message_content, user_instruction)
        str_list = await build_custom_analysis_list(analysis_result)

        # 添加头部信息
        header = [
            f"🔍 自定义分析结果（共{len(records)}条消息）",
            f"📅 时间范围：{time_start.strftime('%Y-%m-%d %H:%M:%S')} ~ {time_stop.strftime('%Y-%m-%d %H:%M:%S')}",
            f"🎯 分析方向：{user_instruction}",
            "",
        ]
        str_list = header + str_list

        # 创建合并转发消息
        return Reference(nodes=[CustomNode(uid="0", content=UniMessage("\n".join(str_list)), name="分析助手")])

    except Exception as e:
        logger.error(f"自定义分析失败: {e}")
        return Reference(nodes=[CustomNode(uid="0", content=UniMessage(f"分析失败: {e!s}"), name="分析助手")])


async def get_message_record_by_id(message_id: str) -> MessageRecord | None:
    """通过消息ID查询单个消息记录"""
    try:
        async with get_session() as db_session:
            statement = select(MessageRecord).where(MessageRecord.message_id == message_id)
            result = await db_session.scalar(statement)
            return result
    except Exception as e:
        logger.error(f"查询消息记录失败: {e}")
        return None
