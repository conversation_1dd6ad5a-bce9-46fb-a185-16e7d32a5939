from datetime import datetime
from zoneinfo import ZoneInfo

from .config import config as plugin_config


def get_datetime_fromisoformat_with_timezone(date_string: str) -> datetime:
    if not plugin_config.timezone:
        return datetime.fromisoformat(date_string).astimezone()
    raw = datetime.fromisoformat(date_string)
    return (
        raw.astimezone(ZoneInfo(plugin_config.timezone))
        if raw.tzinfo
        else raw.replace(tzinfo=ZoneInfo(plugin_config.timezone))
    )


def get_datetime_now_with_timezone() -> datetime:
    if plugin_config.timezone:
        return datetime.now(ZoneInfo(plugin_config.timezone))
    else:
        return datetime.now().astimezone()


def get_datetime_from_timestamp_with_timezone(timestamp: int) -> datetime:
    if plugin_config.timezone:
        return datetime.fromtimestamp(timestamp, ZoneInfo(plugin_config.timezone))
    else:
        return datetime.fromtimestamp(timestamp).astimezone()
