from httpx import AsyncClient
from nonebot import logger
from pydantic import BaseModel, Field
from pydantic_ai import Agent
from pydantic_ai.messages import BinaryContent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider

from .client import openai_client
from .config import config

provider = OpenAIProvider(
    openai_client=openai_client,
)
model = OpenAIModel(config.openai_model, provider=provider)
# google_model = "google_gla:gemini-2.5-pro-preview-06-05"
# http_client = AsyncClient(proxy="http://127.0.0.1:7899")
# google_model = GeminiModel(
#     model_name="gemini-2.5-flash-preview-05-20",
#     provider=GoogleGLAProvider(http_client=http_client, api_key="AIzaSyCRCmm8JWuPL3x9GLSfLy-z-e_yhqRu6qo"),
# )
# model = google_model
# 专门用于图片识别的模型
vision_model = OpenAIModel(config.openai_vision_model, provider=provider)


class ChatTopic(BaseModel):
    topic: str = Field(..., description="话题主题")
    content: str = Field(..., description="话题内容")
    time_start: str = Field(..., description="话题讨论的开始时间")
    time_end: str = Field(..., description="话题讨论的结束时间")
    participants: list[str] = Field(..., description="参与者列表")


class ChatTopicList(BaseModel):
    chat_summary: list[ChatTopic] = Field(..., description="群聊记录的总结列表")
    summary: str = Field(..., description="总结")

    @classmethod
    def merge(cls, topic_lists: list["ChatTopicList"]) -> "ChatTopicList":
        all_topics = []
        summaries = []

        for topic_list in topic_lists:
            all_topics.extend(topic_list.chat_summary)
            if topic_list.summary and topic_list.summary != "出现错误，无法生成总结":
                summaries.append(topic_list.summary)

        all_topics.sort(key=lambda x: x.time_start)

        final_summary = "这些聊天记录令人忍俊不禁，" + "；".join(summaries).replace("这些聊天记录令人忍俊不禁，", "")

        return cls(chat_summary=all_topics, summary=final_summary)


agent = Agent(model, result_type=ChatTopicList, retries=5)


@agent.system_prompt
async def system_prompt():
    return """
你是一个QQ群聊天记录总结助手，你的任务是从用户提供的聊天记录中提取并总结出多个话题的信息，包含话题的主题、内容、开始时间、结束时间和参与者。
最后给出一句话总结，固定句式：这些聊天记录令人忍俊不禁，xxx。
"""


async def get_summary(chat_records_str: str, retries: int = 5) -> ChatTopicList:
    try:
        return (await agent.run(chat_records_str)).data
    except Exception as e:
        logger.error(f"Error in get_summary: {e}")
        if retries > 0:
            return await get_summary(chat_records_str, retries - 1)
        return ChatTopicList(chat_summary=[], summary="出现错误，无法生成总结")


async def get_summary_str(chat_records_str: str) -> str:
    data = await get_summary(chat_records_str)
    return await build_summary_str(data)


async def build_summary_str(chat_topic_list: ChatTopicList) -> str:
    return "\n".join(await build_summary_list(chat_topic_list))


async def build_summary_list(chat_topic_list: ChatTopicList) -> list[str]:
    s = []
    for i, topic in enumerate(chat_topic_list.chat_summary):
        s.append(
            f"""
{i + 1}. 【{topic.topic}】 在 {topic.time_start} 到 {topic.time_end} 期间：{topic.content}
参与者：{", ".join(topic.participants)}
""".strip()
        )
    s.append(chat_topic_list.summary)
    return s


class ForwardMessageSummary(BaseModel):
    """转发消息总结模型 - 网感版"""

    title: str = Field(..., description="有梗感的标题，可以用网络流行语，如'翻车现场'、'新瓜出炉'等")
    brief_summary: str = Field(
        ..., description="30-50字的网感总结，要用网络流行语，如'破防了'、'整活'、'翻车'等，一句话说清楚"
    )
    detailed_summary: str = Field(..., description="详细说明，语言轻松有趣但信息完整，可以用网络词汇")
    key_points: list[str] = Field(..., description="3-5个爆点，用网络化表达，突出有趣的地方")
    participants: list[str] = Field(..., description="主要人物，可以用有趣的称呼")
    time_range: str = Field(..., description="时间范围")
    conclusion: str = Field(..., description="有网感的点评，可以稍微调侃但要客观")


# 专门用于转发消息总结的agent
forward_agent = Agent(model, result_type=ForwardMessageSummary, retries=5)


@forward_agent.system_prompt
async def forward_system_prompt():
    return """
你是一个很有网感的总结助手，专门处理转发消息。你要用年轻人喜欢的表达方式来总结内容。

要求：
1. 语言要有网感，可以用网络流行语、网络梗
2. 保持客观但不要太正式，要有点趣味性
3. 突出重点，让人一眼就懂发生了啥
4. 如果是瓜的话要突出瓜点，如果是争议要平衡各方
5. 语言要简洁有力，不要太啰嗦
6. 注意：消息中可能包含图片分析内容，格式为"[图片: 描述, 图片文字: xxx, 类型: xxx]"，要合理利用这些信息

输出格式：
- title: 10-15字，要有梗感（如："某up和粉丝的代练翻车现场"、"游戏圈又现新瓜"）
- brief_summary: 30-50字，一句话说清楚，要有网感，可以用"翻车了"、"破防了"、"整活"等网络词汇
- detailed_summary: 150-200字，详细说明来龙去脉，语言轻松但信息完整，如果有图片内容要适当提及
- key_points: 3-5个爆点，用网络化表达，突出有趣的地方，如果图片很重要可以单独作为要点
- participants: 主要人物，可以用有趣的称呼
- time_range: 时间范围
- conclusion: 一句话点评，要有点网感

记住：要让90后00后看了觉得有意思，但信息要准确完整！如果有图片截图等视觉证据，要重点提及！
"""


async def get_forward_summary(chat_records_str: str, retries: int = 5) -> ForwardMessageSummary:
    """获取转发消息的总结"""
    try:
        return (await forward_agent.run(chat_records_str)).data
    except Exception as e:
        logger.error(f"Error in get_forward_summary: {e}")
        if retries > 0:
            return await get_forward_summary(chat_records_str, retries - 1)
        return ForwardMessageSummary(
            title="总结生成失败",
            brief_summary="由于技术原因，无法生成转发消息总结",
            detailed_summary="处理过程中出现错误，请稍后重试或联系管理员",
            key_points=["总结生成失败"],
            participants=["未知"],
            time_range="未知",
            conclusion="处理过程中出现错误",
        )


async def build_forward_summary_list(forward_summary: ForwardMessageSummary) -> list[str]:
    """构建转发消息总结的字符串列表"""
    result = []

    # 标题 - 用更有趣的emoji
    result.append(f"🍉 {forward_summary.title}")

    # 核心总结 - 最突出的部分
    result.append("")
    result.append("💥 一句话总结")
    result.append(f"👉 {forward_summary.brief_summary}")
    result.append("")

    # 基本信息 - 简化显示
    result.append(f"⏰ {forward_summary.time_range}")
    result.append(f"🧑‍💻 参与：{', '.join(forward_summary.participants)}")
    result.append("")

    # 详细瓜情
    result.append("📖 详细瓜情")
    result.append(forward_summary.detailed_summary)
    result.append("")

    # 关键爆点
    result.append("⚡ 关键爆点")
    for i, point in enumerate(forward_summary.key_points, 1):
        # 使用更有趣的bullet点
        bullet = ["🔥", "💣", "⭐", "🎯", "🚨"][i - 1] if i <= 5 else "•"
        result.append(f"{bullet} {point}")
    result.append("")

    # 总结点评
    result.append("🤔 点评")
    result.append(forward_summary.conclusion)

    return result


class ImageAnalysisResult(BaseModel):
    """图片分析结果"""

    description: str = Field(..., description="图片内容的详细描述")
    text_content: str = Field(..., description="图片中的文字内容（如果有）")
    key_elements: list[str] = Field(..., description="图片中的关键元素列表")


# 图片分析agent
image_agent = Agent(vision_model, result_type=ImageAnalysisResult, retries=3)


@image_agent.system_prompt
async def image_system_prompt():
    return """
你是一个专业的图片分析助手，需要分析聊天记录中的图片内容。

分析要求：
1. 详细描述图片的主要内容
2. 提取图片中的所有文字内容（OCR）
3. 识别图片中的关键元素（人物、物品、场景等）
4. 语言要简洁明了，符合聊天场景

输出格式：
- description: 用2-3句话描述图片的主要内容
- text_content: 提取图片中的文字，如果没有文字就写"无文字内容"
- key_elements: 列出图片中的关键元素，如"截图"、"表情包"、"商品图片"等
"""


async def analyze_image(image_url: str, retries: int = 3) -> ImageAnalysisResult:
    """分析单张图片"""
    try:

        async def get_binary_content(url: str) -> BinaryContent:
            async with AsyncClient() as client:
                response = await client.get(url)
                return BinaryContent(data=response.content, media_type="image/jpeg")

        result = await image_agent.run(["请分析这张图片", await get_binary_content(image_url)])
        return result.output
    except Exception as e:
        logger.error(f"图片分析失败: {e}")
        if retries > 0:
            return await analyze_image(image_url, retries - 1)
        return ImageAnalysisResult(description="图片分析失败", text_content="无法提取", key_elements=["分析失败"])


class PlainTextSummary(BaseModel):
    """纯文本总结模型"""

    title: str = Field(..., description="文本的主要标题或主题，要简洁有力")
    core_message: str = Field(..., description="核心信息，40-60字概括最重要的内容")
    key_points: list[str] = Field(..., description="3-5个关键要点，突出重要信息")
    content_type: str = Field(..., description="内容类型，如'技术文档'、'新闻资讯'、'讨论话题'、'个人分享'等")
    summary: str = Field(..., description="100-150字的详细总结，保持原文的重要信息和语言风格")
    conclusion: str = Field(..., description="一句话结论或评价")


# 专门用于纯文本总结的agent
text_agent = Agent(model, result_type=PlainTextSummary, retries=5)


@text_agent.system_prompt
async def text_system_prompt():
    return """
你是一个专业的文本总结助手，专门处理用户发送的长文本内容。

处理要求：
1. 保持原文的核心信息和语言风格
2. 提取最重要的关键点
3. 根据内容特点判断文本类型
4. 语言要简洁明了，突出重点
5. 如果是技术内容要保持准确性
6. 如果是讨论或观点要保持客观性

输出格式：
- title: 10-20字，概括文本的主要主题
- core_message: 40-60字，用一句话说明最核心的内容
- key_points: 3-5个要点，每个要点简洁明了
- content_type: 判断内容类型
- summary: 100-150字详细总结，保持原文风格和重要信息
- conclusion: 一句话总结或评价

注意：要忠实于原文内容，不要添加不存在的信息！
同时，这是qq群，用户想要看到的总结是中文的。
"""


async def get_text_summary(text_content: str, retries: int = 5) -> PlainTextSummary:
    """获取纯文本的总结"""
    try:
        return (await text_agent.run(text_content)).data
    except Exception as e:
        logger.error(f"Error in get_text_summary: {e}")
        if retries > 0:
            return await get_text_summary(text_content, retries - 1)
        return PlainTextSummary(
            title="总结生成失败",
            core_message="由于技术原因，无法生成文本总结",
            key_points=["总结生成失败"],
            content_type="错误",
            summary="处理过程中出现错误，请稍后重试或联系管理员",
            conclusion="处理失败",
        )


async def build_text_summary_list(text_summary: PlainTextSummary) -> list[str]:
    """构建文本总结的字符串列表"""
    result = []

    # 标题和类型
    result.append(f"📄 {text_summary.title}")
    result.append(f"🏷️ 类型：{text_summary.content_type}")
    result.append("")

    # 核心信息
    result.append("💡 核心信息")
    result.append(text_summary.core_message)
    result.append("")

    # 关键要点
    if text_summary.key_points:
        result.append("🔑 关键要点")
        for i, point in enumerate(text_summary.key_points, 1):
            result.append(f"{i}. {point}")
        result.append("")

    # 详细总结
    result.append("📖 详细总结")
    result.append(text_summary.summary)
    result.append("")

    # 结论
    result.append("🎯 结论")
    result.append(text_summary.conclusion)

    return result


class CustomAnalysisResult(BaseModel):
    """自定义分析结果模型"""

    title: str = Field(..., description="分析标题，体现分析的主要方向和发现")
    analysis_focus: str = Field(..., description="分析重点，说明按照用户指引主要关注了什么")
    key_findings: list[str] = Field(..., description="3-5个关键发现，针对用户指引的具体分析结果")
    detailed_analysis: str = Field(..., description="详细分析内容，150-300字，深入解读消息内容")
    participants_analysis: list[str] = Field(..., description="主要参与者及其行为分析")
    timeline_analysis: str = Field(..., description="时间线分析，事情的发展过程")
    conclusion: str = Field(..., description="总结性结论，回应用户的分析需求")
    suggestions: list[str] = Field(..., description="基于分析的建议或观点（可选，根据情况提供）")


# 自定义分析agent
custom_agent = Agent(model, result_type=CustomAnalysisResult, retries=5)


@custom_agent.system_prompt
async def custom_analysis_system_prompt():
    return """
你是一个专业的消息分析助手，能够根据用户的具体指引对聊天记录进行深度分析。

你的任务：
1. 仔细理解用户的分析指引和要求
2. 从聊天记录中提取相关信息
3. 按照用户的指引方向进行深入分析
4. 提供客观、准确、有价值的分析结果

分析要求：
- 保持客观中立，不偏不倚
- 基于事实进行分析，避免过度推测
- 语言要专业但通俗易懂
- 突出用户关心的重点内容
- 如果是"锐评"类需求，可以稍微犀利一些，但要有理有据
- 如果是"分析"类需求，要更加客观理性
- 注意时间线和事件发展脉络

输出格式要求：
- title: 体现分析方向的标题
- analysis_focus: 明确说明重点关注了什么
- key_findings: 3-5个关键发现，要有针对性
- detailed_analysis: 150-300字详细分析
- participants_analysis: 主要人物及其行为分析
- timeline_analysis: 事情发展的时间线
- conclusion: 总结性结论
- suggestions: 相关建议（如果适用）

记住：要根据用户的具体指引来调整分析的重点和风格！
"""


async def get_custom_analysis(chat_records_str: str, user_instruction: str, retries: int = 5) -> CustomAnalysisResult:
    """获取自定义分析结果"""
    try:
        # 将用户指引和聊天记录组合
        analysis_prompt = f"<聊天记录>\n{chat_records_str}</聊天记录>\n<用户指引>{user_instruction}</用户指引>"
        return (await custom_agent.run(analysis_prompt)).output
    except Exception as e:
        logger.error(f"Error in get_custom_analysis: {e}")
        if retries > 0:
            return await get_custom_analysis(chat_records_str, user_instruction, retries - 1)
        return CustomAnalysisResult(
            title="分析生成失败",
            analysis_focus=f"尝试按照'{user_instruction}'进行分析",
            key_findings=["分析过程中出现技术错误"],
            detailed_analysis="由于技术原因，无法完成自定义分析，请稍后重试或联系管理员",
            participants_analysis=["无法分析"],
            timeline_analysis="无法分析时间线",
            conclusion="分析失败，请重试",
            suggestions=["建议稍后重试或简化分析需求"],
        )


async def build_custom_analysis_list(analysis_result: CustomAnalysisResult) -> list[str]:
    """构建自定义分析结果的字符串列表"""
    result = []

    # 标题
    result.append(f"🎯 {analysis_result.title}")
    result.append("")

    # 分析重点
    result.append("🔍 分析重点")
    result.append(analysis_result.analysis_focus)
    result.append("")

    # 关键发现
    result.append("⚡ 关键发现")
    for i, finding in enumerate(analysis_result.key_findings, 1):
        result.append(f"{i}. {finding}")
    result.append("")

    # 参与者分析
    if analysis_result.participants_analysis:
        result.append("👥 参与者分析")
        for participant in analysis_result.participants_analysis:
            result.append(f"• {participant}")
        result.append("")

    # 时间线分析
    result.append("⏱️ 时间线分析")
    result.append(analysis_result.timeline_analysis)
    result.append("")

    # 详细分析
    result.append("📊 详细分析")
    result.append(analysis_result.detailed_analysis)
    result.append("")

    # 结论
    result.append("💡 结论")
    result.append(analysis_result.conclusion)

    # 建议（如果有）
    if analysis_result.suggestions:
        result.append("")
        result.append("💭 建议")
        for i, suggestion in enumerate(analysis_result.suggestions, 1):
            result.append(f"{i}. {suggestion}")

    return result
