from nonebot import get_plugin_config
from pydantic import BaseModel, field_validator


class GroupSummaryConfig(BaseModel):
    """群组总结配置"""

    group_id: str
    hourly_summary: bool = False
    daily_summary: bool = True


class Config(BaseModel):
    """Plugin Config Here"""

    openai_api_key: str | int
    openai_base_url: str
    openai_model: str
    # 专门用于图片识别的模型，需要支持图片处理
    openai_vision_model: str = "gpt-4.1-mini"
    # 是否启用图片解析功能（默认关闭，通过命令参数控制）
    enable_image_analysis: bool = False
    aggregate_message: bool = True
    timezone: str = "Asia/Shanghai"

    # 群组总结配置，格式："群号:每小时总结:每日总结"，例如 "123456789:1:1;987654321:0:1"
    # 其中 1 表示启用，0 表示禁用
    summary_groups: str = "778701522:1:1;1022691140:1:1"

    # 最小总结消息数量
    min_messages_for_summary: int = 20

    # 最大未总结小时数
    max_unsummarized_hours: int = 12

    @field_validator("summary_groups")
    @classmethod
    def parse_summary_groups(cls, v: str) -> str:
        # 验证格式是否正确
        if not v:
            return v

        groups = v.split(";")
        for group in groups:
            if not group:
                continue

            parts = group.split(":")
            if len(parts) != 3:
                raise ValueError(f"群组总结配置格式错误: {group}，应为 '群号:每小时总结:每日总结'")

            group_id, hourly, daily = parts
            if not group_id.isdigit():
                raise ValueError(f"群号必须为数字: {group_id}")

            if hourly not in ["0", "1"]:
                raise ValueError(f"每小时总结配置必须为 0 或 1: {hourly}")

            if daily not in ["0", "1"]:
                raise ValueError(f"每日总结配置必须为 0 或 1: {daily}")

        return v

    def get_group_configs(self) -> list[GroupSummaryConfig]:
        """解析群组总结配置"""
        result = []

        if not self.summary_groups:
            return result

        groups = self.summary_groups.split(";")
        for group in groups:
            if not group:
                continue

            parts = group.split(":")
            if len(parts) != 3:
                continue

            group_id, hourly, daily = parts
            result.append(
                GroupSummaryConfig(group_id=group_id, hourly_summary=(hourly == "1"), daily_summary=(daily == "1"))
            )

        return result


config = get_plugin_config(Config)
