from asyncio import Lock
import hashlib
from uuid import uuid4


def hash_strings(strings):
    hash_object = hashlib.md5()
    strings = [str(s) if s is not None else "none" for s in strings]
    [hash_object.update(s.encode("utf-8")) for s in strings]
    return hash_object.hexdigest()


def hash_s(*strings):
    return hash_strings(strings)


class Locks:
    locks_dict: dict[str, Lock] = {}

    @staticmethod
    def get_global_lock():
        Locks.locks_dict["global"] = Locks.locks_dict.get("global", Lock())
        return Locks.locks_dict["global"]

    @staticmethod
    def get_group_lock(group_id: str, plugin_name: str = "", command_name: str = ""):
        return Locks.get_lock(hash_s(group_id, plugin_name, command_name))

    @staticmethod
    def get_user_lock(user_id: str, group_id: str, plugin_name: str = "", command_name: str = ""):
        return Locks.get_lock(hash_s(user_id, group_id, plugin_name, command_name))

    @staticmethod
    def get_plugin_lock(plugin_name: str):
        return Locks.get_lock(hash_s(plugin_name))

    @staticmethod
    def get_command_lock(command_name: str):
        return Locks.get_lock(hash_s(command_name))

    @staticmethod
    def get_uuid_lock():
        return Locks.get_lock(hash_s(uuid4().hex))

    @staticmethod
    def get_lock(key: str):
        Locks.locks_dict[key] = Locks.locks_dict.get(key, Lock())
        return Locks.locks_dict[key]


if __name__ == "__main__":

    async def test():
        print(hash_strings(["hello", "world"]))
        print(hash_s("hello", "world", None))
        Locks.get_global_lock()
        Locks.get_group_lock("123")
        Locks.get_user_lock("123", "456")
        Locks.get_plugin_lock("test")
        Locks.get_command_lock("test")
        Locks.get_uuid_lock()

        Locks.get_group_lock(1, None, 1)

    import asyncio

    asyncio.run(test())
