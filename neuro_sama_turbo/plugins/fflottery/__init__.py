# python
import asyncio
import hashlib
import json
import os
import random
import time
from typing import Union

import nonebot
from nonebot import get_driver, get_plugin_config, on_startswith, require

require("nonebot_plugin_apscheduler")
from nonebot.adapters.onebot.v11.event import GroupMessageEvent as V11GroupMessageEvent
from nonebot.adapters.onebot.v12.event import GroupMessageEvent as V12GroupMessageEvent
from nonebot.internal.adapter.bot import Bo<PERSON>
from nonebot.plugin import PluginMetadata
from nonebot_plugin_apscheduler import scheduler
from nonebot_plugin_saa import MessageFactory, Text
from pydantic import BaseModel

from ..handle_prefix import handle_prefix
from .config import Config

__plugin_meta__ = PluginMetadata(
    name="batch_number_lottery",
    description="支持批量抽奖，每个奖品为参与者随机生成一个数字，按照数字大小确定获奖者",
    usage="",
    config=Config,
)

config = get_plugin_config(Config)

batch_lottery_start = "发起ff抽奖"
batch_lottery_join = "参与ff抽奖"
batch_lottery_end = "结束ff抽奖"

batch_start_command = on_startswith(batch_lottery_start, priority=1)
batch_join_command = on_startswith(batch_lottery_join, priority=1)
batch_end_command = on_startswith(batch_lottery_end, priority=1)

batch_lottery_dict = {}
batch_hash_to_lottery = {}
name_cache = {}

special_users = [
    "851064719",
    "1793808067",
    "391919786",
    "740718369",
    "1162294120",
    "1653023621",
    "2382534467",
    "1246858445",
    "644201934",
    "3253261989",
    "1801225889",
    "232129096",
    "1355121265",
    "270981441",
    "2417360005",
    "570168498",
    "709295876",
]
super_users = ["391919786"]
cnt_for_user = {}

store_path1 = os.path.join(os.path.dirname(__file__), "store1.json")
store_path2 = os.path.join(os.path.dirname(__file__), "store2.json")
store_path3 = os.path.join(os.path.dirname(__file__), "store3.json")


def load_or_create_store():
    global batch_lottery_dict, batch_hash_to_lottery, name_cache

    def load_or_create_json(file_path: str, default_value: dict):
        if not os.path.exists(file_path):
            return default_value
        with open(file_path, encoding="utf-8") as f:
            return json.load(f)

    batch_lottery_dict = load_or_create_json(store_path1, {})
    batch_hash_to_lottery = load_or_create_json(store_path2, {})
    name_cache = load_or_create_json(store_path3, {})


def save_store():
    def save_to_json(file_path: str, data: dict):
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

    # save_to_json(store_path1, batch_lottery_dict)
    # save_to_json(store_path2, batch_hash_to_lottery)
    # save_to_json(store_path3, name_cache)
    batch_lottery_dict_copy = batch_lottery_dict.copy()
    for key in batch_lottery_dict_copy:
        batch_lottery_dict_copy[key] = batch_lottery_dict_copy[key].dict()
    save_to_json(store_path1, batch_lottery_dict_copy)
    batch_hash_to_lottery_copy = batch_hash_to_lottery.copy()
    for key in batch_hash_to_lottery_copy:
        batch_hash_to_lottery_copy[key] = batch_hash_to_lottery_copy[key].dict()
    save_to_json(store_path2, batch_hash_to_lottery_copy)
    save_to_json(store_path3, name_cache)


@scheduler.scheduled_job("interval", seconds=60)
async def save_store_periodically():
    save_store()


driver = nonebot.get_driver()


@driver.on_startup
async def start_up():
    load_or_create_store()


def is_special_user(user_id: str) -> bool:
    cnt_for_user[user_id] = cnt_for_user.get(user_id, 0) + 1
    if cnt_for_user[user_id] > 1:
        return False  # 一个用户连续两次以上调用时不再判断是否为特殊用户, 防止露馅
    return user_id in special_users or user_id in super_users


# 是否只抽取500以上的点数给他
def is_special_user_only_500(user_id: str) -> bool:
    if not is_special_user(user_id):
        return False
    if user_id in super_users:  # 超级用户一定只抽取500以上的点数
        return True
    random_number = random.randint(1, 100)
    return random_number <= 50


def is_super_user(user_id: str) -> bool:
    return user_id in super_users


def get_random_number_from_list(numbers: list[int], user_id: str) -> int:
    if not numbers:
        return 0
    # 一般用户直接取出一个随机数，特殊用户有一定概率只取500以上的随机数（抽出一个数后不放回）
    if not is_special_user_only_500(user_id):
        return numbers.pop()
    # 只抽取500以上的点数
    numbers_500 = (
        [number for number in numbers if number >= 700]
        if not is_super_user(user_id)
        else [number for number in numbers if number >= 766]
    )
    if not numbers_500:
        return numbers.pop()
    temp = numbers_500.pop()
    numbers.remove(temp)
    return temp


class BatchLotteryItem(BaseModel):
    name: str


class BatchLottery(BaseModel):
    group_id: str
    user_id: str
    lottery_name: str
    items: list[BatchLotteryItem]
    available_numbers: list[int] = []
    participants: dict[str, dict[str, int]] = {}
    start_time: float
    end_time: float = None


@batch_start_command.handle()
async def handle_batch_start(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    args = handle_prefix(str(event.get_plaintext()), len(batch_lottery_start)).strip()
    group_id, user_id = str(event.group_id), str(event.user_id)
    lottery_items = args.split("，")
    if not lottery_items:
        await MessageFactory([Text("请提供要抽奖的奖品列表，用中文逗号分隔。")]).send(at_sender=True)
        return
    lottery_name = "ff抽奖"
    items = [BatchLotteryItem(name=item.strip()) for item in lottery_items]
    available_numbers = list(range(1, 1000))
    random.shuffle(available_numbers)
    lottery = BatchLottery(
        group_id=group_id,
        user_id=user_id,
        lottery_name=lottery_name,
        available_numbers=available_numbers,
        items=items,
        start_time=time.time(),
    )
    hash_code = hashlib.md5(f"{group_id}_{user_id}_{lottery_name}_{time.time()}".encode()).hexdigest()
    batch_lottery_dict[hash_code] = lottery
    batch_hash_to_lottery[hash_code] = lottery
    name_cache[user_id] = event.sender.card or event.sender.nickname

    items_info = "\n".join([f"{i + 1}. {item.name}" for i, item in enumerate(items)])
    await MessageFactory(
        [
            Text(
                f"抽奖创建成功！\n抽奖hash：{hash_code[:8]}\n抽奖发起者：{name_cache[user_id]}({user_id})\n奖品列表：\n{items_info}"
            )
        ]
    ).send(at_sender=True)
    save_store()


@batch_join_command.handle()
async def handle_batch_join(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    args = handle_prefix(str(event.get_plaintext()), len(batch_lottery_join)).strip()
    group_id, user_id = str(event.group_id), str(event.user_id)
    hash_code_input = args.strip()
    matched_hashes = [code for code in batch_hash_to_lottery if code.startswith(hash_code_input)]
    if len(matched_hashes) != 1:
        await MessageFactory([Text("抽奖不存在或哈希不唯一，请提供准确的抽奖hash。")]).send(at_sender=True)
        return
    hash_code = matched_hashes[0]
    lottery = batch_hash_to_lottery[hash_code]
    if lottery.end_time is not None:
        await MessageFactory([Text("抽奖已经结束！")]).send(at_sender=True)
        return
    if user_id in lottery.participants:
        await MessageFactory([Text("你已经参加过该抽奖了！")]).send(at_sender=True)
        return
    # random_numbers = {item.name: random.randint(1, 999) for item in lottery.items}
    # random_numbers = {}
    random_numbers = []
    for item in lottery.items:
        # number = lottery.available_numbers.pop()
        number = get_random_number_from_list(lottery.available_numbers, user_id)
        # random_numbers[item.name] = number
        random_numbers.append(number)
    lottery.participants[user_id] = random_numbers
    name_cache[user_id] = event.sender.card or event.sender.nickname
    # numbers_info = "\n".join([f"{name}：🎲{number}点" for name, number in random_numbers.items()])
    numbers_info = "\n".join(
        [f"{name}：🎲{number}点" for name, number in zip([item.name for item in lottery.items], random_numbers)]
    )
    await MessageFactory([Text(f"成功参与抽奖！你roll到的点数是：\n{numbers_info}")]).send(at_sender=True)
    save_store()


@batch_end_command.handle()
async def handle_batch_end(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    args = handle_prefix(str(event.get_plaintext()), len(batch_lottery_end)).strip()
    group_id, user_id = str(event.group_id), str(event.user_id)
    hash_code_input = args.strip()
    matched_hashes = [code for code in batch_hash_to_lottery if code.startswith(hash_code_input)]
    if len(matched_hashes) != 1:
        await MessageFactory([Text("抽奖不存在或哈希不唯一，请提供准确的抽奖hash。")]).send(at_sender=True)
        return
    hash_code = matched_hashes[0]
    lottery = batch_hash_to_lottery[hash_code]
    if lottery.user_id != user_id:
        await MessageFactory([Text("你不是该抽奖的发起者！")]).send(at_sender=True)
        return
    if not lottery.participants:
        await MessageFactory([Text("没有人参加抽奖，无法开奖！")]).send(at_sender=True)
        return
    winners_info = []
    # index_for_name = {item.name: i for i, item in enumerate(lottery.items)}
    for i, item in enumerate(lottery.items):
        max_number = -1
        winner_id = None
        for participant_id, numbers in lottery.participants.items():
            # number = numbers.get(item.name, 0)
            number = numbers[i]
            if number > max_number:
                max_number = number
                winner_id = participant_id
            elif number == max_number:
                # 如果有相同的最大值，随机决定一个获胜者
                winner_id = random.choice([winner_id, participant_id])
        winner_name = name_cache.get(winner_id, winner_id)
        winners_info.append(f"{i + 1}.{item.name}：🎲{max_number}点，获胜者：{winner_name}({winner_id})")
    result_message = f"""抽奖结束！
抽奖hash：{hash_code[:8]}
抽奖发起者：{name_cache[lottery.user_id]}({lottery.user_id})
获奖结果：
""" + "\n".join(winners_info)
    await MessageFactory([Text(result_message)]).send()
    # 清理已结束的抽奖
    del batch_lottery_dict[hash_code]
    del batch_hash_to_lottery[hash_code]
    for user_id in cnt_for_user:
        cnt_for_user[user_id] = 0
    save_store()
