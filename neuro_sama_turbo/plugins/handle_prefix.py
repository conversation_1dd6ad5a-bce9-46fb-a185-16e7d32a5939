from nonebot import get_driver
from nonebot.adapters.onebot.v11.event import GroupMessageEvent as V11GroupMessageEvent
from nonebot.adapters.onebot.v11.event import MessageEvent as V11MessageEvent
from nonebot.adapters.onebot.v11.event import PokeNotifyEvent
from nonebot.adapters.onebot.v12.event import GroupMessageEvent as V12GroupMessageEvent
from nonebot.adapters.onebot.v12.event import MessageEvent as V12MessageEvent

driver = get_driver()
prefixs = driver.config.command_start


def handle_prefix(prompt: str, command_len: int) -> str:
    global prefixs
    for prefix in prefixs:
        if prompt.startswith(prefix):
            prompt = prompt[len(prefix) :]
            break
    return prompt[command_len:].strip()


# event中去掉前缀
def handle_prefix_event(
    event: V11MessageEvent | V11GroupMessageEvent | V12MessageEvent | V12GroupMessageEvent | PokeNotifyEvent,
    command_len: int,
):
    global prefixs
    messages = event.get_message()
    for prefix in prefixs:
        if messages[0].type == "text" and messages[0].data["text"].startswith(prefix):
            messages[0].data["text"] = messages[0].data["text"][len(prefix) :]
            break
    if messages[0].type == "text":
        messages[0].data["text"] = messages[0].data["text"][command_len:].strip()
    return messages
