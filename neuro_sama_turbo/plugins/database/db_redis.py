from redis.asyncio import Redis
import json
from pydantic import BaseModel

from .config import config


redis: Redis = Redis.from_url(config.redis_url)


async def set_value(key: str, value: any, expire: int | None = None):
    """Set a value in Redis with an optional expiration time."""
    await redis.set(key, value, ex=expire)

async def get_value(key: str) -> any:
    """Get a value from Redis."""
    return await redis.get(key)

async def delete_value(key: str):
    """Delete a value from Redis."""
    await redis.delete(key)

async def set_json(key: str, value: any, expire: int | None = None):
    """Set a JSON value in Redis with an optional expiration time."""
    if issubclass(value.__class__, BaseModel):
        value = value.dict()
    await redis.set(key, json.dumps(value), ex=expire)

async def get_json(key: str) -> any:
    """Get a JSON value from Redis."""
    value = await redis.get(key)
    if value is None:
        return None
    return json.loads(value)

async def delete_json(key: str):
    """Delete a JSON value from Redis."""
    await redis.delete(key)

async def set_model(key: str, model: BaseModel, expire: int | None = None):
    """Set a Pydantic model in Redis with an optional expiration time."""
    await set_json(key, model.dict(), expire)

async def get_model(key: str, model: BaseModel):
    """Get a Pydantic model from Redis."""
    json_data = await get_json(key)
    if json_data is None:
        return None
    return model.parse_obj(json_data)
async def delete_model(key: str):
    """Delete a Pydantic model from Redis."""
    await delete_value(key)

async def rset(key: str, value: any, expire: int | None = None):
    """Set a value in Redis with an optional expiration time."""
    if issubclass(value.__class__, BaseModel):
        await set_model(key, value, expire)
    elif isinstance(value, dict) or isinstance(value, list):
        await set_json(key, value, expire)
    else:
        await set_value(key, value, expire)

async def rget(key: str, model: BaseModel | None = None):
    """Get a value from Redis."""
    if model:
        return await get_model(key, model)
    else:
        try:
            return await get_json(key)
        except:
            return await get_value(key)

async def rdelete(key: str):
    """Delete a value from Redis."""
    await delete_value(key)