from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorCollection
from pydantic import BaseModel

from .config import config

client = AsyncIOMotorClient(config.mongo_url)
defaule_db = client["neuro_sama_turbo_default"]

async def get_collection(collection_name: str) -> AsyncIOMotorCollection:
    return defaule_db[collection_name]

async def find_one(collection_name: str, query: dict, *args: any, **kwargs: any) -> dict:
    return (await get_collection(collection_name)).find_one(query, *args, **kwargs)

async def find(collection_name: str, query: dict, *args: any, **kwargs: any) -> dict:
    return (await get_collection(collection_name)).find(query, *args, **kwargs)

async def insert_one(collection_name: str, document: dict, *args: any, **kwargs: any) -> dict:
    return (await get_collection(collection_name)).insert_one(document, *args, **kwargs)

async def insert_many(collection_name: str, documents: list, *args: any, **kwargs: any) -> dict:
    return (await get_collection(collection_name)).insert_many(documents, *args, **kwargs)

async def update_one(collection_name: str, query: dict, update: dict, upsert: bool = False, *args: any, **kwargs: any) -> dict:
    return (await get_collection(collection_name)).update_one(query, update, upsert=upsert, *args, **kwargs)

async def update_many(collection_name: str, query: dict, update: dict, upsert: bool = False, *args: any, **kwargs: any) -> dict:
    return (await get_collection(collection_name)).update_many(query, update, upsert=upsert, *args, **kwargs)

async def delete_one(collection_name: str, query: dict, *args: any, **kwargs: any) -> dict:
    return (await get_collection(collection_name)).delete_one(query, *args, **kwargs)

async def delete_many(collection_name: str, query: dict, *args: any, **kwargs: any) -> dict:
    return (await get_collection(collection_name)).delete_many(query, *args, **kwargs)

async def count_documents(collection_name: str, query: dict, *args: any, **kwargs: any) -> dict:
    return (await get_collection(collection_name)).count_documents(query, *args, **kwargs)


