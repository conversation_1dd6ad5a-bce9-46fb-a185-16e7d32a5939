import asyncio
import json
import re

from nonebot import require

require("nonebot_plugin_htmlrender")
from nonebot_plugin_alconna import (
    Alconna,
    Args,
    Command,
    Match,
    MsgId,
    Subcommand,
    UniMessage,
    on_alconna,
)
from nonebot_plugin_alconna import (
    At as alconnaAt,
)
from nonebot_plugin_alconna import (
    CustomNode as alconnaCustomNode,
)
from nonebot_plugin_alconna import (
    Reference as alconnaReference,
)
from nonebot_plugin_alconna.builtins.extensions import ReplyRecordExtension
from nonebot_plugin_htmlrender import (
    get_new_page,
    md_to_pic,
    template_to_pic,
    text_to_pic,
)


async def md2pic(md: str):
    return await md_to_pic(md)


from typing import Union

from nonebot import get_driver, get_plugin_config
from nonebot.adapters.onebot.v11.event import GroupMessageEvent as V11GroupMessageEvent
from nonebot.adapters.onebot.v12.event import GroupMessageEvent as V12GroupMessageEvent
from nonebot.internal.adapter.bot import Bo<PERSON>
from nonebot.plugin import PluginMetadata
from nonebot_plugin_saa import Image as saaImage
from nonebot_plugin_saa import MessageFactory, TargetQQGroup, Text, enable_auto_select_bot

from .config import Config

enable_auto_select_bot()
import nonebot

from ..handle_prefix import handle_prefix
from .gemini import Gemini
from .oneapi import OneAPI
from .oneapi.db.db_mongo import (
    clear_current_prompt,
    create_user_prompt,
    get_current_prompt,
    get_user_or_create,
    search_prompt,
    set_current_prompt,
    show_prompt,
    upload_prompt,
)
from .oneapi.db.db_mongo import (
    create_index as create_index,
)
from .oneapi.db.db_mongo import (
    display_results as display_results,
)
from .pplx import PplxAPI

__plugin_meta = PluginMetadata(
    name="mixai",
    description="",
    usage="",
    config=Config,
)

global_config = get_driver().config
config = get_plugin_config(Config)

mixai_command_prefix = "mixai"
mixai_command = nonebot.on_startswith(msg=mixai_command_prefix, priority=1, block=True)

api_ai = {
    "gpt": OneAPI.get_gpt_response,
    "glm": OneAPI.get_chatglm_response,
    "qwen": OneAPI.get_qwen_response,
    "xunfei": OneAPI.get_xunfei_response,
    "pplx": OneAPI.get_pplx_response,
    "dsr": OneAPI.get_deepseek_r1_response,
    "dsv": OneAPI.get_deepseek_response,
    #    "coze": OneAPI.get_coze_response,
}

serach_ai = {
    # "bing": Bing.get_response,
    #    "bard": Bard.get_response,
}

ais = {
    #    "gemini": Gemini.get_response,
    **api_ai,
    **serach_ai,
}


async def request_all_ai(func_name, func, str_prompt, user_id, target):
    try:
        if str_prompt.startswith("system") and func_name not in api_ai.keys():
            return
        resp = await func(str_prompt, user_id)
        resp = [Text(f"\n{func_name}：\n")] + resp
        if str_prompt.startswith("system"):
            return  # 不重复发送系统提示
        if str_prompt.startswith("exit"):
            return  # 不重复发送退出提示
        await MessageFactory(resp).send(at_sender=True)
    except Exception as e:
        await MessageFactory([Text(f"\n模型{func_name}出错：{e}")]).send(at_sender=True)


async def request_friend_gpt(command, prompt):
    await MessageFactory([Text(f"{command} {prompt}")]).send()


"""
[mixai set <ai_name>] 设置默认ai
[mixai system <id>] 为通过api调用的ai设置起手式/系统提示
[mixai system create <prompt>] 创建起手式，会返回id，用于设置系统提示
[mixai <api_ai_name> system <id>] 为指定ai设置起手式/系统提示
"""

sv_help = """
[mixai all <prompt>] 调用所有ai
[mixai api <prompt>] 调用所有通过api调用的ai
[mixai search <prompt>] 调用接入搜索引擎的ai（bing，bard）
[mixai bing <prompt>] 调用bing
[mixai gemini <prompt>] 调用gemini
[mixai bard <prompt>] 调用bard
[mixai glm <prompt>] 调用chatglm
[mixai qwen <prompt>] 调用通义千问
[mixai gpt <prompt>] 调用chatgpt3.5
[mixai xunfei <prompt>] 调用讯飞星火
[mixai <prompt>] 调用默认ai
[mixai <ai_name> exit] 退出指代ai
[mixai all exit] 退出所有ai
[mixai exit] 退出默认ai
[mixai list] 列出所有ai
"""


async def find_image_url(event) -> list[str] | str | None:
    img_url = []
    for msg in event.message:
        if msg.type == "image":
            # img_url = msg.data["url"]
            # break
            img_url.append(msg.data["url"])
    return img_url or None


@mixai_command.handle()
async def mixai(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    str_prompt = handle_prefix(event.get_plaintext(), len(mixai_command_prefix))
    group_id, user_id = (
        str(event.group_id),
        str(event.user_id),
    )  # event.get_session_id().split("_")
    img_url = await find_image_url(event)
    if str_prompt.startswith("all"):
        str_prompt = str_prompt[3:].strip()
        if not str_prompt:
            await MessageFactory([Text("\n请输入问题")]).send(at_sender=True)
            await mixai_command.finish()
        target = TargetQQGroup(group_id=int(group_id))
        tasks = [
            asyncio.create_task(request_all_ai(func_name, func, str_prompt, user_id, target))
            for func_name, func in ais.items()
        ]
        await asyncio.gather(*tasks)
        if str_prompt.startswith("system"):
            await MessageFactory([Text("\n已设置系统提示")]).send(at_sender=True)
        elif str_prompt.startswith("exit"):
            await MessageFactory([Text("\n已退出")]).send(at_sender=True)
    elif str_prompt in ["list", "模型列表"]:
        model_list_str = "\n".join(list(ais.keys()))
        await MessageFactory([Text(f"\n模型列表：\n{model_list_str}")]).send(at_sender=True)
    elif str_prompt in ["help", "帮助"]:
        await MessageFactory([Text(sv_help)]).send(at_sender=True)
    elif str_prompt.startswith("gpt"):
        str_prompt = str_prompt[3:].strip()
        resp = await OneAPI.get_gpt_response(str_prompt, user_id, img_url)
        await MessageFactory(resp).send(at_sender=True)
    elif str_prompt.startswith("gemini"):
        str_prompt = str_prompt[6:].strip()
        resp = await Gemini.get_response(str_prompt, user_id)
        await MessageFactory(resp).send(at_sender=True)
    elif str_prompt.startswith("coze"):
        str_prompt = str_prompt[4:].strip()
        resp = await OneAPI.get_coze_response(str_prompt, user_id)
        await MessageFactory(resp).send(at_sender=True)
    elif str_prompt.startswith("search"):
        str_prompt = str_prompt[6:].strip()
        target = TargetQQGroup(group_id=int(group_id))
        tasks = [
            asyncio.create_task(request_all_ai(func_name, func, str_prompt, user_id, target))
            for func_name, func in serach_ai.items()
        ]
        await asyncio.gather(*tasks)
    elif str_prompt.startswith("api"):
        str_prompt = str_prompt[3:].strip()
        target = TargetQQGroup(group_id=int(group_id))
        tasks = [
            asyncio.create_task(request_all_ai(func_name, func, str_prompt, user_id, target))
            for func_name, func in api_ai.items()
        ]
        await asyncio.gather(*tasks)
    else:
        # 遍历所有ai
        is_exit = False
        for func_name, func in ais.items():
            if str_prompt.startswith(func_name):
                str_prompt = str_prompt[len(func_name) :].strip()
                # 对应的ai
                resp = await func(str_prompt, user_id, img_url, group_id=group_id)
                await MessageFactory(resp).send(at_sender=True)
                is_exit = True
                break
        if not is_exit:
            str_prompt = str_prompt.strip()
            resp = await OneAPI.get_gpt_response(str_prompt, user_id, img_url, group_id=group_id)
            await MessageFactory(resp).send(at_sender=True)
    await mixai_command.finish()


qwen_command = nonebot.on_startswith(msg="qwen", priority=1, block=True)


@qwen_command.handle()
async def qwen(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    str_prompt = event.get_plaintext().strip()
    str_prompt = str_prompt[4:].strip()
    img_url = await find_image_url(event)
    group_id = str(event.group_id)
    user_id = str(event.user_id)
    resp = await OneAPI.get_qwen_response(str_prompt, user_id, img_url, group_id=group_id)
    await MessageFactory(resp).send(at_sender=True)
    await qwen_command.finish()


qwen_video_command = on_alconna(
    Alconna(
        "qwen_video",
        Args["prompt?", str],
    ),
    extensions=[ReplyRecordExtension()],
    use_cmd_start=True,
    priority=5,
    block=True,
    aliases={"通义千问视频分析", "qv", "qwen_v", "分析视频", "视频分析"},
)
from nonebot.adapters.onebot.v11.message import MessageSegment
from nonebot.params import Depends


@qwen_video_command.handle()
async def _(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
    ext: ReplyRecordExtension,
    msg_id: MsgId,
    prompt: Match[str],
):
    reply = ext.get_reply(msg_id)
    if not reply:
        await qwen_video_command.finish("未找到回复")
    video_list = []
    group_id, user_id = str(event.group_id), str(event.user_id)
    for seg in reply.msg:
        seg: MessageSegment = seg
        print(seg.type, seg.data)
        if seg.type == "file":
            file_id = seg.data["file_id"]
            print(file_id)
            file_data = await bot.get_group_file_url(file_id=file_id, group_id=group_id)
            print(file_data)
            file_url = file_data["url"]
            print(file_url)
            video_list.append(file_url + "test.mp4")
        elif seg.type == "video":
            pass
    if not video_list:
        await qwen_video_command.finish("未找到视频")
    print(video_list)
    await qwen_video_command.send("正在分析中，请耐心等待，建议喝个茶先😀")
    if prompt.available:
        prompt = prompt.result
    else:
        prompt = "分析视频"
    user_id = str(event.user_id)
    resp = await OneAPI.get_qwen_response(prompt=prompt, uid=user_id, video_url=video_list)
    await MessageFactory(resp).send(at_sender=True)


pplx_command = nonebot.on_startswith(msg="pplx", priority=1, block=True)


@pplx_command.handle()
async def pplx(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    str_prompt = event.get_plaintext().strip()
    str_prompt = str_prompt[4:].strip()
    user_id = str(event.user_id)
    resp = await PplxAPI.handle_base_command(user_id, str_prompt)
    # resp = await OneAPI.get_pplx_response(str_prompt, user_id)
    await MessageFactory(resp).send(at_sender=True)
    await pplx_command.finish()


dsr_command = nonebot.on_startswith(msg="dsr", priority=1, block=True)


@dsr_command.handle()
async def dsr(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    str_prompt = event.get_plaintext().strip()
    str_prompt = str_prompt[3:].strip()
    img_url = await find_image_url(event)
    user_id = str(event.user_id)
    group_id = str(event.group_id)
    resp = await OneAPI.get_deepseek_r1_response(str_prompt, user_id, img_url, group_id)
    await MessageFactory(resp).send(at_sender=True)
    await dsr_command.finish()


dsv_command = nonebot.on_startswith(msg="dsv", priority=1, block=True)


@dsv_command.handle()
async def dsv(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    str_prompt = event.get_plaintext().strip()
    str_prompt = str_prompt[3:].strip()
    user_id = str(event.user_id)
    group_id = str(event.group_id)
    img_url = await find_image_url(event)
    resp = await OneAPI.get_deepseek_response(str_prompt, user_id, img_url, group_id)
    await MessageFactory(resp).send(at_sender=True)
    await dsv_command.finish()


jdr_command = nonebot.on_startswith(msg="jdr", priority=1, block=True)


@jdr_command.handle()
async def jdr(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    str_prompt = event.get_plaintext().strip()
    str_prompt = str_prompt[3:].strip()
    user_id = str(event.user_id)
    group_id = str(event.group_id)
    img_url = await find_image_url(event)
    resp = await OneAPI.get_jinaai_deepresearch_response(str_prompt, user_id, img_url, group_id)
    await MessageFactory(resp).send(at_sender=True)
    await jdr_command.finish()


grok_command = nonebot.on_startswith(msg="grok", priority=1, block=True)


@grok_command.handle()
async def grok(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    str_prompt = event.get_plaintext().strip()
    str_prompt = str_prompt[4:].strip()
    user_id = str(event.user_id)
    group_id = str(event.group_id)
    img_url = await find_image_url(event)
    resp = await OneAPI.get_grok_response(str_prompt, user_id, img_url, group_id)
    await MessageFactory(resp).send(at_sender=True)
    await grok_command.finish()


qwq_command = nonebot.on_startswith(msg="qwq", priority=1, block=True)


@qwq_command.handle()
async def qwq(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    str_prompt = event.get_plaintext().strip()
    str_prompt = str_prompt[3:].strip()
    user_id = str(event.user_id)
    group_id = str(event.group_id)
    img_url = await find_image_url(event)
    resp = await OneAPI.get_qwq_response(str_prompt, user_id, img_url, group_id=group_id)
    await MessageFactory(resp).send(at_sender=True)
    await qwq_command.finish()


gemma_command = nonebot.on_startswith(msg="gemma", priority=1, block=True)


@gemma_command.handle()
async def gemma(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    str_prompt = event.get_plaintext().strip()
    str_prompt = str_prompt[5:].strip()
    user_id = str(event.user_id)
    group_id = str(event.group_id)
    img_url = await find_image_url(event)
    resp = await OneAPI.get_gemma_response(str_prompt, user_id, img_url, group_id)
    await MessageFactory(resp).send(at_sender=True)
    await gemma_command.finish()


mm_command = nonebot.on_startswith(msg="mm", priority=1, block=True)


@mm_command.handle()
async def mm(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    str_prompt = event.get_plaintext().strip()
    str_prompt = str_prompt[2:].strip()
    user_id = str(event.user_id)
    group_id = str(event.group_id)
    img_url = await find_image_url(event)
    resp = await OneAPI.get_mm_response(str_prompt, user_id, img_url, group_id)
    await MessageFactory(resp).send(at_sender=True)
    await mm_command.finish()


smol_command = nonebot.on_startswith(msg="smol", priority=1, block=True)


@smol_command.handle()
async def smol(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    str_prompt = event.get_plaintext().strip()
    str_prompt = str_prompt[4:].strip()
    user_id = str(event.user_id)
    group_id = str(event.group_id)
    img_url = await find_image_url(event)
    resp = await OneAPI.get_smolagent_response(str_prompt, user_id, img_url, group_id)
    await MessageFactory(resp).send(at_sender=True)
    await smol_command.finish()


# prompt的search，set，self，show
"""
search 直接根据关键词搜索
set 根据传入的id设置系统提示
self 查看自己的prompt
show 根据传入的id查看prompt
"""
prompt_command = on_alconna(
    Alconna(
        "prompt",
        Subcommand(
            "search",
            Args["keyword", str],
            alias=["搜索"],
        ),
        Subcommand(
            "set",
            Args["prompt_id", str],
            alias=["设置"],
        ),
        Subcommand(
            "self",
            alias=["我的"],
        ),
        Subcommand(
            "show",
            Args["prompt_id", str],
            alias=["查看"],
        ),
        Subcommand(  # 自定义
            "create",
            Args["prompt", str],
            alias=["创建"],
        ),
        Subcommand(
            "clear",
            alias=["清除"],
        ),
        Subcommand(
            "upload",
            Args["prompt_content", str],
            Args["prompt_name", str],
            alias=["上传"],
        ),
        Subcommand(
            "help",
            alias=["帮助", "h"],
        ),
    ),
    aliases=["起手式", "提示词", "pp"],
)


# 封装消息为合并转发，由于目前无法自定义转发中的用户，所以直接硬编码
async def merge_forward(msgs: list):
    return alconnaReference(
        nodes=[
            alconnaCustomNode(
                uid="123456",
                content=msg,
                name="mixai",
            )
            for msg in msgs
        ]
    )


@prompt_command.assign("search")
async def search_prompt_cmd(keyword: Match[str], event):
    # 搜索prompt，返回结果
    if keyword:
        print(keyword, type(keyword))
        result = await search_prompt(keyword.result)
        if not result or len(result) == 0:
            await prompt_command.finish("没有找到相关提示")
        # msg = await display_results(result)
        user_id = str(event.user_id)
        """msg = [
            alconnaCustomNode(
                uid=user_id,
                content=f"[{prompt.uid}] {prompt.name}: {prompt.content}",
                name=prompt.name,
            )
            for prompt in result
        ]
        # 合并转发
        msg = alconnaReference(nodes=msg)
        """
        await prompt_command.send(
            await merge_forward([f"[{prompt.uid}] {prompt.name}: {prompt.content[:50]}..." for prompt in result])
        )
    else:
        await prompt_command.finish("请输入关键词")


@prompt_command.assign("set")
async def set_prompt_cmd(
    prompt_id: Match[str],
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    # 设置系统提示
    user_id = str(event.user_id)
    group_id = str(event.group_id)
    try:
        user = await get_user_or_create(user_id)
        await set_current_prompt(user_id, prompt_id.result)
        await prompt_command.send("设置成功")
    except Exception as e:
        await prompt_command.finish(f"设置失败：{e}")


@prompt_command.assign("self")
async def self_prompt_cmd(
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    # 查看自己的prompt
    user_id = str(event.user_id)
    try:
        user = await get_user_or_create(user_id)
        prompt = await get_current_prompt(user_id)
        await prompt_command.send(prompt.content)
    except Exception as e:
        await prompt_command.finish(f"获取失败：{e}")


@prompt_command.assign("show")
async def show_prompt_cmd(prompt_id: Match[str]):
    # 查看指定的prompt
    try:
        prompt = await show_prompt(prompt_id.result)
        await prompt_command.send(f"{prompt.name}：\n{prompt.content}")
    except Exception as e:
        await prompt_command.finish(f"获取失败：{e}")


@prompt_command.assign("create")
async def create_prompt_cmd(
    prompt: Match[str],
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    # 创建prompt
    user_id = str(event.user_id)
    try:
        prompt = await create_user_prompt(user_id, prompt.result)
        await prompt_command.send("自定义提示词已设置")
    except Exception as e:
        await prompt_command.finish(f"设置失败：{e}")


@prompt_command.assign("clear")
async def clear_prompt_cmd(
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    # 清除当前的prompt
    user_id = str(event.user_id)
    try:
        await clear_current_prompt(user_id)
        await prompt_command.send("已清除当前提示")
    except Exception as e:
        await prompt_command.finish(f"清除失败：{e}")


@prompt_command.assign("upload")
async def upload_prompt_cmd(
    prompt_content: Match[str],
    prompt_name: Match[str],
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    try:
        raw_msg = event.get_plaintext()
        prompt_content = re.search(r"upload (.+?)" + prompt_name.result, raw_msg)

        r = await upload_prompt(prompt_content.result, prompt_name.result)
        await prompt_command.send(f"上传提示词成功，id为{r}")
    except Exception as e:
        await prompt_command.finish(f"上传失败：{e}")


@Command("prompt upload <...content>").build(auto_send_output=True).handle()
async def _(content: UniMessage):
    # upload 之后的内容
    str_content = str(content)
    prompt_content = str_content
    last_space = prompt_content.rfind(" ")
    prompt_name = prompt_content[last_space + 1 :]
    prompt_content = prompt_content[:last_space]
    print(prompt_content, prompt_name)
    r = await upload_prompt(prompt_content, prompt_name)
    await prompt_command.finish(f"上传提示词成功，id为{r}")


@prompt_command.assign("help")
async def show_prompt_cmd():
    help_str = """
[prompt search <keyword>] 搜索prompt
[prompt set <prompt_id>] 设置系统提示
[prompt self] 查看自己的prompt
[prompt show <prompt_id>] 查看指定的prompt
[prompt create <prompt>] 自定义自己的提示词
[prompt clear] 清除当前的prompt
""".strip()
    await prompt_command.finish(help_str)


# 添加新的命令处理流式输出
stream_command = nonebot.on_startswith(msg="tstream", priority=1, block=True)


@stream_command.handle()
async def stream(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    str_prompt = event.get_plaintext().strip()
    str_prompt = str_prompt[6:].strip()  # 去掉"stream"前缀
    img_url = await find_image_url(event)
    user_id = str(event.user_id)

    # 获取配置的主机和端口
    host = getattr(config, "stream_public_host", "127.0.0.1")
    port = getattr(config, "stream_port", 8000)

    # 默认使用GPT模型
    resp = await OneAPI.handle_stream_command(
        user_id,
        str_prompt,
        "gpt-4o-mini-2024-07-18",
        img_url,
        server_host=host,
        server_port=port,
    )
    await MessageFactory(resp).send(at_sender=True)
    await stream_command.finish()
