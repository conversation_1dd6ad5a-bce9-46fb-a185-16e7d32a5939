from PIL import Image
import io
from ... import aiorequests

def get_img_url(event):
    img_url = None
    group_id, user_id =str(event.group_id), str(event.user_id) # event.get_session_id().split("_")
    for ev_msg in event.message:
        if ev_msg.type == "image":
            #print(ev_msg.data)
            img_base_url = "https://gchat.qpic.cn/gchatpic_new/1/"
            raw_id = ev_msg.data['path'].split("/")[-1].split(".")[0]
            raw_id = raw_id.upper()
            img_url = f"{img_base_url}{group_id}-{user_id}-{raw_id}/0?term=3"
            break
    return img_url

async def get_img_by_event(event):
    img_url = get_img_url(event)
    if img_url:
        return await download_image(img_url)
    else:
        return None

async def download_image(url = None):
    if url is None:
        return None
    resp = await aiorequests.get(url)
    img = Image.open(io.BytesIO(await resp.content)) # type: ignore
    return img