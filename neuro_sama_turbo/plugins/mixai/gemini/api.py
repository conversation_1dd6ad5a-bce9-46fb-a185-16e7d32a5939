import pathlib
import textwrap
import google.generativeai as genai
from google.generativeai import ChatSession

from .config import config, global_config
from PIL import Image

def to_markdown(text):
  text = text.replace('•', '  *')
  return text

genai.configure(api_key=config.google_api_key)

safety_settings = [
  {
    "category": "HARM_CATEGORY_HARASSMENT",
    "threshold": "BLOCK_ONLY_HIGH"
  },
  {
    "category": "HARM_CATEGORY_HATE_SPEECH",
    "threshold": "BLOCK_ONLY_HIGH"
  },
  {
    "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
    "threshold": "BLOCK_ONLY_HIGH"
  },
  {
    "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
    "threshold": "BLOCK_ONLY_HIGH"
  }
]

async def list_models():
    return [ model.name for model in genai.list_models() if 'generateContent' in model.supported_generation_methods ]

async def gen_only_text(model_name = 'gemini-pro', prompt = 'Please summarise this document: ...'):
    model = genai.GenerativeModel(model_name,safety_settings=safety_settings)
    resp = await model.generate_content_async(prompt)
    try:
        return to_markdown(resp.text)
    except Exception as e:
        return f"{type(e)}: {e}"

async def gen_with_vision(model_name = 'gemini-pro-vision', prompt = 'Please summarise this document: ...', image: Image.Image = None): # type: ignore
    model = genai.GenerativeModel(model_name,safety_settings=safety_settings)
    resp = await model.generate_content_async([prompt, image])
    try:
        return to_markdown(resp.text)
    except Exception as e:
        return f"{type(e)}: {e}"


class ChatSessionProxy(ChatSession):

    def __init__(self, model_name: str = 'gemini-pro', *args, **kwargs):
        self.model_name = model_name
        self.chat_session = genai.GenerativeModel(model_name,safety_settings=safety_settings).start_chat(*args, **kwargs)

    @property
    def history(self):
        return self.chat_session.history

    async def send_message_async(self, message):
        #if not isinstance(message, str) and self.model_name != 'gemini-pro-vision':
        #    self.switch_model('gemini-pro-vision')
        #elif isinstance(message, str) and self.model_name != 'gemini-pro':
        #    self.switch_model('gemini-pro')
        return await self.chat_session.send_message_async(message)
    
    def switch_model(self, model_name: str):
        self.model_name = model_name
        histroy = self.history
        self.chat_session = genai.GenerativeModel(model_name).start_chat(history=histroy)

chats: dict[str, ChatSessionProxy] = {}

async def create_session(model_name: str = 'gemini-pro', history: list = []):
    return ChatSessionProxy(model_name, history=history)

async def create_session_for_user(user_id: str, model_name: str = 'gemini-pro', history: list = []):
    chats[user_id] = await create_session(model_name, history)
    return chats[user_id]

async def delete_session_for_user(user_id: str):
    try:
        del chats[user_id]
    except KeyError:
        pass

async def chat_with_model(user_id: str, prompt: str, image: Image.Image | None = None):
    chat = chats.setdefault(user_id, await create_session())
    if image is None:
        resp = await chat.send_message_async(prompt)
        try:
            return str(to_markdown(resp.text))
        except Exception as e:
            await delete_session_for_user(user_id)
            return f"{type(e)}: {e}"
    else:
        resp = await gen_with_vision(prompt=prompt, image=image)
    return resp
        

async def get_chat_history_for_user(user_id: str):
    chat = chats.setdefault(user_id, await create_session())
    return chat.history

async def get_chat_history_for_user_as_markdown(user_id: str):
    history = await get_chat_history_for_user(user_id)
    return [ to_markdown(f"**{message.role}**: {message.parts[0].text}") for message in history ] 