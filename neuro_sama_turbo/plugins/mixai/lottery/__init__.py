import asyncio
import hashlib
import time
from typing import Union

import nonebot
from nonebot import get_driver, get_plugin_config
from nonebot.adapters.onebot.v11.event import GroupMessageEvent as V11GroupMessageEvent
from nonebot.adapters.onebot.v12.event import GroupMessageEvent as V12GroupMessageEvent
from nonebot.internal.adapter.bot import Bo<PERSON>
from nonebot.plugin import PluginMetadata
from nonebot_plugin_saa import MessageFactory, Text

from ..handle_prefix import handle_prefix
from .config import Config

__plugin_meta = PluginMetadata(
    name="lottery",
    description="",
    usage="",
    config=Config,
)

global_config = get_driver().config
config = get_plugin_config(Config)

start_lottery = "发起抽奖"
end_lottery = "结束抽奖"
join_lottery = "参加抽奖"
lottery_list = "抽奖列表"

start_command = nonebot.on_startswith(start_lottery, priority=1)
end_command = nonebot.on_startswith(end_lottery, priority=1)
join_command = nonebot.on_startswith(join_lottery, priority=1)

lottery_dict = {}
hash_to_lottery = {}


@start_command.handle()
async def handle_start(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent | RedGroupMessageEvent):
    args = handle_prefix(str(event.get_plaintext()), len(start_lottery)).strip()
    lottery_name = args
    group_id, user_id = str(event.group_id), str(event.user_id)  # event.get_session_id().split("_")
    hash_code = hashlib.md5(f"{group_id}_{user_id}_{lottery_name}_{time.time()}".encode()).hexdigest()
    lottery_dict[hash_code] = {
        "group_id": group_id,
        "user_id": user_id,
        "lottery_name": lottery_name,
        "lottery_list": [],
        "start_time": time.time(),
        "end_time": None,
    }
    hash_to_lottery[hash_code] = lottery_name
    await MessageFactory([Text(f"抽奖发起成功！\n抽奖名称：{lottery_name}\n抽奖hash：{hash_code[:8]}")]).send(
        at_sender=True
    )


@end_command.handle()
async def handle_end(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent | RedGroupMessageEvent):
    args = handle_prefix(str(event.get_plaintext()), len(end_lottery)).strip()
    group_id, user_id = str(event.group_id), str(event.user_id)  # event.get_session_id().split("_")
    hash_code = args
    # if hash_code not in hash_to_lottery:
    # 找到以hash_code开头的hash
    hash_code = None
    all_starts = []
    for hash_code_ in hash_to_lottery:
        if hash_code_.startswith(args):
            all_starts.append(hash_code_)
    if len(all_starts) == 0:
        await MessageFactory([Text("抽奖不存在！")]).send(at_sender=True)
        return
    if len(all_starts) > 1:
        # 遍历所有以hash_code开头的hash，输出所有抽奖
        all_info = []
        for hash_code_ in all_starts:
            lottery_name = hash_to_lottery[hash_code_]
            lottery = lottery_dict[hash_code_]
            all_info.append(
                f"抽奖名称：{lottery_name}\n抽奖hash：{hash_code_[:8]}\n抽奖发起者：{lottery['user_id']}\n抽奖人数：{len(lottery['lottery_list'])}"
            )
        await MessageFactory([Text(f"抽奖哈希不唯一！\n{all_info}")]).send(at_sender=True)
    else:
        hash_code = all_starts[0]
    lottery_name = hash_to_lottery[hash_code]
    lottery = lottery_dict[hash_code]
    if lottery["user_id"] != user_id:
        await MessageFactory([Text("你不是抽奖发起者！")]).send(at_sender=True)
        return
    # 随机抽取一个人
    if len(lottery["lottery_list"]) == 0:
        await MessageFactory([Text("抽奖人数为0！")]).send(at_sender=True)
        return
    else:
        lottery["end_time"] = time.time()
        lottery["winner"] = lottery["lottery_list"][int(time.time()) % len(lottery["lottery_list"])]
        await MessageFactory(
            [
                Text(
                    f"抽奖结束！\n抽奖名称：{lottery_name}\n抽奖hash：{hash_code[:8]}\n抽奖发起者：{lottery['user_id']}\n抽奖人数：{len(lottery['lottery_list'])}\n抽奖获奖者：{lottery['winner']}"
                )
            ]
        ).send(at_sender=True)


@join_command.handle()
async def handle_join(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent | RedGroupMessageEvent):
    args = handle_prefix(str(event.get_plaintext()), len(join_lottery)).strip()
    group_id, user_id = str(event.group_id), str(event.user_id)  # event.get_session_id().split("_")
    hash_code = args
    hash_code = None
    all_starts = []
    for hash_code_ in hash_to_lottery:
        if hash_code_.startswith(args):
            all_starts.append(hash_code_)
    if len(all_starts) == 0:
        await MessageFactory([Text("抽奖不存在！")]).send(at_sender=True)
        return
    if len(all_starts) > 1:
        # 遍历所有以hash_code开头的hash，输出所有抽奖
        all_info = []
        for hash_code_ in all_starts:
            lottery_name = hash_to_lottery[hash_code_]
            lottery = lottery_dict[hash_code_]
            all_info.append(
                f"抽奖名称：{lottery_name}\n抽奖hash：{hash_code_[:8]}\n抽奖发起者：{lottery['user_id']}\n抽奖人数：{len(lottery['lottery_list'])}"
            )
        await MessageFactory([Text(f"抽奖哈希不唯一！\n{all_info}")]).send(at_sender=True)
    else:
        hash_code = all_starts[0]
    lottery_name = hash_to_lottery[hash_code]
    lottery = lottery_dict[hash_code]
    if lottery["end_time"] is not None:
        await MessageFactory([Text("抽奖已经结束！")]).send(at_sender=True)
        return
    if user_id in lottery["lottery_list"]:
        await MessageFactory([Text("你已经参加过抽奖了！")]).send(at_sender=True)
        return
    lottery["lottery_list"].append(user_id)
    await MessageFactory(
        [
            Text(
                f"参加抽奖成功！\n抽奖名称：{lottery_name}\n发起者：{lottery['user_id']}\n抽奖人数：{len(lottery['lottery_list'])}"
            )
        ]
    ).send(at_sender=True)
