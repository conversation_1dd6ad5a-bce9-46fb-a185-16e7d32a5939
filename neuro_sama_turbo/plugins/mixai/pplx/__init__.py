import asyncio
import json
import os
import time

from nonebot import get_driver, get_plugin_config
from nonebot_plugin_apscheduler import scheduler
from nonebot_plugin_saa import Text

from ..oneapi.db.db_mongo import get_current_prompt
from . import api
from .config import Config

path = os.path.dirname(__file__)

driver = get_driver()
global_config = driver.config
config = get_plugin_config(Config)

history = {}
locks = {}


# 每分钟备份一次历史记录
@scheduler.scheduled_job("interval", seconds=60)
async def backup_history():
    global history
    with open(os.path.join(path, "history.json"), "w", encoding="utf-8") as f:
        f.write(json.dumps(history, indent=4, ensure_ascii=False))


# 启动时加载历史记录
@driver.on_startup
async def load_history():
    global history
    if os.path.exists(os.path.join(path, "history.json")):
        with open(os.path.join(path, "history.json"), encoding="utf-8") as f:
            history = json.loads(f.read())


class PplxAPI:
    @staticmethod
    async def get_lock(uid, model_name):
        global locks
        locks.setdefault(uid, {})
        locks[uid].setdefault(model_name, asyncio.Lock())
        return locks[uid][model_name]

    @staticmethod
    async def get_history(uid, model_name):
        global history
        history.setdefault(uid, {})
        history[uid].setdefault(model_name, {"data": [], "time": time.time()})
        # 如果超过10分钟，清空历史记录
        if time.time() - history[uid][model_name]["time"] > 60 * 10:
            await PplxAPI.clear_history(uid, model_name)
            return await PplxAPI.get_history(uid, model_name)
        return history[uid][model_name]

    @staticmethod
    async def add_history(uid, model_name, content, role: str = "user"):
        global history
        current_history = await PplxAPI.get_history(uid, model_name)
        current_history["data"].append({"role": role, "content": content})
        current_history["time"] = time.time()
        return current_history

    @staticmethod
    async def clear_history(uid, model_name):
        global history
        if uid in history and model_name in history[uid]:
            history[uid][model_name]["data"] = []
            history[uid][model_name]["time"] = time.time()

    @staticmethod
    async def handle_base_command(uid, prompt, model_name="sonar-reasoning"):
        lock = await PplxAPI.get_lock(uid, model_name)
        async with lock:
            prompt = prompt.strip()
            if prompt in ["exit", "退出"]:
                return await PplxAPI.clear_history(uid, model_name)
            elif prompt in ["history", "历史记录"]:
                # 该接口暂时不可用
                return [Text("接口暂时不可用")]
                return await PplxAPI.get_history(uid, model_name)
            elif prompt:
                messages = await PplxAPI.add_history(uid, model_name, prompt, "user")
                prompts = messages["data"]
                try:
                    current_prompt = await get_current_prompt(uid)
                    prompts = [{"role": "system", "content": current_prompt.content}] + prompts
                except Exception as e:
                    print(e)
                print(prompts)
                resp = await api.get_response(prompts, model_name)
                await PplxAPI.add_history(uid, model_name, resp.choices[0].message.content, "assistant")
                return [Text(await api.build_message_from_response(resp))]
            else:
                return [Text("请输入问题")]
