from pydantic import BaseModel
from nonebot import get_driver, get_plugin_config


class Config(BaseModel):
    """Plugin Config Here"""

    pplx_api_key: str = ""
    #    openai_org_id: str
    pplx_base_url: str = "https://api.perplexity.ai"  # chat/completions


global_config = get_driver().config
config = get_plugin_config(Config)


# 仅用于测试
config.pplx_api_key = "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"

from openai import AsyncOpenAI

client = AsyncOpenAI(
    api_key=config.pplx_api_key,
    #    organization=config.openai_org_id
    base_url=config.pplx_base_url,
)
