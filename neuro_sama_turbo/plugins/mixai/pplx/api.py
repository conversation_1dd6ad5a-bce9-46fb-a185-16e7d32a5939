from .config import config, client
from pydantic import BaseModel, Extra
from openai.types.chat import ChatCompletion


class Message(BaseModel):
    """Message Model Here"""

    role: str  # system | user | assistant
    content: str


class CustomChatCompletion(ChatCompletion):
    """Custom Chat Completion"""

    citations: list[str] = []


class ChatCompletion(CustomChatCompletion): ...


async def get_response(prompt, model_name: str = "sonar-reasoning") -> ChatCompletion:
    """get response"""
    completion = await client.chat.completions.create(model=model_name, messages=prompt)
    return completion


async def build_message_from_response(response: ChatCompletion) -> str:
    """build message from response"""
    bot_message = ""
    for choice in response.choices:
        bot_message += choice.message.content
    citations = ""
    for i, citation in enumerate(response.citations):
        citations += f"{i + 1}. {citation}\n"

    return f"""{bot_message}\n\n
Citations:\n{citations}""".strip()
