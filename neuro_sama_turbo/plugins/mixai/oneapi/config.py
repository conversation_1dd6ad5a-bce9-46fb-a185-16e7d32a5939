from nonebot import get_driver, get_plugin_config
from pydantic import BaseModel


class Config(BaseModel):
    """Plugin Config Here"""

    openai_api_key: str = ""
    #    openai_org_id: str
    openai_base_url: str = "https://oneapi.nyanners.moe/v1"


global_config = get_driver().config
config = get_plugin_config(Config)


# 仅用于测试
config.openai_api_key = "sk-7Eepd7CCumY5StmE8cE0A3F67d5d429cB78c997d4b30437a"

from openai import AsyncOpenAI

sa_client = AsyncOpenAI(
    api_key="Yx4jvmh1ZvbNX5LaEsvs",
    base_url="https://ollama-sa.lirica.cn/v1",
)
sa_qwen_client = AsyncOpenAI(
    api_key="sa",
    base_url="http://100.122.0.45:7888/v1",
)
ds_client = AsyncOpenAI(
    api_key="sk-033a4e41eb5b442e81788f0376847823",
    base_url="https://api.deepseek.com/v1",
)
qwen_client = AsyncOpenAI(
    api_key="7221ae48-ad40-48f9-8e38-7ac6bccbaf93",
    base_url="https://api-inference.modelscope.cn/v1",
)
jinaai_client = AsyncOpenAI(
    api_key="jina_4bed47008aef4361b7a4598b774ca13cG-OhkpW355IgO8JaomK1-Ma1ZPQl",
    base_url="https://deepsearch.jina.ai/v1",
)
blt_client = AsyncOpenAI(
    api_key="sk-KMnrtH4NAZgFJYQkD8Eb3dC8Eb2846C98bC32eD00085Df38",
    base_url="https://api.bltcy.ai/v1",
)
grok_client = blt_client
mm_client = AsyncOpenAI(
    api_key="**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    base_url="https://api.minimaxi.com/v1",
)
clients = {
    "openai": AsyncOpenAI(
        api_key=config.openai_api_key,
        #    organization=config.openai_org_id
        base_url=config.openai_base_url,
    ),
    "sa": sa_client,
    "ds": ds_client,
    "qwen": qwen_client,
    "sa_qwen": sa_qwen_client,
    "jinaai": jinaai_client,
    "grok": grok_client,
    "blt": blt_client,
    "mm": mm_client,
}
from smolagents import CodeAgent, DuckDuckGoSearchTool, OpenAIServerModel


def from_openai_client_to_smolagent(client: AsyncOpenAI, model: str) -> CodeAgent:
    _model = OpenAIServerModel(
        model_id=model,
        api_base=str(client.base_url),
        api_key=client.api_key,
    )
    agent = CodeAgent(
        model=_model,
        tools=[DuckDuckGoSearchTool()],
    )

    return agent


def run_code(code: str, client: str, model: str) -> str:
    agent = from_openai_client_to_smolagent(clients[client], model)
    response = agent.run(code)
    return response
