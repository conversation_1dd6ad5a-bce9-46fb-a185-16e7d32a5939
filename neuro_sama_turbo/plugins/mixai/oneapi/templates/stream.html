<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <title>{{title}} - {{model_name}}</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                        mono: ['JetBrains Mono', 'monospace'],
                    },
                    colors: {
                        primary: {
                            50: '#eef9ff',
                            100: '#dcf3ff',
                            200: '#b3e7ff',
                            300: '#78d6ff',
                            400: '#36bfff',
                            500: '#0ca5ff',
                            600: '#0087db',
                            700: '#006eb2',
                            800: '#005b92',
                            900: '#074c78',
                            950: '#032f4d',
                        },
                        secondary: {
                            50: '#f6f7f9',
                            100: '#eceef2',
                            200: '#d5dae2',
                            300: '#b0bcc9',
                            400: '#8598ac',
                            500: '#657b91',
                            600: '#506278',
                            700: '#425062',
                            800: '#394352',
                            900: '#333b47',
                            950: '#21252d',
                        }
                    },
                    boxShadow: {
                        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
                        'soft-lg': '0 10px 30px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                        'inner-soft': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)'
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-out forwards',
                        'slide-up': 'slideUp 0.3s ease-out forwards',
                        'pulse-subtle': 'pulseSubtle 2s infinite',
                        'gradient': 'gradient 8s ease infinite',
                        'bounce': 'bounce 2s infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(10px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        pulseSubtle: {
                            '0%, 100%': { opacity: '1' },
                            '50%': { opacity: '0.7' },
                        },
                        gradient: {
                            '0%': { backgroundPosition: '0% 50%' },
                            '50%': { backgroundPosition: '100% 50%' },
                            '100%': { backgroundPosition: '0% 50%' },
                        },
                        bounce: {
                            '0%, 20%, 50%, 80%, 100%': { transform: 'translateY(0)' },
                            '40%': { transform: 'translateY(-8px)' },
                            '60%': { transform: 'translateY(-4px)' },
                        },
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .animate-spin {
            animation: spin 1.2s linear infinite;
        }

        .markdown h1 {
            @apply text-3xl font-bold my-6 text-gray-800 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2;
        }

        .markdown h2 {
            @apply text-2xl font-bold mt-6 mb-4 text-gray-800 dark:text-gray-100;
        }

        .markdown h3 {
            @apply text-xl font-semibold mt-5 mb-3 text-gray-800 dark:text-gray-100;
        }

        .markdown h4 {
            @apply text-lg font-semibold mt-4 mb-2 text-gray-800 dark:text-gray-200;
        }

        .markdown p {
            @apply my-4 leading-relaxed text-gray-700 dark:text-gray-300;
        }

        .markdown ul {
            @apply list-disc pl-5 my-4 space-y-2 text-gray-700 dark:text-gray-300;
        }

        .markdown ol {
            @apply list-decimal pl-5 my-4 space-y-2 text-gray-700 dark:text-gray-300;
        }

        .markdown li {
            @apply mb-1;
        }

        .markdown pre {
            @apply bg-gray-100 dark:bg-gray-800/80 p-4 rounded-lg my-4 overflow-x-auto border border-gray-200 dark:border-gray-700 shadow-sm;
        }

        .markdown code {
            @apply font-mono text-sm bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded text-gray-800 dark:text-gray-200 border border-gray-200 dark:border-gray-700/50;
        }

        .markdown pre code {
            @apply bg-transparent border-none p-0 text-gray-800 dark:text-gray-200;
        }

        .markdown blockquote {
            @apply border-l-4 border-primary-400 dark:border-primary-600/70 bg-gray-50 dark:bg-gray-800/50 pl-4 py-2 pr-2 my-4 text-gray-700 dark:text-gray-300 rounded-sm;
        }

        .markdown a {
            @apply text-primary-600 dark:text-primary-400 hover:underline font-medium;
        }

        .markdown table {
            @apply min-w-full border border-gray-300 dark:border-gray-700 my-4 rounded-lg overflow-hidden;
        }

        .markdown th {
            @apply bg-gray-100 dark:bg-gray-800 text-left py-2 px-4 font-semibold text-gray-700 dark:text-gray-300 border-b border-gray-300 dark:border-gray-700;
        }

        .markdown td {
            @apply py-2 px-4 border-b border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300;
        }

        .markdown img {
            @apply max-w-full my-4 rounded-lg;
        }

        .markdown hr {
            @apply my-6 border-t border-gray-300 dark:border-gray-700;
        }

        .bg-gradient {
            background: linear-gradient(135deg, #0ca5ff, #005b92, #032f4d);
            background-size: 200% 200%;
            animation: gradient 15s ease infinite;
        }

        .glassmorphism {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .typing-indicator::after {
            content: '|';
            animation: blink 1s step-end infinite;
        }

        @keyframes blink {

            from,
            to {
                opacity: 1;
            }

            50% {
                opacity: 0;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: transparent;
        }

        ::-webkit-scrollbar-thumb {
            @apply bg-gray-400/50 dark:bg-gray-700/70 rounded-full;
        }

        ::-webkit-scrollbar-thumb:hover {
            @apply bg-gray-500/70 dark:bg-gray-600;
        }

        .thinking-container {
            @apply bg-yellow-50 dark:bg-gray-800/60 border border-yellow-200 dark:border-gray-700 rounded-lg my-4 overflow-hidden transition-all duration-300;
        }

        .thinking-header {
            @apply flex items-center justify-between px-4 py-2 bg-yellow-100/70 dark:bg-gray-700/70 border-b border-yellow-200 dark:border-gray-700 cursor-pointer;
        }

        .thinking-content {
            @apply p-4 text-gray-700 dark:text-gray-300 font-mono text-sm whitespace-pre-wrap overflow-auto max-h-96;
        }

        .thinking-collapsed .thinking-content {
            @apply hidden;
        }
    </style>
</head>

<body
    class="bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 h-screen flex flex-col items-center overflow-hidden transition-all duration-300 font-sans">
    <!-- 使用flex-1和overflow-auto让内容区域可滚动而非整个页面 -->
    <div class="w-full max-w-4xl h-full flex flex-col animate-fade-in py-4 px-4">
        <!-- 顶部导航栏 -->
        <div class="flex justify-between items-center mb-4 flex-shrink-0">
            <div class="flex items-center gap-2">
                <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center shadow-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                    </svg>
                </div>
                <span class="font-semibold text-xl text-gray-700 dark:text-gray-200">AI 对话</span>
            </div>

            <button id="theme-toggle"
                class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-800 transition-colors">
                <!-- 月亮图标 (深色模式) -->
                <svg xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-700 dark:text-gray-300 hidden dark:block" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                </svg>
                <!-- 太阳图标 (亮色模式) -->
                <svg xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-700 dark:text-gray-300 block dark:hidden" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
            </button>
        </div>

        <!-- 滚动区域 -->
        <div class="flex-1 overflow-y-auto pr-1">
            <!-- 标题卡片 -->
            <div class="bg-gradient rounded-xl shadow-soft-lg mb-4 overflow-hidden animate-slide-up">
                <div class="p-6 text-white relative">
                    <div class="absolute right-0 top-0 p-4 opacity-10">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5"
                                d="M9.663 17h4.673M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </div>
                    <h1 class="text-2xl font-bold mb-1">{{title}}</h1>
                    <div class="flex items-center mt-3">
                        <div class="flex items-center bg-white/10 rounded-full px-3 py-1 text-sm glassmorphism">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            <span>{{model_name}}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 提示词卡片 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-soft mb-4 border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 hover:shadow-soft-lg animate-slide-up"
                style="animation-delay: 0.1s;">
                <div
                    class="flex items-center justify-between px-6 py-4 border-b border-gray-100 dark:border-gray-700/70">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary-500 mr-2" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                        </svg>
                        <h2 class="text-lg font-medium text-gray-800 dark:text-gray-200">用户提问</h2>
                    </div>
                </div>
                <div class="px-6 py-4">
                    <pre
                        class="whitespace-pre-wrap font-sans text-sm text-gray-700 dark:text-gray-300 leading-relaxed">{{prompt}}</pre>
                </div>
            </div>

            <!-- 回复卡片 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-soft mb-4 border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 hover:shadow-soft-lg animate-slide-up"
                style="animation-delay: 0.2s;">
                <div
                    class="flex items-center justify-between px-6 py-4 border-b border-gray-100 dark:border-gray-700/70">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary-500 mr-2" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0112 21a48.309 48.309 0 01-8.135-.687c-1.718-.293-2.3-2.379-1.067-3.61L5 14.5" />
                        </svg>
                        <h2 class="text-lg font-medium text-gray-800 dark:text-gray-200">AI 回答</h2>
                    </div>
                </div>
                <div class="px-6 py-4 relative">
                    <!-- 响应内容容器 -->
                    <div class="max-h-[400px] overflow-y-auto relative" id="response-container">
                        <!-- 顶部渐变阴影指示器 -->
                        <div id="scroll-top-indicator"
                            class="absolute top-0 left-0 right-1 h-6 bg-gradient-to-b from-white to-transparent dark:from-gray-800 dark:to-transparent opacity-0 transition-opacity pointer-events-none z-10">
                        </div>

                        <!-- 回答内容 -->
                        <div id="response"
                            class="markdown text-gray-700 dark:text-gray-300 leading-relaxed min-h-[100px] pr-2">
                        </div>

                        <!-- 底部渐变阴影指示器 -->
                        <div id="scroll-bottom-indicator"
                            class="absolute bottom-0 left-0 right-1 h-8 bg-gradient-to-t from-white to-transparent dark:from-gray-800 dark:to-transparent opacity-0 transition-opacity pointer-events-none z-10">
                        </div>
                    </div>

                    <!-- 滚动提示图标 -->
                    <div id="scroll-icon"
                        class="absolute bottom-3 right-3 h-8 w-8 bg-white/80 dark:bg-gray-700/80 text-gray-600 dark:text-gray-300 rounded-full flex items-center justify-center shadow-md cursor-pointer opacity-0 transition-opacity z-20 animate-bounce">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                </div>
            </div>

            <!-- 加载指示器 -->
            <div id="loading"
                class="flex items-center justify-center p-4 mb-4 bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 transition-all">
                <div class="relative">
                    <div class="h-10 w-10 rounded-full border-t-2 border-b-2 border-primary-500 animate-spin"></div>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="h-6 w-6 rounded-full bg-white dark:bg-gray-800"></div>
                    </div>
                </div>
                <span class="ml-3 text-gray-600 dark:text-gray-400 font-medium">正在生成回答...</span>
            </div>

            <!-- 页脚 -->
            <div class="text-center text-xs text-gray-500 dark:text-gray-400 mt-4 mb-4 animate-fade-in"
                style="animation-delay: 0.3s;">
                <p>© 2025 · 由AI强力驱动</p>
            </div>
        </div>
    </div>

    <script>
        // 主题切换功能
        const themeToggle = document.getElementById('theme-toggle');
        const htmlElement = document.documentElement;

        // 检测系统主题和本地存储的主题
        const systemDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        const savedTheme = localStorage.getItem('theme');

        // 根据存储的主题或系统主题设置初始主题
        if (savedTheme === 'dark' || (savedTheme === null && systemDarkMode)) {
            htmlElement.classList.add('dark');
        } else {
            htmlElement.classList.remove('dark');
        }

        // 主题切换事件
        themeToggle.addEventListener('click', () => {
            htmlElement.classList.toggle('dark');
            localStorage.setItem('theme', htmlElement.classList.contains('dark') ? 'dark' : 'light');
        });

        // Markdown配置
        marked.setOptions({
            highlight: function (code, lang) {
                if (lang && hljs.getLanguage(lang)) {
                    try {
                        return hljs.highlight(code, { language: lang }).value;
                    } catch (err) { }
                }
                return hljs.highlightAuto(code).value;
            },
            breaks: true
        });

        // 替换现有的renderMarkdown函数及其后续代码

        // 高级Markdown渲染函数
        function renderMarkdown(text) {
            return marked.parse(text);
        }

        // 处理思考过程的函数
        function processThinkingContent(content) {
            let displayContent = content;
            let thinkingContent = null;

            // 查找思考内容
            const thinkRegex = /<think>([\s\S]*?)<\/think>/;
            const match = content.match(thinkRegex);

            if (match) {
                thinkingContent = match[1].trim();
                // 从主内容中移除思考部分
                displayContent = content.replace(/<think>[\s\S]*?<\/think>/, '');
            }

            // 返回处理后的内容
            return {
                displayContent: displayContent || "AI正在思考中...",
                thinkingContent: thinkingContent
            };
        }

        // 更新UI的函数
        // 更新UI的函数
        function updateResponseUI(content) {
            const { displayContent, thinkingContent } = processThinkingContent(content);

            // 清除现有内容
            responseElement.innerHTML = '';

            // 如果有思考内容，添加思考区块
            if (thinkingContent) {
                const thinkingContainer = document.createElement('div');
                thinkingContainer.className = 'thinking-container thinking-collapsed';

                const thinkingHeader = document.createElement('div');
                thinkingHeader.className = 'thinking-header';
                thinkingHeader.innerHTML = `
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-yellow-600 dark:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <span class="font-medium">AI 思考过程</span>
            </div>
            <div class="toggle-icon">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
            </div>
        `;

                thinkingHeader.addEventListener('click', () => {
                    thinkingContainer.classList.toggle('thinking-collapsed');
                    const toggleIcon = thinkingHeader.querySelector('.toggle-icon svg');
                    if (thinkingContainer.classList.contains('thinking-collapsed')) {
                        toggleIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />';
                    } else {
                        toggleIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />';
                    }
                });

                const thinkingContentElem = document.createElement('div');
                thinkingContentElem.className = 'thinking-content';
                thinkingContentElem.textContent = thinkingContent;

                thinkingContainer.appendChild(thinkingHeader);
                thinkingContainer.appendChild(thinkingContentElem);
                responseElement.appendChild(thinkingContainer);
            }

            // 添加主要回复内容
            const mainContent = document.createElement('div');
            mainContent.className = 'main-response';
            mainContent.innerHTML = renderMarkdown(displayContent);
            responseElement.appendChild(mainContent);

            // 语法高亮
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightBlock(block);
            });
        }
        const sessionId = "{{session_id}}";
        const responseElement = document.getElementById('response');
        const loadingElement = document.getElementById('loading');

        let final = "";
        try {
            // 首先用atob解码base64
            const decodedBytes = atob("{{message_base64}}");

            // 然后将二进制数据转换为Uint8Array
            const bytes = new Uint8Array(decodedBytes.length);
            for (let i = 0; i < decodedBytes.length; i++) {
                bytes[i] = decodedBytes.charCodeAt(i);
            }

            // 最后使用TextDecoder将Uint8Array解码为UTF-8文本
            final = new TextDecoder("utf-8").decode(bytes);
        } catch (e) {
            console.error("解码消息时出错:", e);
            final = "加载消息失败，请刷新页面重试。";
        }

        if (final) {
            updateResponseUI(final);
        }

        // 检查是否已完成
        {% if completed %}
        loadingElement.classList.add('hidden');
        {% else %}
        // 建立WebSocket连接
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const ws = new WebSocket(`${protocol}//${window.location.host}/ws/${sessionId}`);

        // 全局响应文本
        let fullResponse = final || "";
        ws.onmessage = function (event) {
            const data = JSON.parse(event.data);
            if (data.type === 'message') {
                console.log("Received chunk:", data.content);
                fullResponse = data.content;

                // 更新整个响应
                updateResponseUI(fullResponse);

                // 检查是否需要更新滚动指示器
                if (typeof updateScrollIndicators === 'function') {
                    updateScrollIndicators();
                }

                // 只在容器内自动滚动到底部
                const responseContainer = document.getElementById('response-container');
                // 检查用户是否正在查看底部内容
                const isScrolledToBottom = responseContainer.scrollHeight - responseContainer.clientHeight <= responseContainer.scrollTop + 50;

                if (isScrolledToBottom) {
                    responseContainer.scrollTop = responseContainer.scrollHeight;
                }
            } else if (data.type === 'complete') {
                console.log("Stream completed");
                loadingElement.classList.add('hidden');
                ws.close();
            }
        };
        ws.onopen = function () {
            console.log("WebSocket connection established");
        };

        ws.onclose = function () {
            console.log("WebSocket connection closed");
            loadingElement.classList.add('hidden');
        };

        ws.onerror = function (error) {
            console.error("WebSocket error:", error);
            loadingElement.innerHTML = '<div class="text-red-500">连接错误，请刷新页面重试</div>';
        };
        {% endif %}

        document.addEventListener('DOMContentLoaded', function () {
            const responseContainer = document.getElementById('response-container');
            const topIndicator = document.getElementById('scroll-top-indicator');
            const bottomIndicator = document.getElementById('scroll-bottom-indicator');
            const scrollIcon = document.getElementById('scroll-icon');
            const styleTag = document.createElement('style');
            styleTag.textContent = `
        .thinking-container {
            background-color: rgba(254, 240, 199, 0.5);
            border: 1px solid rgba(252, 211, 77, 0.3);
            border-radius: 0.5rem;
            margin: 1rem 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .dark .thinking-container {
            background-color: rgba(30, 41, 59, 0.6);
            border-color: rgba(71, 85, 105, 0.7);
        }
        
        .thinking-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.5rem 1rem;
            background-color: rgba(254, 240, 199, 0.7);
            border-bottom: 1px solid rgba(252, 211, 77, 0.3);
            cursor: pointer;
        }
        
        .dark .thinking-header {
            background-color: rgba(51, 65, 85, 0.7);
            border-color: rgba(71, 85, 105, 0.7);
        }
        
        .thinking-content {
            padding: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
            color: rgb(55, 65, 81);
            white-space: pre-wrap;
            overflow: auto;
            max-height: 24rem;
            display: block;
        }
        
        .dark .thinking-content {
            color: rgb(209, 213, 219);
        }
        
        .thinking-collapsed .thinking-content {
            display: none !important;
        }
    `;
            document.head.appendChild(styleTag);
            function updateScrollIndicators() {
                // 检查是否有足够内容需要滚动
                const isScrollable = responseContainer.scrollHeight > responseContainer.clientHeight;

                // 检查滚动位置
                const isScrolledToTop = responseContainer.scrollTop <= 10;
                const isScrolledToBottom = responseContainer.scrollHeight - responseContainer.clientHeight <= responseContainer.scrollTop + 10;

                // 更新顶部指示器
                if (isScrollable && !isScrolledToTop) {
                    topIndicator.style.opacity = "1";
                } else {
                    topIndicator.style.opacity = "0";
                }

                // 更新底部指示器
                if (isScrollable && !isScrolledToBottom) {
                    bottomIndicator.style.opacity = "1";
                } else {
                    bottomIndicator.style.opacity = "0";
                }

                // 更新滚动图标
                if (isScrollable && !isScrolledToBottom) {
                    scrollIcon.style.opacity = "0.8";
                } else {
                    scrollIcon.style.opacity = "0";
                }
            }

            // 当内容更新或用户滚动时检查
            const observer = new MutationObserver(updateScrollIndicators);
            observer.observe(responseElement, { childList: true, subtree: true });

            responseContainer.addEventListener('scroll', updateScrollIndicators);

            // 首次加载时检查
            setTimeout(updateScrollIndicators, 500);

            // 点击滚动图标时滚动到底部
            scrollIcon.addEventListener('click', function () {
                responseContainer.scrollTo({
                    top: responseContainer.scrollHeight,
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>

</html>