from logging import getLogger

logger = getLogger(__name__)
from bson import CodecOptions, ObjectId
from motor.motor_asyncio import AsyncIOMotorClient
from pydantic import BaseModel, Field
import pytz

shanghai_tz = pytz.timezone("Asia/Shanghai")
codec_opt = CodecOptions(tz_aware=True, tzinfo=shanghai_tz)

mongo_host = "localhost"  # global_config.mongo_host
mongo_user = "root"  # global_config.mongo_user
mongo_pass = "passw0rd@zero"  # global_config.mongo_pass

import urllib.parse

mongo_user = urllib.parse.quote_plus(mongo_user)
# mongo_pass = urllib.parse.quote_plus(mongo_pass)

client = AsyncIOMotorClient(f"mongodb://{mongo_host}:27017/")
db = client.gpt_prompt
collection = db.prompt
user_collection = db.user


class GPTPrompt(BaseModel):
    uid: str  # (bson.ObjectId())
    name: str
    lng: str = Field("zh", alias="language")
    description: str | None = None
    content: str
    main_category: str | None = None
    author: str | None = None
    logo: str | None = None
    url: str | None = None
    search: str | None = None


class User(BaseModel):
    uid: str
    current_prompt: GPTPrompt | None = None
    history: list[GPTPrompt] = []


# 定义text索引
async def create_index():
    # 枚举字段, 从prompt的名称，内容，主分类，描述中检索
    text_fields = [
        "search",
    ]
    try:
        await collection.create_index([(field, "text") for field in text_fields])
    except Exception as e:
        logger.error(f"创建索引失败: {e}")
    # 设置uid为唯一索引
    try:
        await collection.create_index("uid", unique=True)
    except Exception as e:
        logger.error(f"创建索引失败: {e}")


async def insert_one(prompt: GPTPrompt):
    prompt.search = " ".join(
        [
            prompt.name,
            prompt.content,
            prompt.main_category or "",
            prompt.description or "",
        ]
    )
    result = await collection.insert_one(prompt.dict())
    return result.inserted_id


async def upload_prompt(
    prompt_content: str,
    prompt_name: str,
    prompt_description: str = "",
    prompt_main_category: str = "",
    prompt_author: str = "",
    prompt_logo: str = "",
    prompt_url: str = "",
):
    id = str(ObjectId())
    prompt = GPTPrompt(
        uid=id,
        name=prompt_name,
        lng="zh",
        content=prompt_content,
        description=prompt_description,
        main_category=prompt_main_category,
        author=prompt_author,
        logo=prompt_logo,
        url=prompt_url,
    )
    await insert_one(prompt)
    return id


import jieba


async def fenci(text: str):
    words = jieba.lcut_for_search(text)
    return " ".join(words)


async def search_prompt(query: str, limit: int = 10):
    cursor = collection.find({"$text": {"$search": await fenci(query)}})
    return [GPTPrompt(**item) for item in (await cursor.to_list(length=limit))]


async def show_prompt(prompt_id: str):
    prompt = await collection.find_one({"uid": prompt_id})
    if prompt:
        return GPTPrompt(**prompt)
    raise ValueError(f"Prompt with id {prompt_id} not found")


"""
async def import_data():
    # 清空数据
    await collection.delete_many({})
    # 先定义索引
    # await create_index()
    # 插入数据
    import json5, os
    path = os.path.join(os.path.dirname(__file__), 'prompts.json5')
    with open(path, 'r', encoding='utf-8') as f:
        prompts = json5.load(f)
    # 如果没有uid，就生成一个
    # 所有重复的uid需要重新生成
    uid_set = set()
    for prompt in prompts:
        if 'uid' in prompt:
            if prompt['uid'] in uid_set:
                prompt['uid'] = str(ObjectId())
            else:
                uid_set.add(prompt['uid'])
    for prompt in prompts:
        if 'uid' not in prompt:
            prompt['uid'] = str(ObjectId())
    prompts = [GPTPrompt(**prompt) for prompt in prompts]
    # 批量插入
    result = await collection.insert_many([prompt.dict() for prompt in prompts])
"""

from tabulate import tabulate


async def display_results(prompts: list[GPTPrompt]):
    headers = ["UID", "Name", "Description", "Content Preview"]
    table_data = []

    for prompt in prompts:
        # 简化内容以适应表格宽度
        content_preview = (prompt.content[:20] + "...") if len(prompt.content) > 20 else prompt.content
        description = (
            (prompt.description[:20] + "...")
            if prompt.description and len(prompt.description) > 20
            else prompt.description
        )
        row = [prompt.uid, prompt.name, content_preview]
        table_data.append(row)

    return tabulate(table_data, headers=headers, tablefmt="grid")


# 创建用户
async def create_user(uid: str):
    user = User(uid=uid)
    if not await user_collection.find_one({"uid": uid}):
        await user_collection.insert_one(user.dict())


# 获取用户
async def get_user_or_create(uid: str) -> User:
    await create_user(uid)
    user = await user_collection.find_one({"uid": uid})
    return User(**user)


# 根据id为用户设置当前的prompt
async def set_current_prompt(uid: str, prompt_id: str):
    # 检查prompt_id是否存在
    prompt = await collection.find_one({"uid": prompt_id})
    if not prompt:
        raise ValueError(f"Prompt with id {prompt_id} not found")
    await user_collection.update_one({"uid": uid}, {"$set": {"current_prompt": prompt}})
    return GPTPrompt(**prompt)


# 清除用户的当前prompt
async def clear_current_prompt(uid: str):
    # unset操作符用于删除字段，而不是设置为null
    await user_collection.update_one({"uid": uid}, {"$unset": {"current_prompt": ""}})


# 用户自己定义的prompt
async def create_user_prompt(uid: str, prompt: GPTPrompt | str):
    print(prompt, type(prompt))
    if type(prompt) == str:
        prompt = GPTPrompt(
            uid=str(ObjectId()),
            name=prompt[:10],
            lng="zh",
            content=prompt,
        )
    await user_collection.update_one({"uid": uid}, {"$set": {"current_prompt": prompt.dict()}})
    return prompt


# 获取用户的当前prompt
async def get_current_prompt(uid: str) -> GPTPrompt:
    user = await get_user_or_create(uid)
    if user.current_prompt:
        return user.current_prompt
    raise ValueError("No current prompt")


async def main():
    # await import_data()
    result = await search_prompt("派蒙")
    print(await display_results(result))

    # 删除测试用户
    await user_collection.delete_many({"uid": "114514"})
    # 测试用户的uid是114514
    await create_user("114514")
    user = await get_user_or_create("114514")
    print(user)
    # 设置用户的当前prompt
    prompt = await set_current_prompt("114514", "20230513_ztacbgsrms_chn")
    print(prompt)

    # 获取用户的当前prompt
    prompt = await get_current_prompt("114514")
    print(prompt)


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
