from .config import config, sa_client, clients
from pydantic import BaseModel, Extra
from .db.db_mongo import get_current_prompt


class Message(BaseModel):
    """Message Model Here"""

    role: str  # system | user | assistant
    content: str


async def get_response(prompt, model_name: str = "gpt-3.5-turbo-1106", client=clients["openai"]):
    """get response"""
    completion = await client.chat.completions.create(model=model_name, messages=prompt, temperature=0.6)
    return completion.choices[0].message.content


async def get_sa_response(prompt, model_name: str = "deepseek-v3"):
    """get response"""
    completion = await sa_client.chat.completions.create(model=model_name, messages=prompt, temperature=0.6)
    return completion.choices[0].message.content


# 添加以下方法到api.py文件中


async def get_streaming_response(messages, model_name="gpt-4o-mini-2024-07-18", client=clients["openai"]):
    """获取流式响应"""
    try:
        async for chunk in get_openai_streaming_response(messages, model_name, client):
            yield chunk
    except Exception as e:
        yield f"Error: {str(e)}"


async def get_openai_streaming_response(messages, model_name="gpt-4o-mini-2024-07-18", client=clients["openai"]):
    """OpenAI的流式输出实现"""
    try:
        completion = await client.chat.completions.create(
            model=model_name,
            messages=messages,
            stream=True,  # 启用流式输出
        )

        collected_chunks = []
        async for chunk in completion:
            if chunk.choices[0].delta.content:
                content = chunk.choices[0].delta.content
                collected_chunks.append(content)
                yield content
    except Exception as e:
        yield f"Error in streaming: {str(e)}"
