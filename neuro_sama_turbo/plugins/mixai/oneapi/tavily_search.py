from tavily import AsyncTavily<PERSON><PERSON>
from pydantic import BaseModel
from typing import Any, Literal, Sequence
import json
from pydantic_ai.format_as_xml import format_as_xml


class BaseModel(BaseModel):

    def to_xml(self):
        return format_as_xml(self, root_tag=self.__class__.__name__, item_tag="item")

    @property
    def xml(self) -> str:
        return self.to_xml()


class SearchRequest(BaseModel):
    query: str
    include_answer: bool = True
    include_raw_content: bool = True
    include_images: bool = True


class SearchResult(BaseModel):
    title: str
    url: str
    content: str
    score: float
    raw_content: str | None = None


class SearchResponse(BaseModel):
    query: str
    follow_up_questions: Any = None
    answer: str = None
    images: list = []
    results: list[SearchResult] = []


class SearchContext(BaseModel):
    index: int = 0
    url: str
    content: str


class SearchContextResponse(BaseModel):
    contexts: list[SearchContext] = []


"""
(method) def get_search_context(
    query: str,
    search_depth: Literal['basic', 'advanced'] = "basic",
    topic: Literal['general', 'news'] = "general",
    days: int = 3,
    max_results: int = 5,
    include_domains: Sequence[str] = None,
    exclude_domains: Sequence[str] = None,
    max_tokens: int = 4000,
"""


class SearchContextRequest(BaseModel):
    query: str
    search_depth: Literal["basic", "advanced"] = "basic"
    topic: Literal["general", "news"] = "general"
    days: int = 3
    max_results: int = 5
    include_domains: Sequence[str] = None
    exclude_domains: Sequence[str] = None
    max_tokens: int = 88888


async_tavily_client = AsyncTavilyClient(
    api_key="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"
)


async def search(
    query: str,
    include_answer: bool = True,
    include_raw_content: bool = True,
    include_images: bool = True,
) -> SearchResponse:
    request = SearchRequest(
        query=query,
        include_answer=include_answer,
        include_raw_content=include_raw_content,
        include_images=include_images,
    )
    response = await async_tavily_client.search(**request.model_dump())
    return SearchResponse.model_validate(response)


async def get_search_context(request: SearchContextRequest) -> SearchContextResponse:
    response = await async_tavily_client.get_search_context(**request.model_dump())
    response = json.loads(response)
    d = SearchContextResponse.model_validate({"contexts": response})
    for i, context in enumerate(d.contexts):
        context.index = i + 1
    return d


async def test():
    # response = await search("如何评价原神")
    # print(response)
    response = await get_search_context(
        SearchContextRequest(query="如何评价小行星撞地球", max_results=10)
    )
    print(response.xml)
    print(len(response.xml))


if __name__ == "__main__":
    import asyncio

    asyncio.run(test())
