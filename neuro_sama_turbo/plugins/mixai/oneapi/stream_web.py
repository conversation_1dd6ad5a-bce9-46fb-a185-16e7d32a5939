from fastapi import APIRouter, FastAPI, Request, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
import os
import uuid
from typing import Dict, List, Optional
import asyncio
import time
import json
from pydantic import BaseModel
import uvicorn
import threading
from nonebot import get_app

from ...txt2img.txt2img import Txt2Img

# 创建应用
app: FastAPI = get_app()
current_dir = os.path.dirname(os.path.abspath(__file__))
templates = Jinja2Templates(directory=os.path.join(current_dir, "templates"))
os.makedirs(os.path.join(current_dir, "templates"), exist_ok=True)
os.makedirs(os.path.join(current_dir, "static"), exist_ok=True)

# 提供静态文件
app.mount("/static", StaticFiles(directory=os.path.join(current_dir, "static")), name="static")
router = APIRouter()


# 存储会话数据
class StreamSession(BaseModel):
    session_id: str
    title: str
    model_name: str
    prompt: str
    creation_time: float
    messages: List[str] = []
    completed: bool = False
    user_id: Optional[str] = None
    group_id: Optional[str] = None


# 本地存储
active_sessions: Dict[str, StreamSession] = {}
active_connections: Dict[str, List[WebSocket]] = {}
# 如果存在本地session数据，恢复
session_data_path = os.path.join(current_dir, "sessions.json")
if os.path.exists(session_data_path):
    with open(session_data_path, "r") as f:
        data = json.load(f)
        for session_id, session in data.items():
            active_sessions[session_id] = StreamSession(**session)


def save_sessions():
    with open(session_data_path, "w") as f:
        data = {session_id: session.model_dump() for session_id, session in active_sessions.items()}
        json.dump(data, f, indent=4, ensure_ascii=False)


# 清理过期会话的任务
async def cleanup_expired_sessions():
    while True:
        current_time = time.time()
        expired_ids = []
        for session_id, session in active_sessions.items():
            # 24小时后过期
            if current_time - session.creation_time > 86400 and session.completed:
                expired_ids.append(session_id)

        for session_id in expired_ids:
            del active_sessions[session_id]
            if session_id in active_connections:
                del active_connections[session_id]

        await asyncio.sleep(3600)  # 每小时检查一次


import base64


def base64_encode(s):
    return base64.b64encode(s.encode("utf-8")).decode("utf-8")


@router.get("/stream/{session_id}", response_class=HTMLResponse)
async def get_stream(request: Request, session_id: str):
    if session_id not in active_sessions:
        return HTMLResponse(content="Session not found", status_code=404)

    session = active_sessions[session_id]
    return templates.TemplateResponse(
        "stream.html",
        {
            "request": request,
            "session_id": session_id,
            "title": session.title,
            "model_name": session.model_name,
            "prompt": session.prompt,
            "messages": session.messages,
            "message_base64": base64_encode("".join(session.messages)),
            "completed": session.completed,
        },
    )


@router.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    await websocket.accept()

    if session_id not in active_connections:
        active_connections[session_id] = []
    active_connections[session_id].append(websocket)

    try:
        while True:
            # 保持连接直到客户端断开
            await websocket.receive_text()
    except WebSocketDisconnect:
        active_connections[session_id].remove(websocket)


async def create_stream_session(
    title: str,
    model_name: str,
    prompt: str,
    user_id: str | None = None,
    group_id: str | None = None,
):
    """创建一个新的流式会话，返回会话ID"""
    session_id = str(uuid.uuid4())
    session = StreamSession(
        session_id=session_id,
        title=title,
        model_name=model_name,
        prompt=prompt,
        creation_time=time.time(),
        user_id=user_id,
        group_id=group_id,
    )
    active_sessions[session_id] = session
    return session_id


async def send_stream_message(session_id: str, content: str):
    """向会话发送消息"""
    if session_id not in active_sessions:
        return False

    session = active_sessions[session_id]
    session.messages.append(content)

    if session_id in active_connections:
        message = json.dumps({"type": "message", "content": "".join(session.messages)})
        for connection in active_connections[session_id]:
            try:
                await connection.send_text(message)
            except:
                pass

    return True


async def complete_stream_session(session_id: str):
    """完成会话"""
    if session_id not in active_sessions:
        return False

    session = active_sessions[session_id]
    session.completed = True
    save_sessions()
    print(f"{session.model_dump()}")
    if session_id in active_connections:
        message = json.dumps({"type": "complete"})
        for connection in active_connections[session_id]:
            try:
                await connection.send_text(message)
            except:
                pass

    if session.user_id and session.group_id:
        import nonebot_plugin_saa as saa

        t2i = Txt2Img()
        pic = t2i.draw(
            title=f"AI {session.model_name} Response",
            text="".join(session.messages),
        )
        await saa.Image(pic).send_to(saa.TargetQQGroup(group_id=session.group_id))

    return True


# 获取会话URL
def get_session_url(session_id: str, host="127.0.0.1", port=8000):
    import re

    ip_pattern = re.compile(r"^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$")  # 匹配IP地址的正则表达式
    if not ip_pattern.match(host):
        return f"https://{host}/stream/{session_id}"
    return f"http://{host}:{port}/stream/{session_id}"


app.include_router(router)
