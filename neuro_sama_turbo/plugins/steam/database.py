from datetime import datetime
import os

from peewee import Char<PERSON>ield, DateField, IntegerField, Model, SqliteDatabase, fn

path = os.path.dirname(os.path.abspath(__file__))

db = SqliteDatabase(os.path.join(path, "steam.db"))


# 用来记录每一个用户每一次游戏的数据
class Game(Model):
    # steamid
    steamid = CharField()
    # 游戏名
    name = CharField()
    # 游戏时长.单位为秒
    playtime_forever = IntegerField()
    # 游玩时的日期
    date = DateField(default=datetime.now().date())

    class Meta:
        database = db


# 连接数据库
def connect_db():
    db.connect()
    db.create_tables([Game], safe=True)


# 关闭数据库
def close_db():
    db.close()


# 插入数据
def insert_data(steamid, name, playtime_forever):
    Game.create(steamid=steamid, name=name, playtime_forever=playtime_forever, date=datetime.now().date())


# 查询数据
def query_data(steamid=None, name=None, date=None, keyword=None):
    query = Game.select()
    if steamid:
        query = query.where(Game.steamid == steamid)
    if name:
        query = query.where(Game.name == name)
    if date:
        query = query.where(Game.date == date)
    if keyword:
        query = query.where(Game.name.contains(keyword))
    return query


# 查询数据，然后按照steamid和name和date进行分组，然后对playtime_forever进行求和
def query_data_groupby(steamid=None, name=None, date=None, keyword=None):
    query = Game.select(
        Game.steamid, Game.name, Game.date, fn.SUM(Game.playtime_forever).alias("playtime_forever")
    ).group_by(Game.steamid, Game.name, Game.date)
    if steamid:
        query = query.where(Game.steamid == steamid)
    if name:
        query = query.where(Game.name == name)
    if date:
        query = query.where(Game.date == date)
    if keyword:
        query = query.where(Game.name.contains(keyword))
    return query


# 查询数据，然后按照steamid和name进行分组，然后对playtime_forever进行求和
def query_data_groupby2(steamid=None, name=None, date=None, keyword=None):
    query = Game.select(Game.steamid, Game.name, fn.SUM(Game.playtime_forever).alias("playtime_forever")).group_by(
        Game.steamid, Game.name
    )
    if steamid:
        query = query.where(Game.steamid == steamid)
    if name:
        query = query.where(Game.name == name)
    if date:
        query = query.where(Game.date == date)
    if keyword:
        query = query.where(Game.name.contains(keyword))
    return query


# 查询数据，然后按照steamid进行分组，然后对playtime_forever进行求和
def query_data_groupby3(steamid=None, name=None, date=None, keyword=None):
    query = Game.select(Game.steamid, fn.SUM(Game.playtime_forever).alias("playtime_forever")).group_by(Game.steamid)
    if steamid:
        query = query.where(Game.steamid == steamid)
    if name:
        query = query.where(Game.name == name)
    if date:
        query = query.where(Game.date == date)
    if keyword:
        query = query.where(Game.name.contains(keyword))
    return query


# 查今年的数据，然后按照steamid和name进行分组，然后对playtime_forever进行求和，去除游戏名为VPet的数据
def query_year_data_groupby(steamid=None, name=None, date=None, keyword=None):
    # query = Game.select(Game.steamid, Game.name, fn.SUM(Game.playtime_forever).alias('playtime_forever')).where(Game.name != 'VPet').group_by(Game.steamid)
    query = (
        Game.select(Game.steamid, Game.name, fn.SUM(Game.playtime_forever).alias("playtime_forever"))
        .where(Game.name != "VPet", Game.date >= datetime.now().date().replace(month=1, day=1).strftime("%Y-%m-%d"))
        .group_by(Game.steamid)
    )
    if steamid:
        query = query.where(Game.steamid == steamid)
    if name:
        query = query.where(Game.name == name)
    if date:
        query = query.where(Game.date == date)
    if keyword:
        query = query.where(Game.name.contains(keyword))
    return query


# 不去掉VPet的数据
def query_year_data_groupby2(steamid=None, name=None, date=None, keyword=None):
    query = (
        Game.select(Game.steamid, Game.name, fn.SUM(Game.playtime_forever).alias("playtime_forever"))
        .where(Game.date >= datetime.now().date().replace(month=1, day=1).strftime("%Y-%m-%d"))
        .group_by(Game.steamid)
    )
    if steamid:
        query = query.where(Game.steamid == steamid)
    if name:
        query = query.where(Game.name == name)
    if date:
        query = query.where(Game.date == date)
    if keyword:
        query = query.where(Game.name.contains(keyword))
    return query


# 删除数据
def delete_data(steamid=None, name=None, date=None, keyword=None):
    query = Game.delete()
    if steamid:
        query = query.where(Game.steamid == steamid)
    if name:
        query = query.where(Game.name == name)
    if date:
        query = query.where(Game.date == date)
    if keyword:
        query = query.where(Game.name.contains(keyword))
    return query.execute()
