import asyncio
import contextlib
from datetime import datetime
import json
import os
from pathlib import Path
import time

from lxml import etree
import nonebot
from nonebot import get_driver, logger, require
from nonebot.adapters.onebot.v11.event import GroupMessageEvent as V11GroupMessageEvent
from nonebot.adapters.onebot.v12.event import GroupMessageEvent as V12GroupMessageEvent
from nonebot.internal.adapter.bot import Bo<PERSON>
from nonebot.permission import SUPERUSER
from nonebot.plugin import PluginMetadata
from nonebot_plugin_saa import (
    Image,
    MessageFactory,
    TargetQQGroup,
    Text,
    enable_auto_select_bot,
)
import requests

from .. import aiorequests
from ..handle_prefix import handle_prefix
from .config import Config
from .database import *
from .draw import draw_image_all, draw_image_bytes
from .update_key import update_key

require("nonebot_plugin_apscheduler")
bots = list(nonebot.get_bots().values())

from nonebot_plugin_apscheduler import scheduler

__plugin_meta = PluginMetadata(
    name="steam",
    description="",
    usage="",
    config=Config,
)

driver = get_driver()
global_config = driver.config

sub_plugins = nonebot.load_plugins(str(Path(__file__).parent.joinpath("plugins").resolve()))
# steam订阅列表
steam_subscribes = "steam订阅列表"
steam_subscribes_command = nonebot.on_fullmatch(steam_subscribes)
# 谁在玩游戏
who_is_playing = "谁在玩游戏"
who_is_playing_command = nonebot.on_fullmatch(who_is_playing)
# 添加steam订阅命令
add_steam_command_prefix = "添加steam订阅"
add_steam_command = nonebot.on_startswith(add_steam_command_prefix)
# 取消steam订阅命令
cancel_steam_command_prefix = "取消steam订阅"
cancel_steam_command = nonebot.on_startswith(cancel_steam_command_prefix)
# 绑定steam id
bind_steam_id_command_prefix = "绑定steamid"
bind_steam_id_command = nonebot.on_startswith(bind_steam_id_command_prefix)
# 怡宝今天派了多久
ellye_apex_time_today = "怡宝今天派了多久"
ellye_apex_time_today_command = nonebot.on_fullmatch(ellye_apex_time_today)
# 群猎杀
group_hunt = "群猎杀"
group_hunt_command = nonebot.on_fullmatch(group_hunt)
# 今日猎杀
today_hunt = "今日猎杀"
today_hunt_command = nonebot.on_fullmatch(today_hunt)
# 群天才少年榜
group_genius = "群天才少年榜"
group_genius_command = nonebot.on_fullmatch(group_genius)
# 今日天才榜
today_genius = "今日天才榜"
today_genius_command = nonebot.on_fullmatch(today_genius)
# 更新steamkey
update_steam_key = "更新steamkey"
update_steam_key_command = nonebot.on_fullmatch(update_steam_key)
# 主动消息测试
test_message = "主动测试消息"
test_message_command = nonebot.on_fullmatch(test_message)
# 迁移群1的steam订阅到群2
migrate_steam_subscribes = "迁移steam订阅"
migrate_steam_subscribes_command = nonebot.on_startswith(migrate_steam_subscribes)


@migrate_steam_subscribes_command.handle()
async def handle_migrate_steam_subscribes(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent):
    group_id, user_id = str(event.group_id), str(event.user_id)  # event.get_session_id().split("_")
    # group_id, user_id = str(event.group_id), str(event.user_id)
    messages = handle_prefix(event.get_plaintext(), len(migrate_steam_subscribes)).strip()
    group1, group2 = messages.split(" ")
    group1, group2 = int(group1), int(group2)
    print(group1, group2)
    # 获取群1的steam订阅列表
    for key, val in cfg["subscribes"].items():
        if group1 in val:
            cfg["subscribes"][key].remove(group1)
            cfg["subscribes"][key].append(group2)
    with open(config_file, mode="w", encoding="utf-8") as fil:
        json.dump(cfg, fil, indent=4, ensure_ascii=False)
    await MessageFactory([Text("迁移成功！")]).send()
    await migrate_steam_subscribes_command.finish()


@test_message_command.handle()
async def handle_test_message(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent):
    # 启用自动选择 Bot 的功能
    # enable_auto_select_bot()
    # 群聊568772769
    target = TargetQQGroup(group_id=568772769)
    print(nonebot.get_bots())
    bots = list(nonebot.get_bots().values())
    print(bots)
    await MessageFactory([Text("测试消息")]).send_to(target, bot=bots[0])


current_folder = Path(__file__).parent
config_file = current_folder.joinpath("steam.json")
cfg = {}
playing_state = {}
recycle_times = 0  # 重复次数
proxy = "http://localhost:7899"


# 启动时运行
@driver.on_startup
async def startup():
    global cfg, playing_state
    cfg = json.load(config_file.open(encoding="utf-8"))
    playing_state = json.load(current_folder.joinpath("playing_state.json").open(encoding="utf-8"))
    logger.info(f"cfg: {cfg.keys()}")
    for key in list(playing_state.keys()):
        if key not in cfg["subscribes"]:
            playing_state.pop(key)
    connect_db()


async def format_id(id: str) -> str:
    if id.startswith("765611") and len(id) == 17:
        return id
    resp = await aiorequests.get(f"https://steamcommunity.com/id/{id}?xml=1", proxies={"http": proxy, "https": proxy})
    xml = etree.XML(await resp.content)  # type: ignore
    return xml.xpath("/profile/steamID64")[0].text


@steam_subscribes_command.handle()
async def handle_steam_subscribes(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent):
    group_id, user_id = str(event.group_id), str(event.user_id)  # event.get_session_id().split("_")
    players = []
    group_id = int(group_id)
    for key, val in playing_state.items():
        # if group_id in cfg["subscribes"][str(key)]:
        if cfg["subscribes"].get(str(key), None) and group_id in cfg["subscribes"][str(key)]:
            if val["gameextrainfo"] != "":
                # flag = True
                # msg += "%s 正在游玩 %s\n" % (val["personaname"], val["gameextrainfo"])
                player = {
                    "name": val["personaname"],
                    "game": val["gameextrainfo"],
                    "avatar": val["avatar"],
                    "status": 1,
                }
            else:
                player = {"name": val["personaname"], "game": "None", "avatar": val["avatar"], "status": val["status"]}
            players.append(player)
        elif not cfg["subscribes"].get(str(key), None):
            print(f"{key}不在订阅列表中")
    # players优先按照status排序，再按照game排序
    players.sort(key=lambda x: (x["status"], x["game"]))
    result = draw_image_all(players)
    result = Image(result)
    await MessageFactory([result]).send()
    await steam_subscribes_command.finish()


@who_is_playing_command.handle()
async def handle_who_is_playing(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent):
    group_id, user_id = str(event.group_id), str(event.user_id)  # event.get_session_id().split("_")
    players = []
    for key, val in playing_state.items():
        if val["gameextrainfo"] != "" and int(group_id) in cfg["subscribes"][str(key)]:
            player = {"name": val["personaname"], "game": val["gameextrainfo"], "avatar": val["avatar"], "status": 1}
            players.append(player)
    if not players:
        await MessageFactory([Text("暂时没有人在玩游戏哦~")]).send()
        await who_is_playing_command.finish()
    players.sort(key=lambda x: (x["status"], x["game"], x["name"]))
    result = draw_image_all(players)
    result = Image(result)
    await MessageFactory([result]).send()


@add_steam_command.handle()
async def handle_add_steam(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent):
    account = handle_prefix(event.get_plaintext(), len(add_steam_command_prefix))
    group_id, user_id = str(event.group_id), str(event.user_id)  # event.get_session_id().split("_")
    msg_list = []
    try:
        await update_steam_ids(account, int(group_id))
        rsp = await get_account_status(account)
        if rsp["personaname"] == "":
            msg = Text("添加订阅失败！")
            msg_list.append(msg)
        elif rsp["gameextrainfo"] == "":
            img = draw_image_bytes(rsp["personaname"], "None", rsp["avatar"], 3)
            msg_list.append(Image(img))
            msg_list.append(Text("\n添加订阅成功！"))
        else:
            img = draw_image_bytes(rsp["personaname"], rsp["gameextrainfo"], rsp["avatar"], 1)
            msg_list.append(Image(img))
            msg_list.append(Text("\n添加订阅成功！"))
    except Exception as e:
        print(e)
        msg_list.append(Text("添加订阅失败！"))
    msg_builder = MessageFactory(msg_list)
    await msg_builder.send()
    await add_steam_command.finish()


@cancel_steam_command.handle()
async def handle_cancel_steam(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent):
    account = handle_prefix(event.get_plaintext(), len(cancel_steam_command_prefix))
    group_id, user_id = str(event.group_id), str(event.user_id)  # event.get_session_id().split("_")
    msg_list = []
    try:
        await del_steam_ids(account, group_id)
        msg_list.append(Text("取消订阅成功！"))
    except Exception as e:
        print(e)
        msg_list.append(Text("取消订阅失败！"))
    msg_builder = MessageFactory(msg_list)
    await msg_builder.send()
    await cancel_steam_command.finish()


@ellye_apex_time_today_command.handle()
async def handle_ellye_apex_time_today(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent):
    steam_id = "*****************"
    now = datetime.now().date()
    query = query_data_groupby(steamid=steam_id, date=now, name="Apex Legends")
    global playing_state
    for record in query:
        nowtime = record.playtime_forever / 60 / 60
        if steam_id in playing_state and playing_state[steam_id]["gameextrainfo"] == "Apex Legends":
            nowtime += (time.time() - playing_state[steam_id]["lastlogoff"]) / 60 / 60
        await MessageFactory([Text(f"怡宝今天派了{nowtime:.2f}小时")]).send()
        await ellye_apex_time_today_command.finish()
    nowtime = 0
    # 查询当前状态，如果还在玩Apex Legends，那么就加上当前时间
    # global playing_state
    if steam_id in playing_state and playing_state[steam_id]["gameextrainfo"] == "Apex Legends":
        nowtime += (time.time() - playing_state[steam_id]["lastlogoff"]) / 60 / 60
        await MessageFactory([Text(f"怡宝今天派了{nowtime:.2f}小时")]).send()
        await ellye_apex_time_today_command.finish()
    await MessageFactory([Text(f"怡宝今天派了{nowtime:.2f}小时")]).send()
    await ellye_apex_time_today_command.finish()


@bind_steam_id_command.handle()
async def handle_bind_steam_id(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent):
    group_id, user_id = str(event.group_id), str(event.user_id)  # event.get_session_id().split("_")
    messages = event.get_message()
    self_id = bot.self_id
    for message in messages:
        print(message)
    # 判断去掉了命令前缀的消息的第一个参数是不是at，nb2的msgs是不去掉命令前缀的，所以这里直接取下标1
    if len(messages) > 1 and messages[1].type == "at":
        temp_user_id = messages[1].data["user_id"]
        if temp_user_id == self_id:
            # 禁止at_bot
            await MessageFactory([Text("不要at我~")]).send()
            await bind_steam_id_command.finish()
        elif temp_user_id != user_id:
            # 鉴定是否是bot管理员
            if not await SUPERUSER(bot, event):
                await MessageFactory([Text("你不是管理员，不能艾特别人绑定steamid哦~")]).send()
                await bind_steam_id_command.finish()
            else:
                user_id = temp_user_id

    steam_id = handle_prefix(event.get_plaintext(), len(bind_steam_id_command_prefix))
    if not steam_id.isdigit():
        await MessageFactory([Text("steamid不合法！")]).send()
        await bind_steam_id_command.finish()
        # 判断是否是已经记录的steamid且在当前群
    ids = [key for key, val in cfg["subscribes"].items() if group_id in val]
    msg_list = []
    if steam_id in ids:
        # 直接绑定给用户
        bindSteamId(user_id, steam_id)
        msg_list.append(Text(f"绑定成功！{user_id}绑定了{steam_id}"))
    else:
        try:
            await update_steam_ids(steam_id, group_id)
            rsp = await get_account_status(steam_id)
            if rsp["personaname"] == "":
                raise Exception("personaname is null")
            elif rsp["gameextrainfo"] == "":
                img = draw_image_bytes(rsp["personaname"], "None", rsp["avatar"], 3)
                msg_list.append(Image(img))
            else:
                img = draw_image_bytes(rsp["personaname"], rsp["gameextrainfo"], rsp["avatar"], 1)
                msg_list.append(Image(img))
            bindSteamId(user_id, steam_id)
            msg_list.append(Text(f"绑定成功！{user_id}绑定了{steam_id}"))
        except Exception as e:
            print(e)
            msg_list.append(Text("绑定失败，请检查steamid是否正确"))
    msg_builder = MessageFactory(msg_list)
    await msg_builder.send(at_sender=True)


@group_hunt_command.handle()
async def handle_group_hunt(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent):
    group_id, user_id = str(event.group_id), str(event.user_id)  # event.get_session_id().split("_")
    ids = [key for key, val in cfg["subscribes"].items() if int(group_id) in val]
    query = query_data_groupby2(name="Apex Legends")
    records = [record for record in query if record.steamid in ids]
    if not records:
        await MessageFactory([Text("暂时没有猎杀者哦~")]).send()
        await group_hunt_command.finish()
    records = sorted(records, key=lambda x: x.playtime_forever, reverse=True)
    data = getSteamName([record.steamid for record in records])
    data = dict(data)
    msg = ""
    for record in records:
        total_time = record.playtime_forever / 60 / 60  # 单位是小时
        # 分为整数部分和小数部分,用.分割
        int_part, dec_part = str(total_time).split(".")
        # 转为整数
        int_part, dec_part = int(int_part), int(dec_part[:2])
        msg += f"{data[record.steamid]}:{int_part}K{dec_part}D\n"
    msg = f"群猎杀：\n{msg}".strip()
    await MessageFactory([Text(msg)]).send()
    await group_hunt_command.finish()


@today_hunt_command.handle()
async def handle_today_hunt(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent):
    group_id, user_id = str(event.group_id), str(event.user_id)  # event.get_session_id().split("_")
    ids = [key for key, val in cfg["subscribes"].items() if int(group_id) in val]
    query = query_data_groupby2(name="Apex Legends", date=datetime.now().date())
    records = [record for record in query if record.steamid in ids]
    if not records:
        await MessageFactory([Text("暂时没有猎杀者哦~")]).send()
        await today_hunt_command.finish()
    records = sorted(records, key=lambda x: x.playtime_forever, reverse=True)
    data = getSteamName([record.steamid for record in records])
    data = dict(data)
    msg = ""
    for record in records:
        total_time = record.playtime_forever / 60 / 60  # 单位是小时
        # 分为整数部分和小数部分,用.分割
        int_part, dec_part = str(total_time).split(".")
        # 转为整数
        int_part, dec_part = int(int_part), int(dec_part[:2])
        msg += f"{data[record.steamid]}:{int_part}K{dec_part}D\n"
    msg = f"今日猎杀：\n{msg}".strip()
    await MessageFactory([Text(msg)]).send()
    await today_hunt_command.finish()


@group_genius_command.handle()
async def handle_group_genius(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent):
    group_id, user_id = str(event.group_id), str(event.user_id)  # event.get_session_id().split("_")
    ids = [key for key, val in cfg["subscribes"].items() if int(group_id) in val]
    query = query_data_groupby3()
    records = [record for record in query if record.steamid in ids]
    if not records:
        await MessageFactory([Text("暂时没有天才哦~")]).send()
        await group_genius_command.finish()
    records = sorted(records, key=lambda x: x.playtime_forever, reverse=True)
    data = getSteamName([record.steamid for record in records])
    data = dict(data)
    msg = "\n".join([f"{data[record.steamid]}:{record.playtime_forever / 60 / 60:.2f}小时" for record in records])
    msg = f"群天才少年榜：\n{msg}"
    await MessageFactory([Text(msg)]).send()
    await group_genius_command.finish()


yearly_genius = "年度steam"
yearly_genius_command = nonebot.on_fullmatch(yearly_genius)


@yearly_genius_command.handle()
async def handle_yearly_genius(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent):
    group_id, user_id = str(event.group_id), str(event.user_id)
    ids = [key for key, val in cfg["subscribes"].items() if int(group_id) in val]
    query = query_year_data_groupby()
    records = [record for record in query if record.steamid in ids]
    if not records:
        await MessageFactory([Text("暂时没有天才哦~")]).send()
        await yearly_genius_command.finish()
    records = sorted(records, key=lambda x: x.playtime_forever, reverse=True)
    data = getSteamName([record.steamid for record in records])
    data = dict(data)
    msg = "\n".join([f"{data[record.steamid]}:{record.playtime_forever / 60 / 60:.2f}小时" for record in records])
    msg = f"年度steam：\n{msg}"
    await MessageFactory([Text(msg)]).send()
    await yearly_genius_command.finish()


yearly_genius2 = "带vpet的年度steam"
yearly_genius2_command = nonebot.on_fullmatch(yearly_genius)


@yearly_genius2_command.handle()
async def handle_yearly_genius(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent):
    group_id, user_id = str(event.group_id), str(event.user_id)
    ids = [key for key, val in cfg["subscribes"].items() if int(group_id) in val]
    query = query_year_data_groupby2()
    records = [record for record in query if record.steamid in ids]
    if not records:
        await MessageFactory([Text("暂时没有天才哦~")]).send()
        await yearly_genius_command.finish()
    records = sorted(records, key=lambda x: x.playtime_forever, reverse=True)
    data = getSteamName([record.steamid for record in records])
    data = dict(data)
    msg = "\n".join([f"{data[record.steamid]}:{record.playtime_forever / 60 / 60:.2f}小时" for record in records])
    msg = f"年度steam：\n{msg}"
    await MessageFactory([Text(msg)]).send()
    await yearly_genius2_command.finish()


@today_genius_command.handle()
async def handle_today_genius(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent):
    group_id, user_id = str(event.group_id), str(event.user_id)  # event.get_session_id().split("_")
    ids = [key for key, val in cfg["subscribes"].items() if int(group_id) in val]
    query = query_data_groupby3(date=datetime.now().date())
    records = [record for record in query if record.steamid in ids]
    if not records:
        await MessageFactory([Text("暂时没有天才哦~")]).send()
        await group_genius_command.finish()
    records = sorted(records, key=lambda x: x.playtime_forever, reverse=True)
    data = getSteamName([record.steamid for record in records])
    data = dict(data)
    msg = "\n".join([f"{data[record.steamid]}:{record.playtime_forever / 60 / 60:.2f}小时" for record in records])
    msg = f"今日天才榜：\n{msg}"
    await MessageFactory([Text(msg)]).send()
    await group_genius_command.finish()


lock = asyncio.Lock()


@update_steam_key_command.handle()
async def handle_update_steam_key(bot: Bot, event: V11GroupMessageEvent | V12GroupMessageEvent):
    if not await SUPERUSER(bot, event):
        await MessageFactory([Text("你不是管理员，不能更新steamkey哦~")]).send()
        await update_steam_key_command.finish()
    if lock.locked():
        await MessageFactory([Text("正在更新steamkey，请稍后再试")]).send()
        await update_steam_key_command.finish()
    async with lock:
        new_key = await update_key()
        cfg["key"] = new_key
        cfg["keys"] = [new_key, new_key]
        with open(config_file, mode="w", encoding="utf-8") as fil:
            json.dump(cfg, fil, indent=4, ensure_ascii=False)
        await MessageFactory([Text("更新steamkey成功！")]).send()
        await update_steam_key_command.finish()


# 5小时更新一次key
@scheduler.scheduled_job("cron", hour="*/5")
async def update_steam_key_job():
    pass


"""
	new_key = await update_key()
    cfg["key"] = new_key
    cfg["keys"] = [new_key,new_key]
    with open(config_file, mode="w", encoding="utf-8") as fil:
        json.dump(cfg, fil, indent=4, ensure_ascii=False)
    print("更新steamkey成功！")
"""


# bot = nonebot.get_bot()
# 每分钟更新一次游戏状态
@scheduler.scheduled_job("cron", minute="*/1")
async def update_playing_state():
    global playing_state, recycle_times
    old_state = playing_state.copy()
    await update_game_status()

    def formatTimefromseconds(duration_in_seconds):
        # duration_in_seconds是秒
        # 返回timeStr,共玩了xx小时xx分钟xx秒
        timeStr = ""
        duration_in_seconds = int(duration_in_seconds)
        if duration_in_seconds < 60:
            timeStr = f"{int(duration_in_seconds)}秒"
        elif duration_in_seconds < 60 * 60:
            timeStr = f"{int(duration_in_seconds // 60)}分钟"
            timeStr += f"{int(duration_in_seconds % 60)}秒" if duration_in_seconds % 60 != 0 else ""
        else:
            timeStr = f"{int(duration_in_seconds // 60 // 60)}小时"
            timeStr += f"{int(duration_in_seconds // 60 % 60)}分钟" if duration_in_seconds // 60 % 60 != 0 else ""
            timeStr += f"{int(duration_in_seconds % 60)}秒" if duration_in_seconds % 60 != 0 else ""
        return f"共玩了{timeStr}"

    bots = list(nonebot.get_bots().values())
    bot = bots[0]
    for key, val in playing_state.items():
        with contextlib.suppress(KeyError):
            if val["gameextrainfo"] != old_state[key]["gameextrainfo"]:  # 游戏状态发生变化
                glist = set(cfg["subscribes"][key])
                try:
                    # groups = await bot.get_groups()
                    # group_ids = [ int(group.groupCode) for group in groups ]
                    # glist&=set(group_ids)
                    pass
                except Exception as e:
                    print(e)
                if val["gameextrainfo"] == "":  # 不在玩游戏了，发送离开消息，发送玩了多久
                    timeStr = None
                    with contextlib.suppress(Exception):
                        nowTime = time.time()
                        oldTime = old_state[key]["lastlogoff"]
                        duration_in_seconds = nowTime - oldTime
                        timeStr = formatTimefromseconds(duration_in_seconds)
                        insert_data(
                            steamid=key, name=old_state[key]["gameextrainfo"], playtime_forever=(nowTime - oldTime)
                        )

                    img = draw_image_bytes(
                        val["personaname"], old_state[key]["gameextrainfo"], val["avatar"], 2, timeStr
                    )
                else:  # 在玩游戏了，而且和上次不一样，发送进入消息，开始记录时间
                    playing_state[key]["lastlogoff"] = time.time()
                    # 如果上次的状态也是在玩游戏，那么就发送玩了多久
                    if old_state[key]["gameextrainfo"] != "":
                        timeStr = None
                        with contextlib.suppress(Exception):
                            nowTime = time.time()
                            oldTime = old_state[key]["lastlogoff"]
                            duration_in_seconds = nowTime - oldTime
                            timeStr = formatTimefromseconds(duration_in_seconds)
                            insert_data(
                                steamid=key, name=old_state[key]["gameextrainfo"], playtime_forever=(nowTime - oldTime)
                            )

                        img = draw_image_bytes(
                            val["personaname"], old_state[key]["gameextrainfo"], val["avatar"], 2, timeStr
                        )
                        img = Image(img)
                        await broadcast(glist, img)
                        sended = True
                    img = draw_image_bytes(val["personaname"], val["gameextrainfo"], val["avatar"], 1)
                img = Image(img)
                await broadcast(glist, img)
    # 把当前的状态备份写入文件
    with open(os.path.join(os.path.dirname(__file__), "playing_state.json"), "w", encoding="utf-8") as fil:
        json.dump(playing_state, fil, indent=4, ensure_ascii=False)


async def broadcast(group_list: set, msg):
    enable_auto_select_bot()
    bots = list(nonebot.get_bots().values())
    for group in group_list:
        target = TargetQQGroup(group_id=int(group))
        # await MessageFactory(
        #    msg
        # ).send_to(target,bot=bots[0])
        try:
            await asyncio.wait_for(MessageFactory(msg).send_to(target, bot=bots[0]), timeout=10)
            # await sv.bot.send_group_msg(group_id=group, message=msg)
            await asyncio.sleep(0.5)
        except Exception as e:
            print(f"发送消息失败{e}")
            continue


def getSteamName(ids: list):
    params = {"key": cfg["key"], "format": "json", "steamids": ",".join(ids)}
    resp = requests.get(
        "https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v2/",
        params=params,
        proxies={"http": proxy, "https": proxy},
    )
    rsp = resp.json()
    return [(friend["steamid"], friend["personaname"]) for friend in rsp["response"]["players"]]


async def del_steam_ids(steam_id, group):
    steam_id = await format_id(steam_id)
    print(steam_id)
    group = int(group)
    if group in cfg["subscribes"][str(steam_id)]:
        cfg["subscribes"][str(steam_id)].remove(group)
    with open(config_file, mode="w", encoding="utf-8") as fil:
        json.dump(cfg, fil, indent=4, ensure_ascii=False)
    # await update_game_status()


def bindSteamId(uid, steam_id):
    # 判断文件是否存在
    if not os.path.exists(os.path.join(os.path.dirname(__file__), "bind.json")):
        with open(os.path.join(os.path.dirname(__file__), "bind.json"), "w") as fil:
            json.dump({}, fil, indent=4, ensure_ascii=False)
    with open(os.path.join(os.path.dirname(__file__), "bind.json"), encoding="utf-8") as fil:
        bind_dict = json.load(fil)
    bind_dict[uid] = steam_id
    with open(os.path.join(os.path.dirname(__file__), "bind.json"), "w", encoding="utf-8") as fil:
        json.dump(bind_dict, fil, indent=4, ensure_ascii=False)


async def update_steam_ids(steam_id, group):
    steam_id = await format_id(steam_id)
    # print(steam_id)
    if steam_id not in cfg["subscribes"]:
        cfg["subscribes"][str(steam_id)] = []
        # print("add empy list success")
    if group not in cfg["subscribes"][str(steam_id)]:
        cfg["subscribes"][str(steam_id)].append(group)
        # print("add group into list success")
    # print(cfg["subscribes"][str(steam_id)])
    with open(config_file, mode="w", encoding="utf-8") as fil:
        json.dump(cfg, fil, indent=4, ensure_ascii=False)
    await update_game_status()


async def update_game_status():
    async def getJson(url, params=None, times=0):
        # return None
        """
        retry_count = 5
        while retry_count > 0:
            proxy = get_proxy().get("proxy")
            #proxy = "clash-client:7890"
            print(f"使用代理{proxy}获取json")
            try:
                html = await aiorequests.get(url, proxies={"http": "http://{}".format(proxy), "https": "http://{}".format(proxy)},timeout=10)
                # 使用代理访问
                return await html.json()
            except Exception as e:
                retry_count -= 1
                print(f"获取json失败，剩余重试次数{retry_count},代理{proxy}，错误信息{e}")
                # 删除代理池中代理
                delete_proxy(proxy)
        """
        # return None
        if times > 5:
            return None
        # 最后再来一次没有代理的
        try:
            html = await aiorequests.get(url, params=params, timeout=10, proxies={"http": proxy, "https": proxy})
            # html = await aiorequests.get(url, timeout=10,proxies={"http": "http://clash-client:7890", "https": "http://clash-client:7890"})
            return await html.json()
        except Exception as e:
            logger.error(f"获取json失败，错误信息{e}")
            return None

    async def getSteamResult(steamids):
        result = []
        # 打乱steamids顺序
        # random.shuffle(steamids)
        print(f"steamids长度{len(steamids)}")

        # 100个id一组，判断可以分成多少组，然后平均分为多少组
        def getGroupNum(num):
            if num <= 100:
                return 1
            else:
                return num // 100 + 1

        groupNum = getGroupNum(len(steamids))
        groupLen = len(steamids) // groupNum
        groupRemainder = len(steamids) % groupNum
        # 生成嵌套列表
        temp_steamids = [steamids[i : i + groupLen] for i in range(0, len(steamids), groupLen)]
        i = 0

        # 根据当前是第几组，来决定使用哪个key
        def getKey(i):
            keys = cfg["keys"]
            length = len(keys)
            return keys[i % length]

        for steamids in temp_steamids:
            cfg["key"] = getKey(i)
            print(f"使用key{cfg['key']}")
            i += 1
            params = {"key": cfg["key"], "format": "json", "steamids": ",".join(steamids)}
            url = f"https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v2/?key={cfg['key']}&format=json&steamids={','.join(steamids)}"
            resp = await getJson(url)
            # time.sleep(10)
            if resp is None:
                # resp = await getJson("https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v2/", params)
                print(f"获取json失败，{i}")
                continue
            else:
                print(f"获取json成功，{i}")
            resp_json = resp["response"]["players"]
            result.extend(resp_json)
        return result

    rsp = await getSteamResult(list(cfg["subscribes"].keys()))
    # print(rsp)
    statusMap = {
        0: 4,
        1: 3,
        2: 3,
        3: 3,
        4: 3,
        5: 3,
        6: 3,
    }
    # 备份原来的状态
    old_state = playing_state.copy()
    for friend in rsp:
        await download_avatar(friend["steamid"], friend["avatarfull"])
        playing_state[friend["steamid"]] = {
            "personaname": friend["personaname"],
            "gameextrainfo": friend["gameextrainfo"] if "gameextrainfo" in friend else "",
            "avatar": get_avatar_path(friend["steamid"]),
            "status": 1 if "gameextrainfo" in friend else statusMap[friend["personastate"]],
        }
        try:
            playing_state[friend["steamid"]]["lastlogoff"] = (
                old_state[friend["steamid"]]["lastlogoff"]
                if friend["steamid"] in old_state
                and "lastlogoff" in old_state[friend["steamid"]]
                and old_state[friend["steamid"]]["gameextrainfo"] == friend["gameextrainfo"]
                else time.time()
            )
            # print(f"{friend['steamid']}:{playing_state[friend['steamid']]}, in update_game_status, lastlogoff:{playing_state[friend['steamid']]['lastlogoff']}")
        except KeyError:
            # playing_state[friend["steamid"]]["lastlogoff"] = time.time()
            # print(f"KeyError:{friend['steamid']}, in update_game_status")
            # 输出具体的错误信息
            # log_file = os.path.join(current_folder, 'log.txt')
            # with open(log_file, mode="a") as fil:
            #    fil.write(f"{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())} {friend['steamid']} {friend['personaname']}\n")
            continue


async def download_avatar(steam_id, url):
    # 判断文件是否存在的逻辑需要改为判断头像是否和原来的一样，不一样下载，一样不下载
    if check_avatar(steam_id, url) and os.path.exists(get_avatar_path(steam_id)):
        return
    # steam_id = await format_id(steam_id)
    resp = await aiorequests.get(url, proxies={"http": proxy, "https": proxy})
    # 判断文件夹是否存在
    if not os.path.exists(os.path.join(os.path.dirname(__file__), "avatar")):
        os.mkdir(os.path.join(os.path.dirname(__file__), "avatar"))
    with open(os.path.join(os.path.dirname(__file__), "avatar", f"{steam_id}.png"), "wb") as fil:
        fil.write(await resp.content)


def get_avatar_path(steam_id):
    return os.path.join(os.path.dirname(__file__), "avatar", f"{steam_id}.png")


def check_avatar(steam_id, url):
    # 查询头像记录json
    if not os.path.exists(os.path.join(os.path.dirname(__file__), "avatar.json")):
        with open(os.path.join(os.path.dirname(__file__), "avatar.json"), "w", encoding="utf-8") as fil:
            json.dump({}, fil, indent=4, ensure_ascii=False)
    with open(os.path.join(os.path.dirname(__file__), "avatar.json")) as fil:
        avatar_dict = json.load(fil)
    # 判断头像记录是否存在
    if steam_id in avatar_dict and avatar_dict[steam_id] == url:
        return True
    avatar_dict[steam_id] = url
    with open(os.path.join(os.path.dirname(__file__), "avatar.json"), "w", encoding="utf-8") as fil:
        json.dump(avatar_dict, fil, indent=4, ensure_ascii=False)
    return False


async def get_account_status(id) -> dict:
    steamid = await format_id(id)
    params = {"key": cfg["key"], "format": "json", "steamids": steamid}
    resp = await aiorequests.get(
        "https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v2/",
        params=params,
        headers={"Accept-Language": "zh-CN,zh;q=0.9"},
        proxies={"http": proxy, "https": proxy},
    )
    rsp = await resp.json()
    friend = rsp["response"]["players"][0]
    statusMap = {
        0: 4,
        1: 3,
        2: 3,
        3: 3,
        4: 3,
        5: 3,
        6: 3,
    }
    await download_avatar(steamid, friend["avatarfull"])
    return {
        "personaname": friend["personaname"] if "personaname" in friend else "",
        "gameextrainfo": friend["gameextrainfo"] if "gameextrainfo" in friend else "",
        "avatar": get_avatar_path(steamid),
        "status": 1 if "gameextrainfo" in friend else statusMap[friend["personastate"]],
    }
