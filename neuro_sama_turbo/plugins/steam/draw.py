from base64 import b64encode
from io import BytesIO
import os

import numpy as np
from PIL import Image, ImageDraw, ImageFont

fontsPath = os.path.join(os.path.dirname(__file__), "fonts")
defaultFont = os.path.join(fontsPath, "MSYH.TTC")


def draw_rectangle_with_image_and_text(
    width=600,
    height=150,
    background_color="#0e141bcc",
    image_path="test.png",
    name="子落",
    game="Genshin Impact",
    status="is now playing",
    defaultFont=defaultFont,
    font_color="#90A940",
):
    width = height * 4
    img = Image.new("RGBA", (width, height), color=background_color)

    draw = ImageDraw.Draw(img)

    def draw_gradient_line(draw, start, end, color_start, color_end):
        step_r = (color_end[0] - color_start[0]) / (end - start)
        step_g = (color_end[1] - color_start[1]) / (end - start)
        step_b = (color_end[2] - color_start[2]) / (end - start)
        for i in range(start, end):
            color = (
                int(color_start[0] + step_r * i),
                int(color_start[1] + step_g * i),
                int(color_start[2] + step_b * i),
            )
            draw.line([(0, i), (width, i)], fill=color, width=1)

    color_start = (28, 32, 40, 0)
    color_end = (14, 20, 27, 0)
    draw_gradient_line(draw, 0, height, color_start, color_end)

    with Image.open(image_path) as im:
        new_size = (int(height * 0.63), int(height * 0.63))
        im = im.resize(new_size, Image.LANCZOS)
        x = int(0.035 * width)
        y = (height - im.height) // 2
        img.paste(im, (x, y))

        font_color = "#7da84e" if status == "is now playing" else "#7d7e80" if game == "Offline" else "#4c91ac"
        draw.line(
            [
                (x + im.width + int(im.width * 0.05), y),
                (x + im.width + int(im.width * 0.05), y + int(im.height * 0.99)),
            ],
            fill=font_color,
            width=int(width * 0.082 / 5.5),
        )

        def get_text_worth(text):
            worth = 0
            for i in text:
                worth += 0.5 if i.isascii() else 1
            return worth

        def get_text_size(text):
            worth = get_text_worth(text)
            if worth < 10:
                return width // 24
            return width // 30 if worth < 20 else width // 40 if worth < 30 else width // 50

        yd = y + im.height

        def write_text(text, position="center", font_color=font_color):
            text_size = get_text_size(text)
            font = ImageFont.truetype(defaultFont, text_size)
            text_bbox = draw.textbbox((0, 0), text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]

            text_x = x + im.width + int(im.width * 0.32)
            if position == "top":
                text_y = y - text_height + int(height * 0.12)
            elif position == "center":
                text_y = (y + yd - text_height) // 2
            else:  # if position == 'bottom':
                text_y = (yd - text_height) - int(im.height * 0.075)
            draw.text((text_x, text_y), text, font=font, fill=font_color)
            return text_y + text_height

        name_color = "#c7e1ad" if status == "is now playing" else "#7d7e80" if game == "Offline" else "#64bce0"
        write_text(name, "top", font_color=name_color)
        write_text(status, "center", font_color="#7d7e80") if game != "Offline" else ""
        game_color = "#7da84e" if status == "is now playing" else "#7d7e80" if game == "Offline" else "#4c91ac"
        write_text(game, "bottom", font_color=game_color)

    return img


def draw_rectangle_with_text(
    width=600,
    height=50,
    background_color="#000000",
    text="Press Shift+Tab to view",
    font=defaultFont,
    font_size=15,
    font_color="#808080",
    font_position="center",
    font_vertical_position="center",
):
    img = Image.new("RGBA", (width, height), color=background_color)

    draw = ImageDraw.Draw(img)

    draw.rectangle([(0, 0), (width, height)], fill=background_color)  # type: ignore

    def draw_gradient_line(draw, start, end, color_start, color_end):
        step_r = (color_end[0] - color_start[0]) / (end - start)
        step_g = (color_end[1] - color_start[1]) / (end - start)
        step_b = (color_end[2] - color_start[2]) / (end - start)

        for i in range(start, end):
            color = (
                int(color_start[0] + step_r * i),
                int(color_start[1] + step_g * i),
                int(color_start[2] + step_b * i),
            )
            draw.line([(0, i), (width, i)], fill=color, width=1)

    start_color = (14, 15, 18)
    end_color = (0, 0, 0)
    draw_gradient_line(draw, 0, height, start_color, end_color)

    font = ImageFont.truetype(font, font_size)
    text_bbox = draw.textbbox((0, 0), text, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    if font_position == "center":
        text_x = (width - text_width) // 2
    elif font_position == "left":
        text_x = int(width * 0.05)
    else:  # if font_position == 'right':
        text_x = width - text_width - int(width * 0.05)
    if font_vertical_position == "bottom":
        text_y = height - text_height - int(height * 0.05)
    elif font_vertical_position == "center":
        text_y = (height - text_height) // 2 - int(height * 0.03)
    else:  # if font_vertical_position == 'top':
        text_y = int(height * 0.05)
    draw.text((text_x, text_y), text, font=font, fill=font_color)
    return img


def merge_images(images, direction="V"):
    if direction == "V":
        combined_arr = np.vstack((np.array(images[0]), np.array(images[1])))
    else:
        combined_arr = np.hstack((np.array(images[0]), np.array(images[1])))

    return Image.fromarray(combined_arr)


def convert_img(img: Image.Image, is_base64: bool = False):
    img = img.convert("RGB")
    result_buffer = BytesIO()
    img.save(result_buffer, format="PNG", quality=85, subsampling=0)
    res = result_buffer.getvalue()
    if is_base64:
        res = f"base64://{b64encode(res).decode()}"
    return res


def get_propertys(status, game):
    status = int(status)
    if status == 1:
        status = "is now playing"
        color = "#90A940"
    elif status == 2:
        status = "is not playing"
        color = "#62C6F3cc"
    elif status == 3:
        status = "is not playing game"
        color = "#62C6F3cc"
        game = "Online"
    elif status == 4:
        status = "is not playing game"
        color = "#808080"
        game = "Offline"
    return status, color, game


# 读入玩家名，游戏名，状态,绘制图片
def draw_image(name, game, image_path, status, timeStr=None):
    status, color, game = get_propertys(status, game)
    text = timeStr
    if text is None:
        return draw_rectangle_with_image_and_text(
            image_path=image_path, name=name, game=game, status=status, font_color=color
        )
    imgs = [
        draw_rectangle_with_image_and_text(
            image_path=image_path, name=name, game=game, status=status, font_color=color
        ),
        draw_rectangle_with_text(text=text),
    ]
    return merge_images(imgs, direction="V")


def draw_image_bytes(name, game, image_path, status, timeStr=None):
    img = draw_image(name, game, image_path, status, timeStr)
    return convert_img(img)


def draw_image_no_view(name, game, image_path, status):
    status, color, game = get_propertys(status, game)
    return draw_rectangle_with_image_and_text(
        image_path=image_path,
        name=name,
        game=game,
        status=status,
        font_color=color,
    )


def draw_image_all(players: list):
    imgInGame = draw_rectangle_with_text(
        text="In Game",
        font_size=15,
        font_color="#FFFFFF",
        font_position="left",
        font_vertical_position="center",
        background_color="#0e141bcc",
    )
    imgNotInGame = draw_rectangle_with_text(
        text="Not In Game",
        font_size=15,
        font_color="#FFFFFF",
        font_position="left",
        font_vertical_position="center",
        background_color="#0e141bcc",
    )
    inGameList = [imgInGame]
    NoinGameList = [imgNotInGame]
    for player in players:
        img = draw_image_no_view(player["name"], player["game"], player["avatar"], player["status"])
        if player["status"] == 1:  # 在游戏中
            inGameList.append(img)
        else:
            NoinGameList.append(img)
    # print(len(inGameList),len(NoinGameList))
    try:
        imgInGame = merge_draw_image_all(inGameList) if len(inGameList) > 1 else None
        NoinGameList = merge_draw_image_all(NoinGameList) if len(NoinGameList) > 1 else None
        # 如果都为空，返回空
        if imgInGame is None and NoinGameList is None:
            return None
        elif imgInGame is None:
            return resizeAndFormat(NoinGameList)
        elif NoinGameList is None:
            return resizeAndFormat(imgInGame)
        else:
            imgs = [imgInGame, NoinGameList]
            img = merge_images(imgs)
            return resizeAndFormat(img)
    except Exception as e:
        print(e)
        return None


# TODO Rename this here and in `draw_image_all`
def resizeAndFormat(arg0):
    # 按比例缩放图片
    # new_size = int(arg0.size[0] * 0.25), int(arg0.size[1] * 0.25)
    # arg0 = arg0.resize(new_size, Image.LANCZOS)
    return convert_img(arg0)


# TODO Rename this here and in `draw_image_all`
def merge_draw_image_all(imgs):
    img = imgs[0]
    for i in range(1, len(imgs)):
        img = merge_images((img, imgs[i]), direction="V")
    # img = merge_images((img,draw_rectangle_with_text()), direction='V')
    # 按比例缩放图片
    # new_size = (int(img.size[0]*0.5), int(img.size[1]*0.5))
    # img = img.resize(new_size,Image.LANCZOS)
    return img


def test():
    # imgs = [ draw_rectangle_with_image_and_text(image_path='test.png',name='UFOLASDZeroZZZ',game='GENSHIN',status='is not playing',font_color="#62C6F3cc"),draw_rectangle_with_text(text='Press Shift+Tab to view',font_size=30,font_color='#808080',font_position='center',font_vertical_position='center') ]
    # imgs.append(draw_rectangle_with_image_and_text(game="塞尔达GENSHIN"))
    # imgs *= 5
    # img = imgs[0]
    # for i in range(1,len(imgs)):
    #    img = merge_images((img,imgs[i]), direction='V')
    # 缩放图片
    # img = img.resize((500, 200), Image.LANCZOS)
    img1 = draw_image(name="Zero", game="asldkjklasjdj", image_path="test.png", status=3, timeStr="共玩了 51.2 min ")
    img2 = draw_image(name="Zero", game="回来吧原神", image_path="test.png", status=1, timeStr="共玩了 51.2 min ")
    img3 = draw_image(
        name="Zero", game="112111111111111111111113124", image_path="test.png", status=2, timeStr="共玩了 51.2 min "
    )
    img4 = draw_image(name="Zero", game="回来吧原神", image_path="test.png", status=3, timeStr="共玩了 51.2 min ")
    img = merge_images((img1, img2, img3, img4), direction="V")
    # img = img.resize((500, 200), Image.LANCZOS)
    img.show()


if __name__ == "__main__":
    test()
