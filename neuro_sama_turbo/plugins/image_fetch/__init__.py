import datetime
import random
import re
import string
from typing import Literal

from nonebot import logger, require

require("nonebot_plugin_saa")
require("nonebot_plugin_alconna")
require("nonebot_plugin_chatrecorder")
require("nonebot_plugin_userinfo")
require("nonebot_plugin_session")
require("nonebot_plugin_session_orm")
require("nonebot_plugin_orm")
from asyncio import gather
import json
from pathlib import Path

from nonebot.adapters import Bot, Event
from nonebot.adapters.onebot.v11.message import MessageSegment
from nonebot.params import Depends
from nonebot.plugin import PluginMetadata
from nonebot_plugin_alconna import (
    Alconna,
    Args,
    Match,
    MsgId,
    UniMessage,
    on_alconna,
)
from nonebot_plugin_alconna.builtins.extensions import ReplyRecordExtension
from nonebot_plugin_session import Session, extract_session
import pyzipper

from .config import Config

__plugin_meta__ = PluginMetadata(
    name="image_fetch",
    description="",
    usage="",
    config=Config,
)


async def is_admin(event: Event):
    return event.get_user_id() in ["1025799490", "2158901807"]


image_fetch = on_alconna(
    Alconna(
        "提取图片",
    ),
    extensions=[ReplyRecordExtension()],
    aliases={"fetch_image", "图片提取"},
    use_cmd_start=True,
    priority=5,
    block=True,
)

image_fetch_dir = Path("/opt/share/image_fetch")
image_fetch_dir.mkdir(exist_ok=True, parents=True)

from .. import aiorequests
from ..locks import Locks


def get_lock(resource: str):
    return Locks.get_lock(resource)


@image_fetch.handle()
async def _(
    bot: Bot,
    event: Event,
    ext: ReplyRecordExtension,
    msg_id: MsgId,
    session: Session = Depends(extract_session),
):
    # assert await is_admin(event)

    async with get_lock(f"image_fetch_{msg_id}"):
        reply = ext.get_reply(msg_id)
        if not reply:
            await image_fetch.finish("未找到回复")
        img_list = []
        for seg in reply.msg:
            seg: MessageSegment = seg
            if seg.type == "image":
                img_list.append(seg.data["url"])
            elif seg.type == "forward":
                raw_data = json.dumps(seg.data)
                urls = re.findall(r'"url": "(.*?)"', raw_data)
                img_list.extend(urls)
        img_list = list(set(img_list))
        if not img_list:
            await image_fetch.finish("你图片呢？")
        await image_fetch.send(f"共找到{len(img_list)}张图片，正在下载~")
        img_list = [url.replace("https://", "http://") for url in img_list]
        """
        async with httpx.AsyncClient() as client:
            sem = Semaphore(2)
            tasks = []
            for url in img_list:
                async with sem:
                    tasks.append(create_task(client.get(url)))
            responses = await gather(*tasks)
        """

        async def img_download(url, retries=3):
            try:
                return await (await aiorequests.get(url)).content
            except Exception as e:
                logger.exception(f"下载图片失败：{e}，重试次数：{retries}")
                if retries:
                    return await img_download(url, retries - 1)
                return None

        responses = await gather(*[img_download(url) for url in img_list])
        await image_fetch.send(UniMessage(f"共找到{len(responses)}张图片，正在打包~").reply(id=msg_id))
        file_name = f"image_fetch_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.zip"
        """
        bio = BytesIO()
        with ZipFile(bio, "w") as zf:
            for i, response in enumerate(responses):
                zf.writestr(f"{i}.jpg", response)
        bio.seek(0)
        full_path = image_fetch_dir / file_name
        with open(full_path, "wb") as f:
            f.write(bio.getvalue())
        """

        def random_password(mode: Literal["digit", "letter", "mix"] = "mix", length: int = 8):
            if mode == "digit":
                return "".join(random.choices(string.digits, k=length))
            elif mode == "letter":
                return "".join(random.choices(string.ascii_letters, k=length))
            elif mode == "mix":
                return "".join(random.choices(string.digits + string.ascii_letters, k=length))

        password_str = random_password()
        password = password_str.encode()
        full_path = image_fetch_dir / file_name
        with pyzipper.AESZipFile(full_path, "w", compression=pyzipper.ZIP_DEFLATED, encryption=pyzipper.WZ_AES) as zf:
            zf.setpassword(password)
            for i, response in enumerate(responses):
                zf.writestr(f"{i}.jpg", response)
        await image_fetch.send(UniMessage(f"打包已完成，正在上传群文件，解压密码为{password_str}").reply(id=msg_id))
        await bot.upload_group_file(group_id=event.group_id, file=str(full_path), name=file_name)
        # await image_fetch.send(f"打包完成，请访问该链接下载：https://share.nyanners.moe/image_fetch/{file_name}")


from .github_download import download_github_folder

github_folder_fetch = on_alconna(
    Alconna(
        "提取github文件夹",
        Args["repo_path", str],
    ),
    extensions=[ReplyRecordExtension()],
    aliases={"gh下载", "github下载"},
    use_cmd_start=True,
    priority=5,
    block=True,
)


@github_folder_fetch.handle()
async def _(bot: Bot, event: Event, repo_path: Match[str], msg_id: MsgId):
    # assert await is_admin(event)
    if not repo_path.available:
        await github_folder_fetch.finish("请输入repo路径")

    async with get_lock(f"gh_folder_{repo_path.result}"):
        repo_path = repo_path.result
        file_name, full_path = await download_github_folder(repo_path)
        # await github_folder_fetch.finish(
        #     f"打包完成，请访问该链接下载：https://share.nyanners.moe/github_folder/{urllib.parse.quote(file_name)}"
        # )
        await github_folder_fetch.send(UniMessage("打包已完成，正在上传群文件").reply(id=msg_id))
        await bot.upload_group_file(group_id=event.group_id, file=str(full_path), name=file_name)
