import asyncio
from asyncio import Semaphore, create_task, gather
from io import BytesIO
import json
from pathlib import Path
import re
import urllib.parse
from zipfile import ZipF<PERSON>

from bs4 import BeautifulSoup
from nonebot import logger

from .. import aiorequests

github_folder_path = Path("/opt/share/github_folder")
github_folder_path.mkdir(exist_ok=True, parents=True)
proxies = {
    "http": "http://localhost:7899",
    "https": "http://localhost:7899",
}


async def download_github_folder(repo_path):
    user_repo = "/".join(repo_path.split("/")[0:2])
    folder_name = "/".join(repo_path.split("/")[2:])
    repo_url = f"https://github.com/{user_repo}/tree/master/{folder_name}"
    logger.info(f"repo_url: {repo_url}")
    response = await aiorequests.get(repo_url, proxies=proxies)
    r = await response.text

    # 新的解析方式：从JavaScript中提取JSON数据
    source_urls = []

    # 尝试从嵌入式JSON中提取
    pattern = r'<script type="application/json" data-target="react-app.embeddedData">(.*?)</script>'
    match = re.search(pattern, r, re.DOTALL)

    if match:
        try:
            # 解析JSON数据
            data = json.loads(match.group(1))
            payload = data.get("payload", {})
            repo_info = payload.get("repo", {})
            owner = repo_info.get("ownerLogin")
            repo_name = repo_info.get("name")
            branch = payload.get("refInfo", {}).get("name", "master")

            # 获取文件列表
            items = payload.get("tree", {}).get("items", [])

            # 构建文件URL列表
            for item in items:
                if item.get("contentType") == "file":
                    file_path = item.get("path")
                    file_ext = file_path.split(".")[-1].lower()

                    if file_ext in ["png", "jpg", "jpeg", "gif", "svg", "json", "txt", "md"]:
                        # 构建原始内容URL
                        raw_url = f"https://raw.githubusercontent.com/{owner}/{repo_name}/{branch}/{file_path}"
                        source_urls.append((raw_url, file_ext))

            logger.info(f"从JSON数据中提取到 {len(source_urls)} 个文件链接")
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")

    # 如果没有从JSON中获取到链接，尝试使用原来的方法
    if not source_urls:
        logger.info("尝试使用旧的解析方法...")
        soup = BeautifulSoup(r, "html.parser")
        for link in soup.find_all("a"):
            href = link.get("href")
            logger.info(f"href: {href}")
            if href and href.endswith(("png", "jpg", "jpeg", "gif", "svg", "json", "txt", "md")):
                file_url = "https://raw.githubusercontent.com" + href.replace("/blob/", "/")
                ext = file_url.split(".")[-1]
                source_urls.append((file_url, ext))

    source_urls = list(set(source_urls))
    logger.info(f"source_urls: {source_urls}")
    sem = Semaphore(5)
    tasks = []
    results = []

    async def download_file(file_url, ext):
        async with sem:
            logger.info(f"Downloading {file_url}")
            retries = 3
            for attempt in range(retries):
                try:
                    response = await aiorequests.get(file_url, proxies=proxies)
                    r = await response.content
                    file_name = file_url.split("/")[-1]
                    file_name = urllib.parse.unquote(file_name)
                    results.append((file_name, r, ext))
                    break
                except Exception as e:
                    logger.error(f"Error downloading {file_url}: {e}")
                    if attempt < retries - 1:
                        logger.info(f"Retrying {file_url} (attempt {attempt + 1})")
                        await asyncio.sleep(1)
                    else:
                        logger.error(f"Failed to download {file_url} after {retries} attempts")

    for url, ext in source_urls:
        tasks.append(create_task(download_file(url, ext)))
    await gather(*tasks)

    # 打包
    bio = BytesIO()
    with ZipFile(bio, "w") as zf:
        for file_name, file_content, ext in results:
            zf.writestr(file_name, file_content)

    bio.seek(0)

    file_name = f"{repo_path.replace('/', '_')}.zip"
    file_name = urllib.parse.unquote(file_name)
    full_path = github_folder_path / file_name
    with open(full_path, "wb") as f:
        f.write(bio.getvalue())
    return file_name, full_path
