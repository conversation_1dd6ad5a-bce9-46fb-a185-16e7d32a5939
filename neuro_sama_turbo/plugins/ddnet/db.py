from logging import getLogger

logger = getLogger(__name__)
from bson import CodecOptions
from motor.motor_asyncio import AsyncIOMotorClient
from pydantic import BaseModel, Field
import pytz

shanghai_tz = pytz.timezone("Asia/Shanghai")
codec_opt = CodecOptions(tz_aware=True, tzinfo=shanghai_tz)

mongo_host = "localhost"  # global_config.mongo_host
mongo_user = "root"  # global_config.mongo_user
mongo_pass = "passw0rd@zero"  # global_config.mongo_pass

import urllib.parse

mongo_user = urllib.parse.quote_plus(mongo_user)
mongo_pass = urllib.parse.quote_plus(mongo_pass)

client = AsyncIOMotorClient(f"mongodb://{mongo_host}:27017/")
db = client.gpt_prompt
user_collection = db.user


class User(BaseModel):
    user_id: int = Field(..., alias="user_id")
    name: str | None = None


async def set_index():
    await user_collection.create_index("user_id", unique=True)


async def get_user(user_id: int):
    user = await user_collection.find_one({"user_id": user_id})
    if user:
        u = User(**user)
        if u.name:
            return u
    raise ValueError("你还没有绑定账号哦")


async def create_user(user_id: int, name: str = None):
    user = User(user_id=user_id, name=name)
    await user_collection.insert_one(user.dict())


async def set_user(user_id: int, name: str):
    if not await user_collection.find_one({"user_id": user_id}):
        await create_user(user_id, name)
    await user_collection.update_one({"user_id": user_id}, {"$set": {"name": name}})
