from nonebot import get_driver, get_plugin_config
from nonebot.plugin import PluginMetadata

from .config import Config
from .db import get_user, set_user

__plugin_meta = PluginMetadata(
    name="ddnet",
    description="",
    usage="",
    config=Config,
)

config = get_plugin_config(Config)

# query_url: https://ddnet.org/players/{name}

from nonebot_plugin_alconna import Alconna, Args, Image, Match, Subcommand, on_alconna

command_ddnet = on_alconna(
    Alconna(
        "dd",
        Subcommand(
            "bind",
            Args["name", str],
            alias=["b", "绑定"],
        ),
        Subcommand(
            "points",
            Args["name?", str],
            alias=["p", "查分"],
        ),
    )
)
from nonebot.adapters.onebot.v11 import Event
from playwright.async_api import Playwright, async_playwright


class DDNet:
    global_playwright: Playwright = None
    global_browser = None

    @staticmethod
    async def get_browser():
        if DDNet.global_browser is None:
            DDNet.global_playwright = await async_playwright().start()
            DDNet.global_browser = await DDNet.global_playwright.chromium.launch()
        return DDNet.global_browser

    @staticmethod
    async def close_browser():
        if DDNet.global_browser is not None:
            await DDNet.global_browser.close()
            DDNet.global_browser = None
            await DDNet.global_playwright.stop()
            DDNet.global_playwright = None

    @staticmethod
    async def get_points(name: str):
        try:
            browser = await DDNet.get_browser()
            page = await browser.new_page()
            await page.goto(f"https://ddnet.org/players/{name}")
            await page.evaluate("document.getElementById('playersearch')?.remove()")
            await page.evaluate("document.querySelectorAll('input').forEach(e => e.remove())")
            element = await page.query_selector("#global")
            if element:
                screenshot = await element.screenshot()
                await page.close()
                return screenshot
            else:
                await page.close()
                raise ValueError("未找到该玩家")
        except Exception as e:
            raise e


import atexit


@atexit.register
async def close_browser():
    await DDNet.close_browser()


@command_ddnet.assign("points")
async def points_ddnet_cmd(name: Match[str], event: Event):
    try:
        if name.available:
            rname = name.result
            await command_ddnet.send(Image(raw=await DDNet.get_points(rname)))
        else:
            user_id = event.user_id
            try:
                user = await get_user(user_id)
                await command_ddnet.send(Image(raw=await DDNet.get_points(user.name)))
            except Exception as e:
                await command_ddnet.send(str(e))
    except Exception as e:
        await command_ddnet.send(str(e))


@command_ddnet.assign("bind")
async def bind_ddnet_cmd(name: Match[str], event: Event):
    if name.available:
        rname = name.result
        user_id = event.user_id
        try:
            await set_user(user_id, rname)
            await command_ddnet.send("绑定成功")
        except Exception as e:
            await command_ddnet.send(str(e))
