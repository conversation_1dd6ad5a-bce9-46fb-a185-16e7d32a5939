from __future__ import annotations

from typing import Any

from pydantic import BaseModel


class RecentHistoryItem(BaseModel):
    hq: bool
    pricePerUnit: int
    quantity: int
    timestamp: int
    onMannequin: bool
    worldName: str
    worldID: int
    buyerName: str
    total: int


class Listing1(BaseModel):
    lastReviewTime: int
    pricePerUnit: int
    quantity: int
    stainID: int
    worldName: str
    worldID: int
    creatorName: str
    creatorID: Any
    hq: bool
    isCrafted: bool
    listingID: str
    materia: list
    onMannequin: bool
    retainerCity: int
    retainerID: str
    retainerName: str
    sellerID: Any
    total: int
    tax: int


class RecentHistoryItem1(BaseModel):
    hq: bool
    pricePerUnit: int
    quantity: int
    timestamp: int
    onMannequin: bool
    worldName: str
    worldID: int
    buyerName: str
    total: int


class ItemInfo(BaseModel):
    itemID: int
    lastUploadTime: int
    listings: list[Listing1]
    recentHistory: list[RecentHistoryItem1]
    dcName: str


class Model(BaseModel):
    itemIDs: list[int]
    items: dict[str, ItemInfo]
    dcName: str
    unresolvedItems: list
