import asyncio
from collections import defaultdict
from collections.abc import AsyncIterator, Callable
import csv
from dataclasses import dataclass
import datetime
from functools import lru_cache
import os
import re
import threading
from typing import Any

import aiohttp
from motor.motor_asyncio import AsyncIOMotorClient
from nonebot import logger as logging
from nonebot import require
from pydantic import BaseModel, Field
import redis.asyncio as redis_asyncio

require("nonebot_plugin_htmlrender")
from nonebot_plugin_htmlrender import template_to_pic

templates_path = os.path.join(os.path.dirname(__file__), "templates")
this_dir = os.path.dirname(__file__)

# 全局Redis对象
redis = redis_asyncio.Redis(host="localhost", port=6379, db=0)
mongo_client = AsyncIOMotorClient("mongodb://localhost:27017/")
collection = mongo_client["market_hunter"]["market_data"]
# 配置大区和小区
MAJOR_REGION = "陆行鸟"
SUB_REGIONS = ["沃仙曦染", "晨曦王座"]

# 添加全局缓存变量和锁
_item_data_cache = None
_cache_lock = threading.Lock()
_cache_timestamp = 0


class StreamProcessor:
    """流式数据处理器，使用迭代器模式优化整个数据流程"""

    def __init__(self, collection, batch_size: int = 1000, chunk_size: int = 100):
        self.collection = collection
        self.batch_size = batch_size  # MongoDB查询批次大小
        self.chunk_size = chunk_size  # 处理块大小

    async def stream_cached_items(self, item_ids: list[str]) -> AsyncIterator[tuple[str, dict]]:
        """流式获取缓存数据的异步迭代器"""
        logging.info(f"开始流式处理 {len(item_ids)} 个物品的缓存数据")
        start_time = datetime.datetime.now()
        processed_count = 0

        # 分批处理item_ids，避免MongoDB查询过大
        for i in range(0, len(item_ids), self.batch_size):
            batch_ids = item_ids[i : i + self.batch_size]

            # 使用游标进行流式查询
            cursor = self.collection.find(
                {"id": {"$in": batch_ids}},
                {"_id": 0, "id": 1, "data": 1},  # 只返回需要的字段
            )

            # 流式处理每个文档
            async for document in cursor:
                yield document["id"], document["data"]
                processed_count += 1

                # 定期输出进度
                if processed_count % 1000 == 0:
                    elapsed = (datetime.datetime.now() - start_time).total_seconds()
                    rate = processed_count / elapsed if elapsed > 0 else 0
                    logging.info(f"已流式处理 {processed_count} 个物品，速度: {rate:.1f} 物品/秒")

        elapsed = (datetime.datetime.now() - start_time).total_seconds()
        logging.info(f"流式处理完成，共处理 {processed_count} 个物品，总耗时: {elapsed:.2f}秒")

    async def stream_market_analysis(
        self,
        item_ids: list[str],
        id_name_map: dict[str, dict],
        analyzer: "DataAnalyzer",
    ) -> AsyncIterator[dict[str, Any]]:
        """流式市场分析的异步迭代器"""
        logging.info("开始流式市场分析")
        processed_items = 0
        results_count = 0

        # 创建数据流
        item_data_map = {}
        async for item_id, item_data in self.stream_cached_items(item_ids):
            item_data_map[item_id] = item_data

            # 当累积到一定数量时进行批量分析
            if len(item_data_map) >= self.chunk_size:
                async for result in self._analyze_chunk(item_data_map, id_name_map, analyzer):
                    yield result
                    results_count += 1

                processed_items += len(item_data_map)
                logging.info(f"已分析 {processed_items} 个物品，发现 {results_count} 个可倒卖物品")
                item_data_map.clear()

        # 处理剩余的数据
        if item_data_map:
            async for result in self._analyze_chunk(item_data_map, id_name_map, analyzer):
                yield result
                results_count += 1
            processed_items += len(item_data_map)

        logging.info(f"流式分析完成，共分析 {processed_items} 个物品，发现 {results_count} 个可倒卖物品")

    async def _analyze_chunk(
        self,
        item_data_map: dict[str, Any],
        id_name_map: dict[str, dict],
        analyzer: "DataAnalyzer",
    ) -> AsyncIterator[dict[str, Any]]:
        """分析数据块的异步迭代器"""
        for item_id, item_data in item_data_map.items():
            item_info = id_name_map.get(item_id)
            if not item_info or not item_data:
                continue

            # 使用现有的分析逻辑
            sub_region_listings, global_listings, sub_histories, global_history = analyzer.process_market_data(
                item_data
            )
            if not global_listings:
                continue

            # 处理每个小区的数据
            for sub_region in analyzer.sub_regions:
                result = analyzer._analyze_single_region(
                    item_id,
                    item_info,
                    item_data,
                    sub_region,
                    sub_region_listings,
                    global_listings,
                    sub_histories,
                )
                if result:
                    yield result

    def collect_stream_results(
        self, stream: AsyncIterator[dict[str, Any]], max_results: int | None = None
    ) -> AsyncIterator[list[dict[str, Any]]]:
        """收集流式结果为批次"""

        async def _collect():
            results = []
            count = 0
            async for result in stream:
                results.append(result)
                count += 1

                if len(results) >= self.chunk_size or (max_results and count >= max_results):
                    yield results
                    results = []

                    if max_results and count >= max_results:
                        break

            if results:
                yield results

        return _collect()


@dataclass
class RecipeAnalysisResult:
    """配方分析结果"""

    total_cost: int  # 总成本
    total_revenue: int  # 总收入
    profit: int  # 利润
    materials: dict[str, int]  # 材料及数量
    levels: dict[int, list[dict]]  # 各层级的节点
    all_items: set[str]  # 所有涉及的物品ID


# 在 MAJOR_REGION 定义后添加以下模型定义
class MarketItem(BaseModel):
    id: str
    name: str
    local: int  # 本小区最低价
    global_price: int = Field(alias="global")  # 大区最低价
    lowest_region: str  # 最低价所在小区
    sub_region: str  # 本小区
    last_price: int  # 最近一次交易价格
    last_time: float  # 最近一次交易时间
    profit: int  # 利润
    note: str  # 备注
    recent_history: list[dict[str, Any]] = []  # 最近历史记录
    last_trade_time: float = Field(default_factory=lambda: datetime.datetime.now().timestamp())
    local_price: int = 0  # 本小区最低价

    @property
    def profit_ratio(self) -> float:
        """利润率"""
        return self.profit / self.local if self.local > 0 else 0

    @property
    def daily_volume(self) -> int:
        """日均交易量"""
        if not self.recent_history:
            return 0
        time_span = self.recent_history[0]["timestamp"] - self.recent_history[-1]["timestamp"]
        if time_span <= 0:
            return 0
        return int(len(self.recent_history) / (time_span / 86400))

    @property
    def price_volatility(self) -> float:
        """价格波动率"""
        if not self.recent_history:
            return 0
        prices = [record["pricePerUnit"] for record in self.recent_history]
        if not prices:
            return 0
        avg = sum(prices) / len(prices)
        variance = sum((p - avg) ** 2 for p in prices) / len(prices)
        return (variance**0.5) / avg if avg > 0 else 0

    @property
    def last_trade_days(self) -> float:
        """距离最后一次交易的天数"""
        return (datetime.datetime.now().timestamp() - self.last_trade_time) / 86400

    class Config:
        allow_population_by_field_name = True


class FilterRule(BaseModel):
    name: str
    condition: Callable[[MarketItem], bool]
    description: str | None = ""

    class Config:
        arbitrary_types_allowed = True


class MarketFilter:
    def __init__(self):
        self.rules: list[FilterRule] = []
        self._init_default_rules()

    def _init_default_rules(self):
        """初始化默认过滤规则"""
        self.add_rule(
            name="min_history_count",
            condition=lambda item: len(item.recent_history) >= 5,
            description="最少历史记录数量不低于5",
        )

        self.add_rule(
            name="price_not_999",
            condition=lambda item: not any(pattern.match(str(item.local_price)) for pattern in [re.compile(r"9+")]),
            description="价格不能为9",
        )

        self.add_rule(
            name="recent_trades",
            condition=lambda item: (datetime.datetime.now().timestamp() - item.last_trade_time <= 86400 * 3),
            description="最近3天内有交易",
        )

    def add_rule(self, name: str, condition: Callable[[MarketItem], bool], description: str = ""):
        """添加新的过滤规则"""
        rule = FilterRule(name=name, condition=condition, description=description)
        self.rules.append(rule)

    def remove_rule(self, name: str):
        """移除指定名称的过滤规则"""
        self.rules = [rule for rule in self.rules if rule.name != name]

    def apply_filters(self, items: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """应用所有过滤规则"""
        # 转换为 Pydantic 模型
        market_items = [MarketItem.model_validate(item) for item in items]

        # 应用过滤规则
        filtered_items = market_items
        for rule in self.rules:
            filtered_items = [item for item in filtered_items if rule.condition(item)]

        # 转换回字典
        return [item.dict(by_alias=True) for item in filtered_items]


class DataFetcher:
    def __init__(self):
        self.redis = redis
        self.semaphore = asyncio.Semaphore(5)  # 控制最大并发数为5
        self.redis_semaphore = asyncio.Semaphore(20)
        # self.mongo_semaphore = asyncio.Semaphore(20)
        self.mongo_client = mongo_client
        self.collection = collection
        # 设置优化的索引
        _ = asyncio.create_task(self._setup_indexes())
        # 初始化流处理器
        self.stream_processor = StreamProcessor(self.collection)

    async def _setup_indexes(self):
        """设置MongoDB索引以优化查询性能"""
        try:
            # 创建id字段的唯一索引（查询最常用）
            await self.collection.create_index("id", unique=True)
            logging.info("已创建id唯一索引")

            # 创建lastUploadTime索引，用于缓存过期清理
            await self.collection.create_index("lastUploadTime")
            logging.info("已创建lastUploadTime索引")

            # 可选：创建复合索引用于更复杂的查询
            # self.collection.create_index([("id", 1), ("lastUploadTime", -1)])

        except Exception as e:
            logging.error(f"设置索引失败：{e}")
            # 不抛出异常，让程序继续运行

    async def clear_cache(self):
        try:
            # await self.redis.flushall()
            await self.collection.drop()
            logging.info("缓存已清空")
        except Exception as e:
            logging.error(f"清空缓存失败：{e}")

    async def clean_expired_cache(self, expire_hours: int = 5):
        """清理过期的缓存数据，提升数据库性能"""
        try:
            expire_timestamp = datetime.datetime.now().timestamp() - (expire_hours * 3600)
            result = await self.collection.delete_many({"lastUploadTime": {"$lt": expire_timestamp}})
            logging.info(f"已清理 {result.deleted_count} 条过期缓存数据（超过{expire_hours}小时）")
            return result.deleted_count
        except Exception as e:
            logging.error(f"清理过期缓存失败：{e}")
            return 0

    async def get_cache(self, item_id):
        data = await self.collection.find_one({"id": item_id})
        if data:
            return data.get("data", {})
        return None
        """
        async with self.redis_semaphore:
            try:
                cache_key = f"market_cache:{MAJOR_REGION}:{item_id}"
                data = await self.redis.get(cache_key)
                if data:
                    data_json = json.loads(data)
                    last_upload_time = data_json.get("lastUploadTime", 0)
                    current_time = datetime.datetime.now().timestamp()
                    if current_time - last_upload_time < 18000:  # 缓存有效期5小时
                        # logging.info(f"从缓存获取物品 {item_id} 的数据")
                        return data_json
            except Exception as e:
                logging.error(f"读取缓存失败：{e}")
            return None
        """

    async def save_cache(self, item_id, data):
        """
        try:
            cache_key = f"market_cache:{MAJOR_REGION}:{item_id}"
            data["lastUploadTime"] = datetime.datetime.now().timestamp()
            await self.redis.set(
                cache_key, json.dumps(data, ensure_ascii=False), ex=18000
            )
        except Exception as e:
            logging.error(f"保存缓存失败：{e}")
        """
        try:
            save_data = {
                "id": item_id,
                "data": data,
                "lastUploadTime": datetime.datetime.now().timestamp(),
            }
            await self.collection.update_one({"id": item_id}, {"$set": save_data}, upsert=True)
        except Exception as e:
            logging.error(f"保存缓存失败：{e}")

    async def get_cache_items(self, item_ids: list[str]) -> dict[str, dict]:
        """使用流处理器获取缓存数据"""
        result = {}
        async for item_id, item_data in self.stream_processor.stream_cached_items(item_ids):
            result[item_id] = item_data
        return result

    async def save_cache_items(self, items: list[dict]):
        operations = []
        for item in items:
            operations.append(
                {
                    "updateOne": {
                        "filter": {"id": item["id"]},
                        "update": {
                            "$set": {
                                "data": item,
                                "lastUploadTime": datetime.datetime.now().timestamp(),
                            }
                        },
                        "upsert": True,
                    }
                }
            )

        if operations:
            await self.collection.bulk_write(operations)

    async def fetch_batch(self, session, batch_ids):
        async with self.semaphore:  # 使用信号量控制并发
            url = f"https://universalis.app/api/v2/{MAJOR_REGION}/{','.join(map(str, batch_ids))}?entries=100"
            try:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        logging.info(f"从网络获取物品数据，数量：{len(data.get('items', {}))}，ids：{batch_ids}")
                        # 保存缓存返回数据
                        for item_id, item_data in data.get("items", {}).items():
                            await self.save_cache(item_id, item_data)
                        return data.get("items", {})
                    else:
                        logging.error(f"请求 {url} 失败，状态码 {response.status}")
                        return {}
            except Exception as e:
                logging.error(f"请求 {url} 出错：{e}")
                return {}

    async def fetch_data(self, item_ids):
        uncached_ids = []
        """
        # 首先检查每个物品的缓存
        cache_tasks = [self.get_cache(item_id) for item_id in item_ids]
        cache_results = await asyncio.gather(*cache_tasks)
        
        for item_id, cached_data in zip(item_ids, cache_results):
            if cached_data:
                results["items"][str(item_id)] = cached_data
                logging.info(f"从缓存获取物品 {item_id} 的数据")
            else:
                uncached_ids.append(item_id)
        """
        uncached_ids = item_ids
        if uncached_ids:
            async with aiohttp.ClientSession() as session:
                batch_size = 8
                batches = [uncached_ids[i : i + batch_size] for i in range(0, len(uncached_ids), batch_size)]

                # 并发请求所有批次
                fetch_tasks = [self.fetch_batch(session, batch) for batch in batches]
                await asyncio.gather(*fetch_tasks)
                logging.info("已获取并缓存物品数据")

    # 使用流处理器获取缓存数据
    async def get_cached_data_stream(self, item_ids):
        """流式获取缓存数据"""
        return self.stream_processor.stream_cached_items(item_ids)

    # 只从缓存取出数据
    async def get_cached_data(self, item_ids):
        # cache_tasks = [self.get_cache(item_id) for item_id in item_ids]
        # cache_results = await asyncio.gather(*cache_tasks)
        # return {item_id: cached_data for item_id, cached_data in zip(item_ids, cache_results) if cached_data}
        start_time = datetime.datetime.now()
        result = {
            "items": await self.get_cache_items(item_ids),
        }
        logging.info(f"已获取缓存的物品数据，耗时：{datetime.datetime.now() - start_time}")
        return result


class DataAnalyzer:
    def __init__(self, sub_regions, price_threshold=100000, low_price_limit=10000):
        self.sub_regions = sub_regions
        self.price_threshold = price_threshold
        self.low_price_limit = low_price_limit
        self.patterns = [re.compile(r"9+")]

    def process_market_data(self, item_data):
        """处理市场数据，返回分后的listings和历史记录

        Args:
            item_data (dict): 物品的市场数据

        Returns:
            tuple: (
                dict: 小区listings {region: [listings]},
                list: 全区listings,
                dict: 小区历史记录 {region: [history]},
                list: 全区历史记录
            )
        """
        listings = item_data.get("listings", [])
        history = item_data.get("recentHistory", [])

        if not listings:
            return None, None, None, None

        # 根据 worldName 分类
        sub_region_listings = {region: [] for region in self.sub_regions}
        global_listings = listings

        sub_histories = {region: [] for region in self.sub_regions}
        global_history = history

        # 处理历史记录
        for record in history:
            world = record.get("worldName")
            if world in self.sub_regions:
                sub_histories[world].append(record)

        # 处理当前挂单
        for listing in listings:
            world = listing.get("worldName")
            if world in self.sub_regions:
                sub_region_listings[world].append(listing)

        return sub_region_listings, global_listings, sub_histories, global_history

    def _analyze_single_region(
        self,
        item_id,
        item_info,
        item_data,
        sub_region,
        sub_region_listings,
        global_listings,
        sub_histories,
    ):
        """分析单个小区的数据，提取为独立方法以便流式处理使用"""
        local_listings = sub_region_listings.get(sub_region, [])
        if not local_listings:
            return None

        local_price = local_listings[0]["pricePerUnit"] if local_listings else 0

        if any(pattern.match(str(local_price)) for pattern in self.patterns):
            return None

        # 检查历史出售条目
        recent_history = sub_histories[sub_region]
        if len(recent_history) < 5:
            return None
        if (
            recent_history[-1]["timestamp"] - recent_history[0]["timestamp"] > 86400 * 3
            or recent_history[0].get("pricePerUnit", 0) < local_price * 0.5
        ):
            return None
        if (
            datetime.datetime.now().timestamp() - recent_history[0]["timestamp"] > 86400 * 3
            or datetime.datetime.now().timestamp() - recent_history[-1]["timestamp"] > 86400 * 7
        ):
            return None

        global_price = global_listings[0]["pricePerUnit"] if global_listings else 0

        if global_price == 0 and local_price == 0:  # 价格为0，跳过
            return None
        if local_price == 0:  # 小区价格为0，跳过
            return None
        if global_price == 0 and local_price > 0:  # 大区价格为0，小区价格不为0，疑似异常
            logging.info(
                f"物品：{item_info['name']}，大区 {MAJOR_REGION} 价格为0，小区 {sub_region} 价格：{local_price}，疑似异常"
            )
            return None

        # 价格差分析
        if local_price * 0.97 - global_price * 1.05 > self.price_threshold and local_price > self.low_price_limit:
            profit = int(local_price * 0.97 - global_price * 1.05)
            logging.info(
                f"发现可倒卖物品：[{item_id}]{item_info['name']}，小区 {sub_region} 价格：{local_price}，大区价格：{global_price}，差价：{profit}"
            )
            return {
                "id": item_id,
                "name": item_info["name"],
                "local": local_price,
                "global": global_price,
                "lowest_region": global_listings[0].get("worldName", "未知"),
                "sub_region": sub_region,
                "last_price": recent_history[0].get("pricePerUnit", 0),
                "last_time": recent_history[0]["timestamp"],
                "profit": profit,
                "note": (
                    f"小区 {sub_region} 最低价：{int(local_price * 0.97)}（税后3%），"
                    f"大区最低价：{int(global_price * 1.05)}（税后5%），"
                    f"差价：{profit}"
                ),
            }
        return None

    def analyze_all(self, major_data, id_name_map):
        results = []
        total_items = len(id_name_map)
        processed_items = 0

        items = major_data.get("items", {})

        for item_id, item_info in id_name_map.items():
            item_data = items.get(str(item_id))
            if not item_data:
                continue

            sub_region_listings, global_listings, sub_histories, _global_history = self.process_market_data(item_data)
            if not global_listings:
                continue

            # 处理每个小区的数据
            for sub_region in self.sub_regions:
                result = self._analyze_single_region(
                    item_id,
                    item_info,
                    item_data,
                    sub_region,
                    sub_region_listings,
                    global_listings,
                    sub_histories,
                )
                if result:
                    results.append(result)

            processed_items += 1
            if processed_items % 100 == 0 or processed_items == total_items:
                logging.info(f"已分析 {processed_items}/{total_items} 个物品，发现 {len(results)} 个可倒卖物品")
        return results

    async def analyze_all_stream(
        self, item_ids: list[str], id_name_map: dict[str, dict], fetcher: DataFetcher
    ) -> AsyncIterator[dict[str, Any]]:
        """流式分析所有数据"""
        async for result in fetcher.stream_processor.stream_market_analysis(item_ids, id_name_map, self):
            yield result

    # 找到市场里某雇员售卖的所有物品
    def find_items_by_seller(self, major_data, retainerName, id_name_map):
        results = []
        items = major_data.get("items", {})
        for item_id, item_data in items.items():
            listings = item_data.get("listings", [])
            if not listings:
                continue
            for listing in listings:
                if listing.get("retainerName") == retainerName:
                    results.append(
                        {
                            "id": item_id,
                            "name": id_name_map.get(item_id, {}).get("name", item_id),
                            "price": listing.get("pricePerUnit"),
                            "retainer": listing.get("retainerName"),
                            "world": listing.get("worldName"),
                            "quantity": listing.get("quantity"),
                        }
                    )
        return results

    # 支持正则的查找（适合模糊查找）
    def find_items_by_seller_regex(self, major_data, retainerName: str, id_name_map):
        logging.info(retainerName)
        results = []
        items = major_data.get("items", {})
        for item_id, item_data in items.items():
            listings = item_data.get("listings", [])
            if not listings:
                continue
            for listing in listings:
                # if re.search(retainerName, listing.get("retainerName")):
                pattern = re.compile(retainerName)
                if pattern.search(listing.get("retainerName")):
                    results.append(
                        {
                            "id": item_id,
                            "name": id_name_map.get(item_id, {}).get("name", item_id),
                            "price": listing.get("pricePerUnit"),
                            "retainer": listing.get("retainerName"),
                            "world": listing.get("worldName"),
                            "quantity": listing.get("quantity"),
                        }
                    )
        return results

    def calculate_recipe_profit(
        self,
        recipe_result: RecipeAnalysisResult,
        market_data: dict,
        target_region: str,
        items_map: dict,
        level: int = 0,
        with_filter: bool = False,
    ):
        """计算配方的利润

        Args:
            recipe_result: 配方分析结果
            market_data: 市场数据
            target_region: 目标小区
            items_map: 物品ID到名称的映射

        Returns:
            dict: 包含详细成本和收益信息的字典
        """
        materials_detail = []
        total_cost = 0
        # 计算成品收入
        result_id = recipe_result.levels[0][0]["item_id"]
        result_amount = recipe_result.levels[0][0]["amount"]
        result_name = items_map.get(result_id, {}).get("name", result_id)
        result_market = market_data.get("items", {}).get(str(result_id))
        if not result_market:
            return {"success": False, "error": f"缺少物品 {result_name} 的市场数据"}
        # 获取目标小区的最低价
        target_listings = [
            listing for listing in result_market.get("listings", []) if listing["worldName"] == target_region
        ]

        if not target_listings:
            return {
                "success": False,
                "error": f"小区 {target_region} 没有物品 {result_name} 的挂单数据",
            }

        if with_filter:
            # 只看成品是否异常
            history = result_market.get("recentHistory", [])
            target_history = [record for record in history if record["worldName"] == target_region]
            if (
                len(target_history) < 5
                or (datetime.datetime.now().timestamp() - target_history[0]["timestamp"] > 86400 * 3)
                or (target_history[0].get("pricePerUnit", 0) < target_listings[0]["pricePerUnit"] * 0.5)
            ):
                if target_listings[0]["pricePerUnit"] < 1000000 or int(result_id) < 30000 or int(result_id) in [42056]:
                    return {
                        "success": False,
                        "error": f"物品 {result_name} 最近3天内没有交易，或最近一次交易价格低于最低价的50%",
                    }
            # 价格不超过1000w
            if target_listings[0]["pricePerUnit"] > 7000000:
                return {"success": False, "error": f"物品 {result_name} 价格异常"}

        # 计算材料成本
        materials = recipe_result.materials
        if level != 0:
            materials_list = recipe_result.levels[level]
            materials = {data["item_id"]: data["amount"] for data in materials_list}
        for item_id, amount in materials.items():
            item_market = market_data.get("items", {}).get(str(item_id))
            if not item_market or not item_market.get("listings"):
                """return {
                    "success": False,
                    "error": f"缺少物品 {items_map.get(item_id, {}).get('name', item_id)} 的市场数据"
                }"""
                materials_detail.append(
                    {
                        "id": item_id,
                        "name": items_map.get(item_id, {}).get("name", item_id),
                        "amount": amount,
                        "unit_price": 0,
                        "total_price": 0,
                        "region": "未知",
                        "listings_count": 0,
                    }
                )
                continue

            listings = item_market["listings"]
            min_price_listing = min(listings, key=lambda x: x["pricePerUnit"])
            min_price = min_price_listing["pricePerUnit"]
            min_price_region = min_price_listing["worldName"]

            item_cost = int(min_price * amount * 1.05)  # 考虑5%税
            total_cost += item_cost

            materials_detail.append(
                {
                    "id": item_id,  # 材料ID
                    "name": items_map.get(item_id, {}).get("name", item_id),  # 材料名称
                    "amount": amount,  # 材料数量
                    "unit_price": int(min_price * 1.05),  # 材料最低价
                    "total_price": item_cost,  # 材料总成本
                    "region": min_price_region,  # 材料最低价所在小区
                    "listings_count": len(
                        [l for l in listings if l["worldName"] == min_price_region]
                    ),  # 材料最低价所在小区挂单数量
                }
            )
        materials_detail = sorted(materials_detail, key=lambda x: int(x["id"]), reverse=True)

        sell_price = target_listings[0]["pricePerUnit"]
        total_revenue = sell_price * result_amount * 0.97  # 考虑3%税
        profit = total_revenue - total_cost

        return {
            "success": True,
            "result": {
                "id": result_id,  # 成品ID
                "name": result_name,  # 成品名称
                "amount": result_amount,  # 成品数量
                "materials": materials_detail,  # 材料清单
                "total_cost": int(total_cost),  # 总成本
                "unit_sell_price": sell_price,  # 预期售价
                "unit_sell_price_taxed": int(sell_price * 0.97),  # 预期售价（税后）
                "total_revenue": int(total_revenue),  # 总收入
                "profit": int(profit),  # 预期利润
                "target_region": target_region,  # 目标小区
                "target_listings_count": len(target_listings),  # 目标小区挂单数量
            },
        }


class MarketAnalysis:
    lock = asyncio.Lock()

    @staticmethod
    def get_filtered_item_data(force_refresh=False):
        """获取过滤后的物品数据，带缓存机制

        Args:
            force_refresh: 是否强制刷新缓存

        Returns:
            list: 过滤后的物品数据列表
        """
        global _item_data_cache, _cache_timestamp

        item_data_path = os.path.join(this_dir, "Item.csv")

        # 检查文件修改时间
        try:
            file_mtime = os.path.getmtime(item_data_path)
        except OSError:
            logging.error(f"无法获取文件 {item_data_path} 的修改时间")
            file_mtime = 0

        # 使用线程锁确保缓存安全
        with _cache_lock:
            # 如果缓存存在且文件未修改且不强制刷新，直接返回缓存
            if not force_refresh and _item_data_cache is not None and _cache_timestamp >= file_mtime:
                return _item_data_cache

            # 重新加载数据
            logging.info("正在加载物品数据...")
            start_time = datetime.datetime.now()

            result = []
            try:
                with open(item_data_path, encoding="utf-8") as f:
                    reader = csv.reader(f)
                    next(reader)  # 跳过第一行
                    header = next(reader)
                    next(reader)  # 跳过第三行（类型定义行）

                    # 预先获取索引，避免重复查找
                    category_index = header.index("ItemSearchCategory")
                    name_index = header.index("Name")
                    untradable_index = header.index("IsUntradable")

                    # 使用列表推导式优化性能
                    for row in reader:
                        if (
                            len(row) > max(category_index, name_index, untradable_index)
                            and row[untradable_index] == "False"
                            and row[name_index].strip()  # 使用strip()确保不是空白字符
                            and row[category_index].strip()
                        ):
                            result.append(
                                {
                                    "name": row[name_index],
                                    "id": row[0],
                                    "category": row[category_index],
                                }
                            )

                # 更新缓存
                _item_data_cache = result
                _cache_timestamp = file_mtime

                end_time = datetime.datetime.now()
                logging.info(
                    f"物品数据加载完成，共 {len(result)} 个物品，耗时 {(end_time - start_time).total_seconds():.2f} 秒"
                )

            except Exception as e:
                logging.error(f"加载物品数据失败：{e}")
                if _item_data_cache is not None:
                    logging.info("使用缓存的物品数据")
                    return _item_data_cache
                return []

            return result

    @staticmethod
    def clear_item_data_cache():
        """清空物品数据缓存"""
        global _item_data_cache, _cache_timestamp
        with _cache_lock:
            _item_data_cache = None
            _cache_timestamp = 0
            logging.info("物品数据缓存已清空")

    @staticmethod
    @lru_cache(maxsize=1)
    def get_item_id_name_map():
        """获取物品ID到名称的映射，使用LRU缓存"""
        filtered_data = MarketAnalysis.get_filtered_item_data()
        return {item["id"]: item for item in filtered_data}

    @staticmethod
    def clear_all_caches():
        """清空所有缓存"""
        MarketAnalysis.clear_item_data_cache()
        MarketAnalysis.get_item_id_name_map.cache_clear()
        logging.info("所有缓存已清空")

    @staticmethod
    async def preload_data():
        """预加载数据，可在应用启动时调用"""
        logging.info("开始预加载物品数据...")
        start_time = datetime.datetime.now()

        # 在后台线程中加载数据，避免阻塞异步循环
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, MarketAnalysis.get_filtered_item_data)

        end_time = datetime.datetime.now()
        logging.info(f"物品数据预加载完成，耗时 {(end_time - start_time).total_seconds():.2f} 秒")

    @staticmethod
    async def run(price_threshold=50000, low_price_limit=10000):
        async with MarketAnalysis.lock:
            logging.info("开始获取数据")
            id_name_map = MarketAnalysis.get_item_id_name_map()
            item_ids = list(id_name_map.keys())

            fetcher = DataFetcher()
            # major_data = await fetcher.fetch_data(item_ids)
            major_data = await fetcher.get_cached_data(item_ids)

            logging.info("数据获取完成，开始分析")
            analyzer = DataAnalyzer(SUB_REGIONS, price_threshold, low_price_limit)
            results = analyzer.analyze_all(major_data, id_name_map)

            # 对结果进行排序
            results.sort(key=lambda x: (x["sub_region"], -x["profit"] + x["local"] * 0.3))

            logging.info("分析完成")
            return results

    @staticmethod
    async def run_stream(price_threshold=50000, low_price_limit=10000, max_results=None):
        """使用流式处理的优化版本"""
        async with MarketAnalysis.lock:
            logging.info("开始流式市场分析")
            id_name_map = MarketAnalysis.get_item_id_name_map()
            item_ids = list(id_name_map.keys())

            fetcher = DataFetcher()
            analyzer = DataAnalyzer(SUB_REGIONS, price_threshold, low_price_limit)

            # 收集流式结果
            results = []
            async for result in analyzer.analyze_all_stream(item_ids, id_name_map, fetcher):
                results.append(result)
                # 如果设置了最大结果数，达到后停止
                if max_results and len(results) >= max_results:
                    break

            # 对结果进行排序
            results.sort(key=lambda x: (x["sub_region"], -x["profit"] + x["local"] * 0.3))

            logging.info(f"流式分析完成，共找到 {len(results)} 个可倒卖物品")
            return results

    @staticmethod
    async def run_stream_batch(price_threshold=50000, low_price_limit=10000, batch_size=50):
        """流式处理并分批返回结果"""
        async with MarketAnalysis.lock:
            logging.info("开始流式批量市场分析")
            id_name_map = MarketAnalysis.get_item_id_name_map()
            item_ids = list(id_name_map.keys())

            fetcher = DataFetcher()
            analyzer = DataAnalyzer(SUB_REGIONS, price_threshold, low_price_limit)

            # 使用流处理器收集批次结果
            stream = analyzer.analyze_all_stream(item_ids, id_name_map, fetcher)
            batch_stream = fetcher.stream_processor.collect_stream_results(stream)

            all_batches = []
            async for batch in batch_stream:
                # 对批次结果进行排序
                batch.sort(key=lambda x: (x["sub_region"], -x["profit"] + x["local"] * 0.3))
                all_batches.append(batch)
                logging.info(f"完成一个批次的分析，包含 {len(batch)} 个可倒卖物品")

            logging.info(f"流式批量分析完成，共 {len(all_batches)} 个批次")
            return all_batches

    @staticmethod
    async def render_results(results):
        if not results:
            logging.info("无可倒卖的物品")
            return None
        try:
            img = await template_to_pic(
                template_path=templates_path,
                template_name="recommendation.html",
                templates={"items": results, "datetime": datetime},
                pages={"viewport": {"width": 960, "height": 120}},
                type="png",
            )
            return img
        except Exception as e:
            logging.error(f"渲染结果失败：{e}")
            return None

    @staticmethod
    async def batch_render_results(results):
        if not results:
            logging.info("无可倒卖的物品")
            return None
        try:
            batch_size = 20
            total = len(results)
            img_list = []
            for i in range(0, total, batch_size):
                batch = results[i : i + batch_size]
                img = await MarketAnalysis.render_results(batch)
                img_list.append(img)
            return img_list
        except Exception as e:
            logging.error(f"批量渲染结果失败：{e}")
            return None

    @staticmethod
    async def update_data():
        # 从universalis.app获取数据，并更新到redis
        fetcher = DataFetcher()
        await fetcher.redis.flushall()
        id_name_map = MarketAnalysis.get_item_id_name_map()
        item_ids = list(id_name_map.keys())
        await fetcher.fetch_data(item_ids)

    @staticmethod
    async def maintain_cache():
        """维护缓存数据库，清理过期数据以提升性能"""
        fetcher = DataFetcher()
        deleted_count = await fetcher.clean_expired_cache()
        logging.info(f"缓存维护完成，清理了 {deleted_count} 条过期数据")
        return deleted_count

    @staticmethod
    async def analyze_recipe(item_id: str, target_region: str | None = None, level: int = 0):
        """分析指定物品的配方收益

        Args:
            item_id: 物品ID
            target_region: 目标小区，默认使用第一个小区
        """
        if target_region is None:
            target_region = SUB_REGIONS[0]

        recipe_analyzer = RecipeAnalyzer(os.path.join(this_dir, "Recipe.csv"), os.path.join(this_dir, "Item.csv"))

        # 获取配方树
        recipe_tree = recipe_analyzer.get_recipe_tree(item_id)
        recipe_result = recipe_analyzer.analyze_recipe_tree(recipe_tree)

        # 获取市场数据
        fetcher = DataFetcher()
        market_data = await fetcher.get_cached_data(list(recipe_result.all_items))

        # 计算利润
        analyzer = DataAnalyzer(SUB_REGIONS)
        result = analyzer.calculate_recipe_profit(
            recipe_result, market_data, target_region, recipe_analyzer.items, level
        )

        if not result["success"]:
            return {"success": False, "error": result["error"]}

        return {
            "success": True,
            "recipe_tree": recipe_tree,
            "analysis": result["result"],
        }

    @staticmethod
    async def analyze_recipe_by_name(item_name: str, target_region: str | None = None, level: int = 0):
        """通过物品名称分析配方收益"""
        item_id = MarketAnalysis.get_item_id_by_name(item_name)
        if not item_id:
            return None
        return await MarketAnalysis.analyze_recipe(item_id, target_region, level)

    @staticmethod
    def get_item_id_by_name(item_name: str):
        """通过物品名称获取物品ID"""
        id_name_map = MarketAnalysis.get_item_id_name_map()
        return next(
            (item_id for item_id, item in id_name_map.items() if item["name"] == item_name),
            None,
        )

    @staticmethod
    async def analyze_recipe_and_render(item_name: str, target_region: str | None = None):
        result1 = await MarketAnalysis.analyze_recipe_by_name(item_name, target_region, 0)
        result2 = await MarketAnalysis.analyze_recipe_by_name(item_name, target_region, 1)
        # 我们需要0级：基础材料，1级：直接材料，分别考虑两种情况的利润
        if result2 is None:
            result2 = result1.copy() if result1 else None
        if result1 is None:
            result1 = result2.copy() if result2 else None
        if result1 is None or result2 is None:
            return None
        result = {
            "item_name": item_name,
            "amount": result2["analysis"]["amount"],
            "unit_sell_price": result2["analysis"]["unit_sell_price"],
            "unit_sell_price_taxed": result2["analysis"]["unit_sell_price_taxed"],
            "target_region": target_region,
            "direct_materials": result2["analysis"],
            "base_materials": result1["analysis"],
        }
        return await template_to_pic(
            template_path=templates_path,
            template_name="recipe_analyze.html",
            templates=result,
            type="png",
            pages={"viewport": {"width": 960, "height": 120}},
        )

    @staticmethod
    async def filter_profitable_recipes(target_region: str, profit_threshold: int = 50000):
        """筛选出值得制作的物品

        Args:
            target_region: 目标小区
            profit_threshold: 最低利润阈值

        Returns:
            list: 值得制作的物品列表
        """
        recipe_analyzer = RecipeAnalyzer(os.path.join(this_dir, "Recipe.csv"), os.path.join(this_dir, "Item.csv"))

        # 获取所有配方
        all_recipes = recipe_analyzer.recipes
        profitable_items = []
        all_item_ids = set()
        for item_id in all_recipes.keys():
            all_item_ids.add(item_id)
            recipe_tree = recipe_analyzer.get_recipe_tree(item_id)
            recipe_result = recipe_analyzer.analyze_recipe_tree(recipe_tree)
            all_item_ids.update(recipe_result.all_items)
        fetcher = DataFetcher()
        market_data = await fetcher.get_cached_data(list(all_item_ids))
        for item_id in all_recipes.keys():
            # 获取配方树
            recipe_tree = recipe_analyzer.get_recipe_tree(item_id)
            recipe_result = recipe_analyzer.analyze_recipe_tree(recipe_tree)

            # 计算利润
            analyzer = DataAnalyzer(SUB_REGIONS)
            result = analyzer.calculate_recipe_profit(
                recipe_result,
                market_data,
                target_region,
                recipe_analyzer.items,
                level=0,
                with_filter=True,
            )

            if result["success"]:
                profit = result["result"]["profit"]
                if profit >= profit_threshold:
                    profitable_items.append(
                        {
                            "item_id": item_id,
                            "item_name": recipe_analyzer.items.get(item_id, {}).get("name", item_id),
                            "profit": profit,  # 预期利润
                            "total_cost": result["result"]["total_cost"],  # 总成本
                            "unit_sell_price": result["result"]["unit_sell_price"],  # 预期售价
                            "unit_sell_price_taxed": result["result"]["unit_sell_price_taxed"],
                            "details": result["result"],
                        }
                    )
                    logging.info(
                        f"找到值得制作的物品：{item_id} {recipe_analyzer.items.get(item_id, {}).get('name', item_id)} 利润：{profit}"
                    )

        return {"items": profitable_items, "region": target_region}

    @staticmethod
    async def filter_and_render_profitable_recipes(target_region: str, profit_threshold: int = 100000):
        result = await MarketAnalysis.filter_profitable_recipes(target_region, profit_threshold)
        result["items"].sort(key=lambda x: -x["profit"])
        return await template_to_pic(
            template_path=templates_path,
            template_name="profitable_recipes.html",
            templates=result,
            type="png",
            pages={"viewport": {"width": 960, "height": 120}},
        )

    @staticmethod
    async def filter_and_render_profitable_recipes_batch(target_region: str, profit_threshold: int = 50000):
        result = await MarketAnalysis.filter_profitable_recipes(target_region, profit_threshold)
        result["items"].sort(key=lambda x: -x["profit"])
        batch_size = 20
        total = len(result["items"])
        img_list = []
        for i in range(0, total, batch_size):
            batch = result["items"][i : i + batch_size]
            img = await template_to_pic(
                template_path=templates_path,
                template_name="profitable_recipes.html",
                templates={"items": batch, "region": target_region},
                type="png",
                pages={"viewport": {"width": 960, "height": 120}},
            )
            img_list.append(img)
        return img_list

    @staticmethod
    async def analyze_retainer(retainer_name: str):
        fetcher = DataFetcher()
        logging.info(f"开始查找 {retainer_name} 售卖的物品")
        # 如果retainer_name是用，或,分隔的多个名字，使用正则查找
        retainer_name = retainer_name.replace("，", ",")
        if "," in retainer_name:
            retainer_names_list = retainer_name.split(",")
            pattern_str = "|".join(retainer_names_list)
            logging.info(f"查找多雇员：{pattern_str}")
        else:
            pattern_str = retainer_name
            logging.info(f"只查找：{pattern_str}")
        # 取出所有物品数据，需要先获取所有物品ID
        id_name_map = MarketAnalysis.get_item_id_name_map()
        item_ids = list(id_name_map.keys())
        major_data = await fetcher.get_cached_data(item_ids)
        analyzer = DataAnalyzer(SUB_REGIONS)
        results = analyzer.find_items_by_seller_regex(major_data, pattern_str, id_name_map)
        logging.info(f"找到 {retainer_name} 售卖的物品：{len(results)}: {results}")

        def to_str_v2():
            # 按照retainer_name分组
            retainer_items = defaultdict(list)
            for item in results:
                retainer_items[item["retainer"]].append(item)
            emoji_list = ["🔥", "💎", "🎯", "🚀", "🌈", "⭐", "🍀", "🎉", "🥇", "🏆"]
            return "\n".join(
                [
                    f"{emoji_list[i % len(emoji_list)]} {retainer_name} 售卖的物品：\n"
                    + "\n".join(
                        [
                            f"  ⭐ {item['name']} x{item['quantity']} - 💰 {item['price']} 金币 - 🌍 {item['world']}"
                            for j, item in enumerate(items)
                        ]
                    )
                    for i, (retainer_name, items) in enumerate(retainer_items.items())
                ]
            )

        return to_str_v2() if len(results) > 0 else f"未找到 {retainer_name} 售卖的物品"


class RecipeAnalyzer:
    def __init__(self, recipe_file: str, item_file: str):
        self.recipes = self._load_recipes(recipe_file)
        self.items = self._load_items(item_file)

    def _load_recipes(self, file_path: str) -> dict:
        """加载配方数据"""
        value_columns = (
            ["Item{Result}", "Amount{Result}"]
            + [f"Item{{Ingredient}}[{i}]" for i in range(8)]
            + [f"Amount{{Ingredient}}[{i}]" for i in range(8)]
        )
        raw_recipes = self._load_csv(file_path, "Item{Result}", value_columns)
        recipes = {}
        for result_item, values in raw_recipes.items():
            result_amount = int(values["Amount{Result}"])
            ingredients = []
            for i in range(8):
                ingredient_item = values[f"Item{{Ingredient}}[{i}]"]
                if int(ingredient_item) in [0, -1]:
                    continue
                if ingredient_item:
                    ingredient_amount = int(values[f"Amount{{Ingredient}}[{i}]"])
                    ingredients.append((ingredient_item, ingredient_amount))
            recipes[result_item] = {"amount": result_amount, "ingredients": ingredients}
        return recipes

    def _load_items(self, file_path: str) -> dict:
        """加载物品数据，优先使用缓存"""
        # 尝试使用已有的缓存数据
        try:
            filtered_data = MarketAnalysis.get_filtered_item_data()
            return {item["id"]: {"name": item["name"]} for item in filtered_data}
        except Exception as e:
            logging.warning(f"使用缓存数据失败，回退到直接加载：{e}")
            # 回退到原始方法
            value_columns = ["Name", "ItemSearchCategory", "IsUntradable"]
            filters = {"IsUntradable": "False"}
            raw_items = self._load_csv(file_path, "#", value_columns, filters)
            return {item_id: {"name": values["Name"]} for item_id, values in raw_items.items()}

    def _load_csv(
        self,
        file_path: str,
        key_column: str,
        value_columns: list[str],
        filters: dict | None = None,
    ) -> dict:
        """通用CSV加载函数"""
        data = {}
        with open(file_path, encoding="utf-8") as file:
            reader = csv.reader(file)
            next(reader)  # 跳过第一行
            header = next(reader)
            next(reader)  # 跳过第二行
            key_index = header.index(key_column)
            value_indices = {col: header.index(col) for col in value_columns}
            for row in reader:
                if filters and not all(row[header.index(f)] == v for f, v in filters.items()):
                    continue
                key = row[key_index]
                values = {col: row[idx] for col, idx in value_indices.items()}
                data[key] = values
        return data

    def get_recipe_tree(self, item_id: str, multiplier: int = 1) -> dict:
        """获取配方树"""
        if item_id not in self.recipes:
            return {"item_id": item_id, "amount": multiplier, "ingredients": []}

        recipe = self.recipes[item_id]
        result = {"item_id": item_id, "amount": multiplier, "ingredients": []}

        for ingredient, amount in recipe["ingredients"]:
            sub_tree = self.get_recipe_tree(ingredient, multiplier * amount)
            result["ingredients"].append(sub_tree)

        return result

    def analyze_recipe_tree(self, recipe_tree: dict) -> RecipeAnalysisResult:
        """分析配方树"""
        leaf_nodes = {}
        levels = defaultdict(list)
        all_items = set()

        def traverse(node: dict, level: int = 0):
            item_id = node["item_id"]
            amount = node["amount"]
            all_items.add(item_id)

            if not node["ingredients"]:
                leaf_nodes[item_id] = leaf_nodes.get(item_id, 0) + amount

            levels[level].append(node)
            for ingredient in node["ingredients"]:
                traverse(ingredient, level + 1)

        traverse(recipe_tree)
        return RecipeAnalysisResult(
            total_cost=0,  # 将在计算利润时更新
            total_revenue=0,  # 将在计算利润时更新
            profit=0,  # 将在计算利润时更新
            materials=leaf_nodes,
            levels=dict(levels),
            all_items=all_items,
        )


if __name__ == "__main__":
    # 传统方式（保持兼容）
    # asyncio.run(MarketAnalysis.run())

    # 新的流式处理方式（推荐）
    async def main():
        # 方式1: 流式处理，获取所有结果
        results = await MarketAnalysis.run_stream(price_threshold=50000, low_price_limit=10000)
        print(f"找到 {len(results)} 个可倒卖物品")

        # 方式2: 流式处理，限制最大结果数（适合快速预览）
        limited_results = await MarketAnalysis.run_stream(price_threshold=50000, low_price_limit=10000, max_results=100)
        print(f"限制结果：找到 {len(limited_results)} 个可倒卖物品")

        # 方式3: 流式批量处理（适合大数据集）
        batch_results = await MarketAnalysis.run_stream_batch(price_threshold=50000, low_price_limit=10000)
        total_items = sum(len(batch) for batch in batch_results)
        print(f"批量处理：共 {len(batch_results)} 个批次，{total_items} 个可倒卖物品")

    asyncio.run(main())
