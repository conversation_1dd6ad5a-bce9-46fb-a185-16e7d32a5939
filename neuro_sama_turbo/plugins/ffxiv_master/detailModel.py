from __future__ import annotations

from typing import Any

from pydantic import BaseModel


class ItemItem(BaseModel):
    category: int
    label: str
    name: str
    color: str | None
    colorCode: str | None
    itemId: str
    colorId: str | None


class Item(BaseModel):
    itemId: str
    nameCN: str
    nameJA: str
    nameEN: str
    nameFR: str
    nameDE: str
    iconId: str
    colorCode: Any
    iconUrl: str


class ColorItem(BaseModel):
    itemId: str
    nameCN: str
    nameJA: str
    nameEN: str
    nameFR: str
    nameDE: str
    iconId: str
    colorCode: str
    iconUrl: str


class Color(BaseModel):
    itemId: str
    nameCN: str
    nameJA: str
    nameEN: str
    nameFR: str
    nameDE: str
    iconId: str
    colorCode: str
    iconUrl: str


class ItemNewItem(BaseModel):
    category: int
    label: str
    item: Item
    color: ColorItem | None
    colors: list[Color]


class Data(BaseModel):
    glamourId: int | None
    title: str | None
    description: str | None
    glamourUrls: list[str] | None
    jobName: str | None
    raceName: str | None
    sexName: str | None
    sexId: int | None
    jobId: int | None
    raceId: int | None
    collectionNum: int | None
    greatNum: int | None
    shareNum: int | None
    linkNum: int | None
    signature: str | None
    avatar: str | None
    name: str | None
    uid: str | None
    isVisible: int | None
    isShow: int | None
    collectionStatus: int | None
    greatStatus: int | None
    isTop: int | None
    eventId: int | None
    eventInfo: Any | None
    createdTime: int | None
    hasDownloadPermission: int | None
    isQuickAudit: bool | None
    forkStatus: int | None
    backContent: Any | None
    item: list[ItemItem] | None
    itemNew: list[ItemNewItem] | None
    quickAudit: bool | None


class Model(BaseModel):
    status: int
    message: str
    data: Data
    timestamp: int


class ViewModel(BaseModel):
    glamourId: int
    title: str
    description: str
    glamourUrls: list[str]
    jobName: str
    raceName: str
    sexName: str
    item: list[ItemItem]
    itemNew: list[ItemNewItem]
    quickAudit: bool
