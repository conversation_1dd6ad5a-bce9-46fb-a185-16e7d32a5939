from nonebot import require

require("nonebot_plugin_alconna")
require("nonebot_plugin_session")
require("nonebot_plugin_uninfo")
require("nonebot_plugin_apscheduler")

from nonebot.plugin import PluginMetadata, inherit_supported_adapters
from nonebot_plugin_alconna import (
    Alconna,
    Args,
    Match,
    on_alconna,
)
from nonebot_plugin_apscheduler import scheduler
import nonebot_plugin_saa as saa
from nonebot_plugin_saa import Image as saaImage
from nonebot_plugin_saa import MessageFactory, Text

from .config import Config

__plugin_meta__ = PluginMetadata(
    name="FFXIV Master",
    description="FFXIV相关功能",
    usage="_help_str",
    type="application",  # library
    homepage="https://github.com/X-Zero-L/neuro_sama_turbo",
    config=Config,
    supported_adapters=inherit_supported_adapters(
        "nonebot_plugin_alconna", "nonebot_plugin_uninfo", "nonebot_plugin_session"
    ),
    # supported_adapters={"~onebot.v11"},
    extra={"author": "X-Zero-L <<EMAIL>>"},
)

market_hunter_command = on_alconna(
    Alconna("market_hunter"),
    aliases={"ff倒卖分析"},
)


@market_hunter_command.handle()
async def market_hunter_handle():
    await market_hunter_command.send("正在获取数据，请稍等...")
    from .market_hunter import MarketAnalysis

    # r = await MarketAnalysis.run()
    r = await MarketAnalysis.run_stream()
    rs = await MarketAnalysis.batch_render_results(r)
    if not rs:
        await market_hunter_command.finish("未找到可倒卖的物品。")
    else:
        images = []
        for img_data in rs:
            if img_data:
                images.append(saaImage(img_data))
        if images:
            # await market_hunter_command.finish(UniMessage(images + [AlconnaText("温馨提示：投资有风险，倒卖需谨慎，购买前请自行确认市场情况。")]))
            await saa.AggregatedMessageFactory(
                MessageFactory([*images, Text("温馨提示：投资有风险，倒卖需谨慎，购买前请自行确认市场情况。")])
            ).send()
        else:
            await market_hunter_command.finish("图片生成失败。")


@scheduler.scheduled_job("interval", hours=1)
async def update_market_data():
    from .market_hunter import MarketAnalysis

    await MarketAnalysis.update_data()


market_recipe_command = on_alconna(
    Alconna("market_recipe", Args["item_name", str], Args["target_region?", str]),
    aliases={"ff配方计算", "配方计算", "ff配方收益", "配方收益"},
)


@market_recipe_command.handle()
async def market_recipe_handle(item_name: Match[str], target_region: Match[str]):
    from .market_hunter import MarketAnalysis

    if target_region.available:
        target_regionv = target_region.result
    else:
        target_regionv = "沃仙曦染"
    result = await MarketAnalysis.analyze_recipe_and_render(item_name.result, target_regionv)
    # await market_recipe_command.finish(Image(result))
    await saa.Image(result).send() if result else None


market_recipe_analyze_command = on_alconna(
    Alconna("market_recipe_analyze", Args["target_region?", str]),
    aliases={"ff生产分析", "生产分析"},
)


@market_recipe_analyze_command.handle()
async def market_recipe_analyze_handle(target_region: Match[str]):
    await market_recipe_analyze_command.send("正在获取数据，请稍等...")
    from .market_hunter import MarketAnalysis

    if target_region.available:
        target_regionv = target_region.result
    else:
        target_regionv = "沃仙曦染"
    result = await MarketAnalysis.filter_and_render_profitable_recipes(target_regionv)
    if not result:
        await market_recipe_analyze_command.finish("未找到可生产的物品。")
    else:
        # await market_recipe_analyze_command.finish(Image(result))
        await saa.Image(result).send() if result else None


update_market_data_command = on_alconna(
    Alconna("update_market_data"),
    aliases={"更新市场数据", "ff更新市场数据"},
)


@update_market_data_command.handle()
async def update_market_data_handle(
    event,
):
    if event.user_id != 1025799490:  # type: ignore
        return
    await update_market_data_command.send("正在更新数据，请稍等...")
    from .market_hunter import MarketAnalysis

    await MarketAnalysis.update_data()
    await update_market_data_command.finish("更新完成。")


ff_kai_guyuan_command = on_alconna(
    Alconna("ff_kai_guyuan", Args["retainer_name", str]),
    aliases={"ff开雇员"},
)


@ff_kai_guyuan_command.handle()
async def ff_kai_guyuan_handle(retainer_name: Match[str]):
    from .market_hunter import MarketAnalysis

    result = await MarketAnalysis.analyze_retainer(retainer_name.result)
    await ff_kai_guyuan_command.finish(result)
