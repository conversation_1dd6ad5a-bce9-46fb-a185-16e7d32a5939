from pydantic import BaseModel, Field


class SetuRequest(BaseModel):
    """色图请求模型"""

    num: int = Field(1, description="图片数量", ge=1, le=10)
    tag: str = Field("", description="图片标签")
    r18: bool = Field(False, description="是否包含R18内容")


class SetuResponse(BaseModel):
    """色图响应模型"""

    success: bool = Field(True, description="是否成功")
    message: str = Field("", description="响应消息")
    images: list[bytes] = Field(default_factory=list, description="图片数据列表")
    urls: list[str] = Field(default_factory=list, description="图片URL列表")


class APIInfo(BaseModel):
    """API信息模型"""

    name: str = Field("", description="API名称")
    available: bool = Field(True, description="是否可用")
    error_message: str = Field("", description="错误信息")
