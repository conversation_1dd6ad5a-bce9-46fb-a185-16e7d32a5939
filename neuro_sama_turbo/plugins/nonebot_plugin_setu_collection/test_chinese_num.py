#!/usr/bin/env python3
"""
测试汉字数字转换功能
"""

# 汉字数字转换映射
CHINESE_NUM_MAP = {
    "一": "1", "二": "2", "三": "3", "四": "4", "五": "5",
    "六": "6", "七": "7", "八": "8", "九": "9", "十": "10",
    "两": "2", "俩": "2", "仨": "3", "零": "0",
    "壹": "1", "贰": "2", "叁": "3", "肆": "4", "伍": "5",
    "陆": "6", "柒": "7", "捌": "8", "玖": "9", "拾": "10"
}


def convert_chinese_num(text: str | None) -> str:
    """转换汉字数字为阿拉伯数字"""
    if not text:
        return "1"
    
    # 处理特殊情况
    if text == "点":
        import random
        return str(random.randint(1, 5))
    
    # 处理汉字数字
    if text in CHINESE_NUM_MAP:
        return CHINESE_NUM_MAP[text]
    
    # 处理复合数字，如"十五"、"二十"等
    if "十" in text:
        if text == "十":
            return "10"
        elif text.startswith("十"):  # 十五 -> 15
            suffix = text[1:]
            if suffix in CHINESE_NUM_MAP:
                return str(10 + int(CHINESE_NUM_MAP[suffix]))
        elif text.endswith("十"):  # 二十 -> 20
            prefix = text[:-1]
            if prefix in CHINESE_NUM_MAP:
                return str(int(CHINESE_NUM_MAP[prefix]) * 10)
        else:  # 二十五 -> 25
            parts = text.split("十")
            if len(parts) == 2 and parts[0] in CHINESE_NUM_MAP:
                tens = int(CHINESE_NUM_MAP[parts[0]]) * 10
                ones = int(CHINESE_NUM_MAP.get(parts[1], "0"))
                return str(tens + ones)
    
    # 如果是纯数字，直接返回
    if text.isdigit():
        return text
    
    # 默认返回1
    return "1"


def test_chinese_num_conversion():
    """测试汉字数字转换"""
    test_cases = [
        # 基础数字
        ("一", "1"),
        ("二", "2"),
        ("三", "3"),
        ("五", "5"),
        ("九", "9"),
        ("十", "10"),
        
        # 特殊表达
        ("两", "2"),
        ("俩", "2"),
        ("仨", "3"),
        ("点", "1-5"),  # 随机数，只检查是否为数字
        
        # 复合数字
        ("十一", "11"),
        ("十五", "15"),
        ("二十", "20"),
        ("三十", "30"),
        ("二十五", "25"),
        ("三十八", "38"),
        
        # 繁体数字
        ("壹", "1"),
        ("贰", "2"),
        ("叁", "3"),
        ("伍", "5"),
        ("拾", "10"),
        
        # 阿拉伯数字
        ("1", "1"),
        ("5", "5"),
        ("10", "10"),
        
        # 边界情况
        ("", "1"),
        (None, "1"),
        ("abc", "1"),
    ]
    
    print("测试汉字数字转换功能...")
    print("=" * 50)
    
    for input_text, expected in test_cases:
        result = convert_chinese_num(input_text)
        
        # 特殊处理"点"的情况
        if input_text == "点":
            is_valid = result.isdigit() and 1 <= int(result) <= 5
            status = "✓" if is_valid else "✗"
            print(f"{status} '{input_text}' -> {result} (期望: {expected})")
        else:
            status = "✓" if result == expected else "✗"
            print(f"{status} '{input_text}' -> {result} (期望: {expected})")


def test_regex_examples():
    """测试正则匹配示例"""
    import re
    
    NUM_PATTERN = r"(?:点|\d+|[一二三四五六七八九十两俩仨壹贰叁肆伍陆柒捌玖拾]+)"
    
    test_patterns = [
        rf"(?:要|我要|给我|来|抽)({NUM_PATTERN})(?:张|个|份|幅)?(?:涩|色|瑟)图",
        rf"(?:要|我要|给我|来|抽)({NUM_PATTERN})(?:张|个|份|幅)?(.+?)的?(?:涩|色|瑟)图",
    ]
    
    test_texts = [
        "来一张色图",
        "我要三张涩图", 
        "给我五个色图",
        "来十张色图",
        "抽二十张色图",
        "要两张猫娘的色图",
        "来五张白毛色图",
        "我要三张萝莉的涩图",
        "给我十张兽耳色图",
    ]
    
    print("\n测试正则匹配...")
    print("=" * 50)
    
    for text in test_texts:
        print(f"测试文本: '{text}'")
        for i, pattern in enumerate(test_patterns):
            match = re.search(pattern, text)
            if match:
                groups = match.groups()
                num_text = groups[0] if groups else ""
                tag_text = groups[1] if len(groups) > 1 else ""
                converted_num = convert_chinese_num(num_text)
                print(f"  模式{i+1}: 数量='{num_text}' -> {converted_num}, 标签='{tag_text}'")
        print()


if __name__ == "__main__":
    test_chinese_num_conversion()
    test_regex_examples()
