from nonebot import logger, require
from nonebot.plugin import PluginMetadata, inherit_supported_adapters

require("nonebot_plugin_alconna")

from arclet.alconna import <PERSON>conna, Args, MultiVar, Option, store_true
from nonebot.adapters import Event
from nonebot.exception import FinishedException
from nonebot_plugin_alconna import Match, MsgId, UniMessage, on_alconna
from nonebot_plugin_alconna.uniseg import Image as AlconnaImage
from nonebot_plugin_alconna.uniseg import Text

from .config import Config, plugin_config
from .core import get_setu_images
from .model import SetuRequest

__plugin_meta__ = PluginMetadata(
    name="来张色图",
    description="从多个api获取色图并根据场景整合的色图插件",
    usage="来N张xx色图",
    type="application",
    config=Config,
    homepage="https://github.com/KarisAya/nonebot_plugin_setu_collection",
    supported_adapters=inherit_supported_adapters("nonebot_plugin_alconna"),
    extra={"author": "<PERSON><PERSON><PERSON>ya <<EMAIL>>"},
)

# 色图命令
setu_cmd = on_alconna(
    Alconna(
        "setu",
        Args["count", int, 1],
        Option("r18", action=store_true, default=False, help_text="是否开启R18模式"),
        Option("tags", Args["tags", MultiVar(str, "*")], help_text="指定标签"),
    ),
    use_cmd_start=False,
    block=True,
)


# 添加 shortcut 正则匹配
def wrapper(slot: int | str, content: str | None) -> str:
    """处理正则匹配的内容"""
    if slot == 0:  # 处理数量
        if not content:
            return "1"
        if content == "点":
            import random

            return str(random.randint(1, 5))
    return content or ""


# 添加自然语言快捷方式
setu_cmd.shortcut(
    r"(?:要|我要|给我|来|抽)(点|\d*)(?:张|个|份|幅)?(?:涩|色|瑟)图",
    command="setu {0}",
    fuzzy=True,
    wrapper=wrapper,
).shortcut(
    r"(?:要|我要|给我|来|抽)(点|\d*)(?:张|个|份|幅)?(.+?)的?(?:涩|色|瑟)图",
    command="setu {0}",
    arguments=["tags", "{1}"],
    fuzzy=True,
    wrapper=wrapper,
).shortcut(
    r"(?:涩|色|瑟)图\s*(.+)",
    command="setu 1",
    arguments=["tags", "{0}"],
    fuzzy=True,
).shortcut(
    r"r18(?:涩|色|瑟)图",
    command="setu 1 --r18",
    fuzzy=True,
)

# 帮助命令
help_cmd = on_alconna(
    Alconna("色图帮助"),
    use_cmd_start=False,
    block=True,
    aliases={"涩图帮助"},
)


@setu_cmd.handle()
async def handle_setu(
    count: int,
    tags: Match[tuple[str, ...]],
    r18: bool,
    event: Event,
    msg_id: MsgId,
):
    """处理色图请求"""
    logger.debug(f"收到色图请求，消息ID: {msg_id}")
    try:
        # 构建请求对象
        tag_list = tags.result if tags.available else ()
        tag_str = " ".join(tag_list) if tag_list else ""

        setu_request = SetuRequest(
            num=min(count, 10),  # 限制最大数量
            tag=tag_str,
            r18=r18,
        )

        # 检查权限和限制
        is_private = hasattr(event, "message_type") and getattr(event, "message_type") == "private"

        if is_private and plugin_config.setu_collection_private_setu_limit:
            setu_request.r18 = False
        elif not is_private and plugin_config.setu_collection_public_setu_limit:
            setu_request.r18 = False

        # 选择API
        api_name = (
            plugin_config.setu_collection_private_setu_api
            if is_private
            else plugin_config.setu_collection_public_setu_api
        )

        await UniMessage(f"正在获取{setu_request.num}张图片，请稍候...").reply(id=msg_id).send()

        # 获取图片
        images = await get_setu_images(setu_request, api_name)

        if images:
            # 构建消息
            message_parts = []
            message_parts.append(Text(f"找到{len(images)}张图片"))
            for image_data in images:
                message_parts.append(AlconnaImage(raw=image_data))

            await setu_cmd.finish(UniMessage(message_parts).reply(id=msg_id))
        else:
            await setu_cmd.finish(UniMessage("没有找到符合条件的图片").reply(id=msg_id))

    except FinishedException:
        pass
    except Exception as e:
        logger.error(f"获取色图失败: {e}")
        await setu_cmd.finish(UniMessage(f"获取图片失败: {e!s}").reply(id=msg_id))


@help_cmd.handle()
async def handle_help():
    """处理帮助请求"""
    help_text = """
色图插件使用帮助:

基本命令:
来张色图 - 获取一张随机图片
来3张色图 - 获取3张随机图片
来张猫娘色图 - 获取一张猫娘图片
色图 白毛 - 获取白毛图片

参数选项:
-数量 [数字] - 设置获取图片数量 (最大10张)
-标签 [标签名] - 设置图片标签
-r18 [true/false] - 是否包含R18内容

例如:
来张色图 -数量 3 -标签 猫娘
色图 -标签 白毛 -数量 2

其他命令:
色图帮助 - 显示此帮助信息

支持的标签:
涩图、随机图片、推荐、白毛、兽耳、猫耳、猫娘、星空、壁纸等
"""

    await help_cmd.finish(UniMessage(help_text))
