from nonebot import get_driver, get_plugin_config
from pydantic import BaseModel, Field


class Config(BaseModel):
    """色图插件配置"""

    setu_collection_save_image: bool = Field(True, description="是否保存从api获取的图片")
    setu_collection_path: str = Field("./data/setu_collection", description="主路径")
    setu_collection_private_setu_limit: bool = Field(False, description="私聊图片限制")
    setu_collection_private_setu_api: str = Field("LoliconAPI", description="私聊使用的图片api")
    setu_collection_public_setu_limit: bool = Field(True, description="群聊图片限制（别关）")
    setu_collection_public_setu_api: str = Field("AnosuAPI", description="群聊使用的图片api")
    setu_collection_httpx_config: dict = Field(default_factory=dict, description="httpx配置")


# 配置加载
plugin_config: Config = get_plugin_config(Config)
global_config = get_driver().config
