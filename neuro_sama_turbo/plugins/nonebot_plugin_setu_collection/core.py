import asyncio
from pathlib import Path

from nonebot import logger

from .api.Anosu import API as AnosuAPI
from .api.Lolicon import API as LoliconAPI
from .api.MirlKoi import API as MirlKoiAPI
from .config import plugin_config
from .model import APIInfo, SetuRequest

# API实例映射
API_MAPPING = {
    "Anosu API": AnosuAPI,
    "Lolicon API": LoliconAPI,
    "MirlKoi API": MirlKoiAPI,
}


def get_api_instance(api_name: str):
    """获取API实例"""
    api_class = API_MAPPING.get(api_name)
    if not api_class:
        raise ValueError(f"未知的API: {api_name}")

    # 使用配置中的httpx配置
    return api_class(**plugin_config.setu_collection_httpx_config)


def save_image(image_data: bytes, filename: str) -> bool:
    """保存图片到本地"""
    if not plugin_config.setu_collection_save_image:
        return True

    try:
        save_path = Path(plugin_config.setu_collection_path)
        save_path.mkdir(parents=True, exist_ok=True)

        file_path = save_path / filename
        file_path.write_bytes(image_data)

        logger.info(f"图片已保存: {file_path}")
        return True
    except Exception as e:
        logger.error(f"保存图片失败: {e}")
        return False


async def get_setu_images(request: SetuRequest, api_name: str) -> list[bytes]:
    """获取色图图片数据"""
    try:
        # 获取API实例
        api = get_api_instance(api_name)

        # 调用API获取图片
        r18_value = 1 if request.r18 else 0
        images = await api.call(request.num, r18_value, request.tag)

        if not images:
            logger.warning(f"API {api_name} 未返回图片")
            return []

        # 保存图片（如果启用）
        if plugin_config.setu_collection_save_image:
            for i, image_data in enumerate(images):
                filename = f"setu_{api_name.replace(' ', '_')}_{request.tag or 'random'}_{i}.jpg"
                save_image(image_data, filename)

        logger.info(f"成功获取 {len(images)} 张图片")
        return images

    except Exception as e:
        logger.error(f"获取图片失败: {e}")
        return []


async def check_api_status(api_name: str) -> APIInfo:
    """检查API状态"""
    try:
        api = get_api_instance(api_name)
        # 尝试获取一张图片来测试API
        test_images = await api.call(1, 0, "")

        if test_images:
            return APIInfo(name=api_name, available=True, error_message="")
        else:
            return APIInfo(name=api_name, available=False, error_message="API未返回数据")

    except Exception as e:
        return APIInfo(name=api_name, available=False, error_message=str(e))


async def get_all_api_status() -> list[APIInfo]:
    """获取所有API状态"""
    tasks = [check_api_status(api_name) for api_name in API_MAPPING.keys()]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # 过滤异常，只返回成功的结果
    api_infos = []
    for result in results:
        if isinstance(result, APIInfo):
            api_infos.append(result)
        elif isinstance(result, Exception):
            # 如果有异常，创建一个错误的APIInfo
            api_infos.append(APIInfo(name="Unknown", available=False, error_message=str(result)))

    return api_infos
