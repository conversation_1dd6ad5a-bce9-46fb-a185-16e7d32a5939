[project]
name = "neuro-sama-turbo"
version = "0.1.0"
description = "neuro-sama-turbo"
readme = "README.md"
requires-python = ">=3.9, <4.0"
dependencies = [
    "aiofiles>=24.1.0",
    "aiohttp>=3.11.12",
    "bs4>=0.0.2",
    "e2b-code-interpreter>=1.2.0",
    "filetype>=1.2.0",
    "google-genai>=1.7.0",
    "google-generativeai>=0.8.4",
    "img2pdf>=0.6.0",
    "jmcomic>=2.5.32",
    "logfire>=3.5.3",
    "nest-asyncio>=1.6.0",
    "nonebot-adapter-onebot==2.4.6",
    "nonebot-plugin-alconna>=0.55.1",
    "nonebot-plugin-apscheduler>=0.5.0",
    "nonebot-plugin-chatrecorder>=0.7.0",
    "nonebot-plugin-htmlrender>=0.6.0",
    "nonebot-plugin-orm[default]>=0.7.6",
    "nonebot-plugin-send-anything-anywhere>=0.7.1",
    "nonebot-plugin-session>=0.3.2",
    "nonebot-plugin-session-orm>=0.2.2",
    "nonebot-plugin-uninfo>=0.7.2",
    "nonebot-plugin-userinfo>=0.2.6",
    "nonebot-plugin-waiter>=0.8.1",
    "nonebot-session-to-uninfo>=0.0.2",
    "nonebot2[fastapi]>=2.4.1",
    "numpy>=2.0.2",
    "onnxruntime>=1.20.1",
    "packaging>=24.2",
    "pandas>=2.2.3",
    "pillow>=11.1.0",
    "pydantic-ai[mcp]>=0.0.46",
    "pymongo>=4.11.1",
    "pymongolite>=0.1.7",
    "pypdf2>=3.0.1",
    "pyyaml>=6.0.2",
    "reportlab>=4.3.1",
    "tavily-python>=0.5.1",
    "tqdm>=4.67.1",
    "tzlocal>=5.3",
]

[tool.nonebot]
adapters = [
    { name = "OneBot V11", module_name = "nonebot.adapters.onebot.v11" },
]
plugins = ["nonebot_plugin_alconna", "nonebot_plugin_saa"]
plugin_dirs = [
    "neuro_sama_turbo/plugins",
    "nonebot-plugin-comfyui",
    "gemini-vision",
    "nonebot-plugin-mcp",
    "nonebot-plugin-message-bridge",
]
builtin_plugins = []

[tool.uv.workspace]
members = ["publish/jmhelper"]

[tool.ruff]
line-length = 120
target-version = "py310"

[tool.ruff.format]
line-ending = "lf"

[tool.ruff.lint]
select = [
    "F",     # Pyflakes
    "W",     # pycodestyle warnings
    "E",     # pycodestyle errors
    "I",     # isort
    "UP",    # pyupgrade
    "ASYNC", # flake8-async
    "C4",    # flake8-comprehensions
    "T10",   # flake8-debugger
    "T20",   # flake8-print
    "PYI",   # flake8-pyi
    "PT",    # flake8-pytest-style
    "Q",     # flake8-quotes
    "TID",   # flake8-tidy-imports
    "RUF",   # Ruff-specific rules
]
ignore = [
    "E402",   # module-import-not-at-top-of-file
    "UP037",  # quoted-annotation
    "RUF001", # ambiguous-unicode-character-string
    "RUF002", # ambiguous-unicode-character-docstring
    "RUF003", # ambiguous-unicode-character-comment
    "W191",   # indentation contains tabs
    "TID252", # relative import
]


[tool.ruff.lint.isort]
force-sort-within-sections = true
known-first-party = ["tests/*"]
extra-standard-library = ["typing_extensions"]

[tool.ruff.lint.flake8-pytest-style]
fixture-parentheses = false
mark-parentheses = false

[tool.ruff.lint.pyupgrade]
keep-runtime-typing = true
