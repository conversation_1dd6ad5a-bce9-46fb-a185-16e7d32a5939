"""init

迁移 ID: d7f57cd387ff
父迁移:
创建时间: 2025-05-08 00:35:25.819377

"""

from __future__ import annotations

from collections.abc import Sequence

from alembic import op
import sqlalchemy as sa

revision: str = "d7f57cd387ff"
down_revision: str | Sequence[str] | None = None
branch_labels: str | Sequence[str] | None = ("nonebot_plugin_permission",)
depends_on: str | Sequence[str] | None = None


def upgrade(name: str = "") -> None:
    if name:
        return
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "nonebot_plugin_permission_dependencymodel",
        sa.Column("name", sa.String(length=512), nullable=False),
        sa.Column("subs", sa.JSON(), nullable=False),
        sa.PrimaryKeyConstraint("name", name=op.f("pk_nonebot_plugin_permission_dependencymodel")),
        info={"bind_key": "nonebot_plugin_permission"},
    )
    op.create_table(
        "nonebot_plugin_permission_ownermodel",
        sa.Column("name", sa.String(length=256), nullable=False),
        sa.Column("priority", sa.Integer(), nullable=True),
        sa.Column("wildcards", sa.JSON(), nullable=False),
        sa.PrimaryKeyConstraint("name", name=op.f("pk_nonebot_plugin_permission_ownermodel")),
        info={"bind_key": "nonebot_plugin_permission"},
    )
    op.create_table(
        "nonebot_plugin_permission_permissionmodel",
        sa.Column("name", sa.String(length=512), nullable=False),
        sa.Column("subs", sa.JSON(), nullable=False),
        sa.PrimaryKeyConstraint("name", name=op.f("pk_nonebot_plugin_permission_permissionmodel")),
        info={"bind_key": "nonebot_plugin_permission"},
    )
    op.create_table(
        "nonebot_plugin_permission_ownerinheritsmodel",
        sa.Column("owner_name", sa.String(length=256), nullable=False),
        sa.Column("inherits_name", sa.String(length=256), nullable=False),
        sa.ForeignKeyConstraint(
            ["inherits_name"],
            ["nonebot_plugin_permission_ownermodel.name"],
            name=op.f(
                "fk_nonebot_plugin_permission_ownerinheritsmodel_inherits_name_nonebot_plugin_permission_ownermodel"
            ),
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["owner_name"],
            ["nonebot_plugin_permission_ownermodel.name"],
            name=op.f(
                "fk_nonebot_plugin_permission_ownerinheritsmodel_owner_name_nonebot_plugin_permission_ownermodel"
            ),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint(
            "owner_name",
            "inherits_name",
            name=op.f("pk_nonebot_plugin_permission_ownerinheritsmodel"),
        ),
        info={"bind_key": "nonebot_plugin_permission"},
    )
    op.create_table(
        "nonebot_plugin_permission_ownerpermissionmodel",
        sa.Column("owner_name", sa.String(length=256), nullable=False),
        sa.Column("permission", sa.String(length=512), nullable=False),
        sa.Column("state", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["owner_name"],
            ["nonebot_plugin_permission_ownermodel.name"],
            name=op.f(
                "fk_nonebot_plugin_permission_ownerpermissionmodel_owner_name_nonebot_plugin_permission_ownermodel"
            ),
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["permission"],
            ["nonebot_plugin_permission_permissionmodel.name"],
            name=op.f(
                "fk_nonebot_plugin_permission_ownerpermissionmodel_permission_nonebot_plugin_permission_permissionmodel"
            ),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint(
            "owner_name",
            "permission",
            name=op.f("pk_nonebot_plugin_permission_ownerpermissionmodel"),
        ),
        info={"bind_key": "nonebot_plugin_permission"},
    )
    # ### end Alembic commands ###


def downgrade(name: str = "") -> None:
    if name:
        return
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("nonebot_plugin_permission_ownerpermissionmodel")
    op.drop_table("nonebot_plugin_permission_ownerinheritsmodel")
    op.drop_table("nonebot_plugin_permission_permissionmodel")
    op.drop_table("nonebot_plugin_permission_ownermodel")
    op.drop_table("nonebot_plugin_permission_dependencymodel")
    # ### end Alembic commands ###
