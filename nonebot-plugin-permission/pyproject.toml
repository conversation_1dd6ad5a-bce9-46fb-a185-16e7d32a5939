[project]
name = "nonebot-plugin-permission"
version = "0.1.0"
description = "Permission Plugin based on Arclet Cithun"
authors = [
    {name = "rf_tar_railt", email = "<EMAIL>"},
]
dependencies = [
    "nonebot2>=2.4.1",
    "nonebot-plugin-orm>=0.7.6",
    "arclet-cithun>=0.1.1",
    "nonebot-plugin-alconna>=0.57.4",
    "nonebot-plugin-user>=0.5.0",
]
requires-python = ">=3.9"
readme = "README.md"
license = {text = "MIT"}

[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"


[tool.pdm]
distribution = true

[tool.pdm.build]
includes = ["src/nonebot_plugin_permission"]

[tool.pdm.dev-dependencies]
dev = [
    "isort==5.13.2",
    "black>=25.1.0",
    "ruff>=0.9.7",
    "nonebot-adapter-onebot>=2.4.6",
    "nonebot2[fastapi,uvicorn]>=2.4.1",
    "nonebot-plugin-orm[aiosqlite]>=0.7.6",
]


[tool.black]
line-length = 120
target-version = ["py39", "py310", "py311", "py312"]
include = '\.pyi?$'
extend-exclude = '''
'''

[tool.isort]
profile = "black"
line_length = 120
length_sort = false
skip_gitignore = true
force_sort_within_sections = true
extra_standard_library = ["typing_extensions"]

[tool.ruff]
line-length = 120
target-version = "py39"

[tool.ruff.lint]
select = ["E", "W", "F", "UP", "C", "T", "PYI", "PT", "Q"]
ignore = ["C901", "T201", "E731", "E402"]

[tool.pyright]
pythonVersion = "3.9"
pythonPlatform = "All"
defineConstant = { PYDANTIC_V2 = true }
typeCheckingMode = "basic"
reportShadowedImports = false
disableBytesTypePromotions = true

[tool.pdm.scripts]
format = { composite = ["isort ./src/","black ./src/","ruff check ./src/"] }

[tool.nonebot]
adapters = [
    { name = "OneBot V11", module_name = "nonebot.adapters.onebot.v11" }
]
plugins = ["nonebot_plugin_permission"]
plugin_dirs = []
builtin_plugins = []
