from nonebot import get_driver, require
from nonebot.plugin import PluginMetadata

require("nonebot_plugin_apscheduler")
import asyncio
import hashlib
import re
import time
from typing import Union

import nonebot
from nonebot.adapters.onebot.v11.event import GroupMessageEvent as V11GroupMessageEvent
from nonebot.adapters.onebot.v12.event import GroupMessageEvent as V12GroupMessageEvent
from nonebot.internal.adapter.bot import Bot
from nonebot_plugin_apscheduler import scheduler
from nonebot_plugin_saa import Mention, MessageFactory, TargetQQGroup, Text

from ..handle_prefix import handle_prefix
from .config import Config

__plugin_meta = PluginMetadata(
    name="reminder",
    description="",
    usage="",
    config=Config,
)
driver = nonebot.get_driver()
global_config = driver.config

reminders = {}
import json
import os

path = os.path.join(os.path.dirname(__file__), "reminders.json")


async def save_reminders():
    global reminders
    with open(path, "w", encoding="utf-8") as f:
        json.dump(reminders, f, ensure_ascii=False, indent=4)


async def load_reminders():
    global reminders
    with open(path, encoding="utf-8") as f:
        reminders = json.load(f)


@driver.on_startup
async def on_startup():
    await load_reminders()
    await register_all_reminders()


async def remind_temp(group_id: int, user_id, message):
    group_id, user_id = int(group_id), str(user_id)
    target = TargetQQGroup(group_id=group_id)
    # 解列表
    if isinstance(message, list):
        message = " ".join(message)
    await MessageFactory([Mention(user_id=user_id), Text(f" {message}")]).send_to(
        target, bot=list(nonebot.get_bots().values())[0]
    )


async def add_reminder_time(group_id, user_id, time, message):
    hash_code = hashlib.md5(f"{group_id}_{user_id}_{time}_{message}_{time.time()}".encode()).hexdigest()
    reminders[hash_code] = {
        "group_id": group_id,
        "user_id": user_id,
        "time": time,
        "message": message,
        "raw_msg": f"提醒我 {time} {message}",
    }
    return hash_code


async def add_reminder_date(group_id, user_id, time, message):
    hash_code = await add_reminder_time(group_id, user_id, time, message)
    # 注册定时任务
    scheduler.add_job(
        remind_temp,
        "date",
        run_date=time,
        args=[group_id, user_id, message],
        id=hash_code,
    )
    await save_reminders()


async def add_reminder_cron(
    group_id,
    user_id,
    message,
    year=None,
    month=None,
    day=None,
    week=None,
    day_of_week=None,
    hour=None,
    minute=None,
    second=None,
    start_date=None,
    end_date=None,
    timezone=None,
    raw_msg=None,
):
    hash_code = hashlib.md5(f"{group_id}_{user_id}_{message}_{time.time()}".encode()).hexdigest()
    reminders[hash_code] = {
        "group_id": group_id,
        "user_id": user_id,
        "message": message,
        "year": year,
        "month": month,
        "day": day,
        "week": week,
        "day_of_week": day_of_week,
        "hour": hour,
        "minute": minute,
        "second": second,
        "start_date": start_date,
        "end_date": end_date,
        "timezone": timezone,
        "raw_msg": raw_msg,
    }
    return hash_code


async def add_reminder_interval(
    group_id,
    user_id,
    message,
    year=None,
    month=None,
    day=None,
    week=None,
    day_of_week=None,
    hour=None,
    minute=None,
    second=None,
    start_date=None,
    end_date=None,
    timezone=None,
    raw_msg=None,
):
    hash_code = await add_reminder_cron(
        group_id,
        user_id,
        message,
        year,
        month,
        day,
        week,
        day_of_week,
        hour,
        minute,
        second,
        start_date,
        end_date,
        timezone,
        raw_msg,
    )
    # 注册定时任务
    scheduler.add_job(
        remind_temp,
        "cron",
        year=year,
        month=month,
        day=day,
        week=week,
        day_of_week=day_of_week,
        hour=hour,
        minute=minute,
        second=second,
        start_date=start_date,
        end_date=end_date,
        timezone=timezone,
        args=[group_id, user_id, message],
        id=hash_code,
    )
    await save_reminders()


async def register_all_reminders():
    global reminders
    await load_reminders()
    for hash_code, reminder in reminders.items():
        if "time" in reminder:
            scheduler.add_job(
                remind_temp,
                "date",
                run_date=reminder["time"],
                args=[reminder["group_id"], reminder["user_id"], reminder["message"]],
                id=hash_code,
            )
        else:
            scheduler.add_job(
                remind_temp,
                "cron",
                args=[reminder["group_id"], reminder["user_id"], reminder["message"]],
                id=hash_code,
                year=reminder["year"],
                month=reminder["month"],
                day=reminder["day"],
                week=reminder["week"],
                day_of_week=reminder["day_of_week"],
                hour=reminder["hour"],
                minute=reminder["minute"],
                second=reminder["second"],
                start_date=reminder["start_date"],
                end_date=reminder["end_date"],
                timezone=reminder["timezone"],
            )


reminder_prefix = "提醒我"
reminder_command = nonebot.on_startswith(reminder_prefix)


@reminder_command.handle()
async def handle_reminder(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    args = handle_prefix(str(event.get_plaintext()), len(reminder_prefix)).strip()
    group_id, user_id = (
        str(event.group_id),
        str(event.user_id),
    )  # event.get_session_id().split("_")
    # 期望的格式：提醒我 每周一 8:00 喝水
    args = args.split(" ")
    day_of_week = None
    hour = None
    minute = None
    second = None
    day = None
    msg = None
    for index, arg in enumerate(args):
        if arg in ["周一", "星期一", "每周一"]:
            day_of_week = 0
        elif arg in ["周二", "星期二", "每周二"]:
            day_of_week = 1
        elif arg in ["周三", "星期三", "每周三"]:
            day_of_week = 2
        elif arg in ["周四", "星期四", "每周四"]:
            day_of_week = 3
        elif arg in ["周五", "星期五", "每周五"]:
            day_of_week = 4
        elif arg in ["周六", "星期六", "每周六"]:
            day_of_week = 5
        elif arg in ["周日", "星期日", "每周日"]:
            day_of_week = 6
        # 每.*秒
        elif result := re.match(r"每(\d+)秒", arg):
            second = int(result.group(1))
            second = f"*/{second}"
        # 每.*分钟
        elif result := re.match(r"每(\d+)分钟", arg):
            minute = int(result.group(1))
            minute = f"*/{minute}"
        # 每.*小时
        elif result := re.match(r"每(\d+)小时", arg):
            hour = int(result.group(1))
            hour = f"*/{hour}"
        # 每.*天
        elif result := re.match(r"每(\d+)天", arg):
            day = int(result.group(1))
            day = f"*/{day}"
        # 每月最后一天
        elif arg in ["每月最后一天", "每月最后一日", "每月最后一号", "月底", "月末"]:
            day = "last"
        # 每个月第一天
        elif arg in ["每月第一天", "每月第一日", "每月第一号", "月初"]:
            day = 1
        # hh:mm
        elif result := re.match(r"(\d+):(\d+)", arg):
            hour = int(result.group(1))
            minute = int(result.group(2))
        # *点
        elif result := re.match(r"(\d+)点", arg):
            hour = int(result.group(1))
        # *分
        elif result := re.match(r"(\d+)分", arg):
            minute = int(result.group(1))
        # *秒
        elif result := re.match(r"(\d+)秒", arg):
            second = int(result.group(1))
        else:
            msg = args[index:]
            break
    # 如果所有参数都没有匹配，直接返回
    if day_of_week is None and hour is None and minute is None and second is None and day is None:
        await MessageFactory(
            [
                Text(
                    "格式错误！每个参数需要空格分隔！合法格式如下：\n提醒我 每周一/每1天 8:00 喝水\n提醒我 月底/月初 每1小时 每1分钟 喝水"
                )
            ]
        ).send(at_sender=True)
        return
    await add_reminder_interval(
        group_id,
        user_id,
        msg,
        day_of_week=day_of_week,
        hour=hour,
        minute=minute,
        second=second,
        day=day,
        raw_msg=event.get_plaintext(),
    )
    await MessageFactory([Text("添加提醒成功！")]).send(at_sender=True)


callme_prefix = "callme"
callme_command = nonebot.on_startswith(callme_prefix)


@callme_command.handle()
async def handle_callme(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    args = handle_prefix(str(event.get_plaintext()), len(callme_prefix)).strip()
    group_id, user_id = str(event.group_id), str(event.user_id)
    """
    期望的命令格式：hour=1,3,4,5...&minute=1,2,3,4...&second=1,2,3,4...&message=xxx
    """
    help_str = """
    命令格式参考：
    callme hour=1,3,*/4,5...&minute=1,2,3,4...&second=1,2,3,4...&message=xxx
    callme day_of_week=1,3,4,5...&start_date=xxx&end_date=xxx&timezone=xxx&message=xxx
    callme month=1,3,4,5...&day=1,2,3,4...&hour=1,2,3,4...&minute=1,2,3,4...&second=1,2,3,4...&start_date=xxx&end_date=xxx&timezone=xxx&message=xxx
    不会对=后面的参数进行校验，所以请自行保证参数的合法性
    message字段必须携带
    """
    if not args:
        await MessageFactory([Text(f"{help_str}")]).send(at_sender=True)
        await callme_command.finish()
    args = args.split("&")
    args_dict = {}
    for arg in args:
        key, value = arg.split("=")
        args_dict[key] = value if value else None
    hour = args_dict.get("hour")
    minute = args_dict.get("minute")
    second = args_dict.get("second")
    message = args_dict.get("message")
    day_of_week = args_dict.get("day_of_week")
    start_date = args_dict.get("start_date")
    end_date = args_dict.get("end_date")
    timezone = args_dict.get("timezone")
    month = args_dict.get("month")
    day = args_dict.get("day")
    if not message:
        await MessageFactory([Text(f"参数错误！必须携带message字段\n{help_str}")]).send(at_sender=True)
        await callme_command.finish()
    # 不可以只有second
    if not hour and not minute and second and not day_of_week and not month and not day:
        await MessageFactory([Text(f"间隔太短了！\n{help_str}")]).send(at_sender=True)
        await callme_command.finish()
    await add_reminder_interval(
        group_id,
        user_id,
        message,
        hour=hour,
        minute=minute,
        second=second,
        day_of_week=day_of_week,
        start_date=start_date,
        end_date=end_date,
        timezone=timezone,
        month=month,
        day=day,
        raw_msg=event.get_plaintext(),
    )
    await MessageFactory([Text("添加提醒成功！")]).send(at_sender=True)


# 查看提醒
list_reminder_prefix = "所有提醒"
list_reminder_command = nonebot.on_startswith(list_reminder_prefix)


# 列出对应用户的所有提醒
@list_reminder_command.handle()
async def handle_list_reminder(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    group_id, user_id = (
        str(event.group_id),
        str(event.user_id),
    )  # event.get_session_id().split("_")
    all_reminders = []
    for hash_code in reminders:
        reminder = reminders[hash_code]
        if reminder["user_id"] == user_id and reminder["group_id"] == group_id:
            all_reminders.append(f"提醒hash：{hash_code[:8]}\n提醒内容：{reminder['raw_msg']}")
    if len(all_reminders) == 0:
        await MessageFactory([Text("没有提醒！")]).send(at_sender=True)
        return
    all_reminders_str = "\n".join(all_reminders)
    await MessageFactory([Text(f"所有提醒：\n{all_reminders_str}")]).send(at_sender=True)


# 删除指定提醒
delete_reminder_prefix = "删除提醒"
delete_reminder_command = nonebot.on_startswith(delete_reminder_prefix)


@delete_reminder_command.handle()
async def handle_delete_reminder(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
):
    args = handle_prefix(str(event.get_plaintext()), len(delete_reminder_prefix)).strip()
    group_id, user_id = (
        str(event.group_id),
        str(event.user_id),
    )  # event.get_session_id().split("_")
    all_startswith_reminders = []
    for hash_code in reminders:
        reminder = reminders[hash_code]
        if reminder["user_id"] == user_id and reminder["group_id"] == group_id and hash_code.startswith(args):
            all_startswith_reminders.append(hash_code)

    if len(all_startswith_reminders) == 0:
        await MessageFactory([Text("没有该提醒！")]).send(at_sender=True)
        await delete_reminder_command.finish()

    if len(all_startswith_reminders) == 1:
        hash_code = all_startswith_reminders[0]
        reminder = reminders[hash_code]
        scheduler.remove_job(hash_code)
        del reminders[hash_code]
        await MessageFactory([Text("删除提醒成功！")]).send(at_sender=True)
        await save_reminders()
        await delete_reminder_command.finish()

    # 出现多个匹配项，需要用户选择
    all_reminders = []
    for hash_code in all_startswith_reminders:
        reminder = reminders[hash_code]
        all_reminders.append(f"提醒hash：{hash_code[:8]}\n提醒内容：{reminder['raw_msg']}")
    all_reminders_str = "\n".join(all_reminders)
    await MessageFactory([Text(f"哈希冲突，请选择：\n{all_reminders_str}")]).send(at_sender=True)
    await delete_reminder_command.finish()
