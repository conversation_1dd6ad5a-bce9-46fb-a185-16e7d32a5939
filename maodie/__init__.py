import asyncio
import random

from nonebot import get_plugin_config, require
from nonebot.exception import FinishedException
from nonebot.log import logger
from nonebot.params import Depends
from nonebot.plugin import PluginMetadata, inherit_supported_adapters

require("nonebot_plugin_alconna")
require("nonebot_plugin_uninfo")
from pathlib import Path

from arclet.alconna import AllParam
from nonebot.adapters import Bot, Event
from nonebot_plugin_alconna import (
    Alconna,
    Args,
    Match,
    MsgId,
    UniMessage,
    on_alconna,
)
from nonebot_plugin_alconna import (
    Image as AlconnaImage,
)
from nonebot_plugin_alconna.builtins.extensions import ReplyRecordExtension
from pydantic import BaseModel

from .config import Config

config = get_plugin_config(Config)
_help_str = """

""".strip()
__plugin_meta__ = PluginMetadata(
    name="maodie",
    description="maodie",
    usage=_help_str,
    type="application",
    homepage="",
    config=Config,
    supported_adapters=inherit_supported_adapters("nonebot_plugin_alconna", "nonebot_plugin_uninfo"),
    extra={"author": "X-Zero-L <<EMAIL>>"},
)


class ImageFolder(BaseModel):
    folder: str
    images: list[Path]


def get_image_list() -> list[ImageFolder]:
    image_folder_path = Path(__file__).parent / "images"
    image_folder_path.mkdir(exist_ok=True, parents=True)
    image_folders = []
    for folder in image_folder_path.iterdir():
        if folder.is_dir():
            images = [image for image in folder.glob("*") if image.is_file()]
            images.sort(key=lambda x: int(x.stem.split("_")[-1]) if "_" in x.stem else x.stem)
            image_folders.append(ImageFolder(folder=folder.name, images=images))
    return image_folders


image_folders = get_image_list()
image_paths = []
for folder in image_folders:
    image_paths.extend(folder.images)


async def load_image_from_paths(
    image_paths: list[str],
) -> list[AlconnaImage]:
    images = []
    for path in image_paths:
        try:
            with open(path, "rb") as f:
                image_data = f.read()
            images.append(AlconnaImage(raw=image_data))
        except Exception as e:
            logger.error(f"Error loading image from {path}: {e}")
            continue
    return images


maodie_command = on_alconna(
    Alconna(
        "略猫区",
        Args["count?", int],
    ),
    use_cmd_start=True,
    priority=5,
    block=True,
    extensions=[ReplyRecordExtension()],
    aliases={"略猫蛆", "耄耋", "猫爹"},
)


@maodie_command.handle()
async def handle_gemini(
    bot: Bot,
    event: Event,
    count: Match[int],
    ext: ReplyRecordExtension,
    msg_id: MsgId,
):
    if count.available:
        count = count.result
        if count > 10:
            count = 10
        files = random.sample(image_paths, count)
        datas = await load_image_from_paths(files)
    else:
        target_folder = random.choice(image_folders)
        files = target_folder.images
        datas = await load_image_from_paths(files)
    if not datas:
        await maodie_command.finish(UniMessage("没有找到图片"))
    msg = UniMessage()
    for data in datas:
        msg += data
    await maodie_command.finish(msg.reply(id=msg_id))
