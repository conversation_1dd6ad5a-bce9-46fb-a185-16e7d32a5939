{"fishing_spot_list": [{"name": "晨露溪湾", "level": 1, "geo_feature": "坐落在山脚下的静谧溪湾，晨光洒下，映出晶莹露珠，给人无限生机。", "aquatic_params": {"water_temp": 16.0, "water_salinity": 0.0, "water_depth": 2.0, "water_flow": 1.0}, "fish_list": [{"name": "水灵晨仙", "rarity": "传说", "habitat": "溪流之源", "story": "传说此精灵乃水之守护者，每当晨露初凝时便会现身，带来新生命的希望。", "bait": "晶露草", "weather": "多云", "time": "黎明", "min_size": "大型", "base_chance": 0.005}, {"name": "翠羽鳞鲤", "rarity": "罕见", "habitat": "溪边浅滩", "story": "身披翠绿色鳞片，似乎吸收了溪水中植被的精华，生长迅速而灵动。", "bait": "柳絮碎片", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.05}, {"name": "银鳞跃鱼", "rarity": "罕见", "habitat": "流动的溪流中", "story": "常在落日余晖下跃出水面，其闪耀的银光令观者目眩神迷。", "bait": "晨光虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.05}, {"name": "碧波影鲤", "rarity": "罕见", "habitat": "河心水域", "story": "仿若河流的影子化身，隐秘而神秘，只有心存敬畏者才能捕捉其踪迹。", "bait": "水灵草", "weather": null, "time": null, "min_size": "大型", "base_chance": 0.05}, {"name": "琉璃河魄", "rarity": "罕见", "habitat": "溪流深处", "story": "鳞片琉璃般闪耀，据说每当水波荡漾便预示着河神的降临。", "bait": "晨露精华", "weather": null, "time": null, "min_size": "大型", "base_chance": 0.05}, {"name": "溪尾小虾", "rarity": "普通", "habitat": "浅滩边", "story": "活泼好动的群居生物，是溪湾中最常见的一抹生机。", "bait": "小麦粒", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.15}, {"name": "石纹鱼", "rarity": "普通", "habitat": "岩石缝隙", "story": "依附于溪边岩石，借助潮汐隐蔽身形，常群体出没。", "bait": "碎石虾", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.15}, {"name": "莲花犬鱼", "rarity": "普通", "habitat": "莲叶覆盖的水面", "story": "以犬的敏捷与莲花的优雅著称，虽常见却也蕴藏着大自然的灵动。", "bait": "莲花瓣", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.15}, {"name": "洄流鳄虾", "rarity": "普通", "habitat": "急流中", "story": "在水流冲击中顽强生存的小生物，摇曳于溪水之间。", "bait": "河岸昆虫", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.15}, {"name": "幽岩鳚", "rarity": "普通", "habitat": "溪底石缝", "story": "鳞片散发淡淡的蓝光，平凡中透露出一丝令人遐想的神秘。", "bait": "苔藓碎片", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.15}], "story": "位于山脚下的晨露溪湾，以清晨微光中的神秘气息闻名，水流柔和，让人感受到大自然最原始的生命律动。"}, {"name": "樱华梦涧", "level": 3, "geo_feature": "位于花海深处，水面上散落着飘零的樱花花瓣，仿佛置身于一幅唯美的二次元画卷。", "aquatic_params": {"water_temp": 18.0, "water_salinity": 0.0, "water_depth": 3.0, "water_flow": 0.5}, "fish_list": [{"name": "桃色水仙", "rarity": "普通", "habitat": "花瓣漂流", "story": "淡粉色的身影在晨光中闪现，与落樱共舞，传递春日的温柔。", "bait": "花瓣诱饵", "weather": null, "time": null, "min_size": "小型", "base_chance": 20.0}, {"name": "月影锦鲤", "rarity": "罕见", "habitat": "月光映照之处", "story": "在月夜下游曳的锦鲤，身上流转着神秘的银光，仿佛能映照未来。", "bait": "月光石", "weather": null, "time": null, "min_size": "中型", "base_chance": 15.0}, {"name": "樱精灵鳞", "rarity": "普通", "habitat": "樱落溪畔", "story": "细小的鳞片中似乎封印了樱花的精魂，传说能带来无尽的春意。", "bait": "微光粉尘", "weather": null, "time": null, "min_size": "小型", "base_chance": 18.0}, {"name": "梦幻蝶尾鱼", "rarity": "罕见", "habitat": "梦幻溪流", "story": "尾鳍如同轻舞的蝴蝶，在微风中留下唯美的弧线，每一次闪现都令人心动。", "bait": "彩翼诱饵", "weather": null, "time": null, "min_size": "中型", "base_chance": 12.0}, {"name": "流光萤鳗", "rarity": "普通", "habitat": "夜色秘境", "story": "身体微微发光，如同夜空中飘散的记忆，带有一种淡淡的忧伤。", "bait": "萤火虫", "weather": null, "time": null, "min_size": "小型", "base_chance": 17.0}, {"name": "樱花仙鱼", "rarity": "传说", "habitat": "樱雨之境", "story": "传说中被樱花妖精赋予祝福的鱼王，仅在花雨纷飞时现身，是钓者梦寐以求的奇迹。", "bait": "仙露", "weather": null, "time": null, "min_size": "大型", "base_chance": 5.0}, {"name": "彩虹梦鲤", "rarity": "罕见", "habitat": "彩虹桥畔", "story": "色彩斑斓的身影仿佛从童话中跃出，每一次跃水都带来一抹奇幻色彩。", "bait": "彩虹糖", "weather": null, "time": null, "min_size": "中型", "base_chance": 14.0}, {"name": "霓裳涟影", "rarity": "普通", "habitat": "流光湖面", "story": "细腻的涟漪随着微风缓缓扩散，仿佛低吟着一曲哀而不伤的古老乐章。", "bait": "羽毛", "weather": null, "time": null, "min_size": "小型", "base_chance": 19.0}, {"name": "幽蓝影鳞", "rarity": "罕见", "habitat": "梦幻水道", "story": "蓝色的鳞光仿佛能引领钓者跨入另一个世界，神秘且诱人。", "bait": "蓝晶", "weather": null, "time": null, "min_size": "中型", "base_chance": 11.0}, {"name": "花舞仙鲫", "rarity": "普通", "habitat": "樱花溪边", "story": "伴随着花瓣飘落，仙鲫轻跃水面，为季节平添了一抹生机与诗意。", "bait": "花瓣碎片", "weather": null, "time": null, "min_size": "小型", "base_chance": 16.0}], "story": "樱华梦涧以二次元的浪漫和梦幻著称，水中似乎流淌着不曾言说的樱花传说，每位到访者都有机会与樱花精灵邂逅。"}, {"name": "幽雾滩涂", "level": 5, "geo_feature": "一片萦绕着浓重雾气的湿地滩涂，迷雾与水汽交织出古老而神秘的氛围。", "aquatic_params": {"water_temp": 20.0, "water_salinity": 10.0, "water_depth": 1.5, "water_flow": 0.5}, "fish_list": [{"name": "雾隐幻魄", "rarity": "传说", "habitat": "迷雾深处", "story": "传说中，这幽灵般的生物只在浓雾最重的时刻出现，是幽冥力量的化身。", "bait": "幽雾花瓣", "weather": "多云", "time": "夜晚", "min_size": "大型", "base_chance": 0.005}, {"name": "暗影鳊鱼", "rarity": "罕见", "habitat": "静谧浅水", "story": "在薄雾中游曳，身影朦胧，仿佛暗影凝结而成，令人难以捕捉。", "bait": "霜砂虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.05}, {"name": "苍碧泥鳅", "rarity": "罕见", "habitat": "泥泞水域", "story": "其矫健的体态令人在雾中寻觅时倍感神秘，彷佛与迷雾共舞。", "bait": "蚯蚓碎块", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.05}, {"name": "蓝梦飞鳐", "rarity": "罕见", "habitat": "浅滩水面", "story": "优雅的身形仿佛能穿越梦境与现实，是迷雾中最闪耀的幻影。", "bait": "浮游星砂", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.05}, {"name": "幻纱鲤", "rarity": "罕见", "habitat": "小沟之间", "story": "半透明的鱼鳞如同幻影般轻柔，令人目不转睛又倍感神秘。", "bait": "幽雾藻", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.05}, {"name": "泥潟小鰕", "rarity": "普通", "habitat": "滩涂边缘", "story": "常在泥泞中出没，是这片湿地中最为普遍的居民。", "bait": "泥沙颗粒", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.15}, {"name": "青纹鰕鱼", "rarity": "普通", "habitat": "浅沼地", "story": "体色青绿，独自在芦苇丛中穿梭，显得悄然无声。", "bait": "水草碎片", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.15}, {"name": "灰影鳗", "rarity": "普通", "habitat": "低洼湿地", "story": "修长的身形在夜色中若隐若现，给人一种幽静而神秘的感受。", "bait": "暗河虾", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.15}, {"name": "翠涟泥鳙", "rarity": "普通", "habitat": "泥潭底部", "story": "身披翠绿色细鳞，虽然平凡却显示出惊人的灵活和坚韧。", "bait": "浮泥虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.15}, {"name": "漠沙小鲶", "rarity": "普通", "habitat": "滩涂边缘", "story": "体型细小，在泥沙中穿行，是这片幽雾滩涂中勤劳的生灵。", "bait": "细沙蚯蚓", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.15}], "story": "在这片迷雾缭绕的滩涂中，每一缕轻雾似乎都在诉说着古老的秘密，而夜幕降临时，更显得神秘而不可捉摸。"}, {"name": "幻晶溪流", "level": 7, "geo_feature": "这是一条隐秘于幽林中的晶石河流，河水在晨曦中闪烁着斑斓光彩，仿佛诉说着遗失的魔法传承。", "aquatic_params": {"water_temp": 16.0, "water_salinity": 0.0, "water_depth": 3.0, "water_flow": 1.5}, "fish_list": [{"name": "晶光鱼", "rarity": "罕见", "habitat": "河流中段明亮地带", "story": "相传晶光鱼体内藏有远古晶石的残影，每当晨露初凝，河面便会闪烁出微弱的符文光芒。", "bait": "闪光矿石碎片", "weather": null, "time": "黎明", "min_size": "中型", "base_chance": 0.18}, {"name": "流影小鱼", "rarity": "普通", "habitat": "静谧水域边缘", "story": "这种小鱼擅长在缓流中穿梭，是许多渔师捕捉大型生物的首选诱饵。", "bait": "细碎麦粒", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.3}, {"name": "水晶裂斑", "rarity": "普通", "habitat": "岩石附近的暗处", "story": "水晶裂斑的身上布满了微小的晶状鳞片，据说能映出夜空中的星辰痕迹。", "bait": "晶砂碎片", "weather": null, "time": "夜晚", "min_size": "小型", "base_chance": 0.28}, {"name": "幽蓝刀尾", "rarity": "罕见", "habitat": "急流激射的水域", "story": "其纤细的尾鳍如同刀锋，曾在传说中刺破封印的魔法，留下不可磨灭的蓝色痕迹。", "bait": "蓝光虫", "weather": "多云", "time": null, "min_size": "中型", "base_chance": 0.16}, {"name": "岚雾鲤", "rarity": "罕见", "habitat": "水流形成的薄雾区域", "story": "岚雾鲤常在薄雾中戏水，传闻其每一次跃出水面都能激起一阵迷蒙的晶雾。", "bait": "细碎林果", "weather": "多云", "time": "黎明", "min_size": "大型", "base_chance": 0.15}, {"name": "潮汐旋翼", "rarity": "普通", "habitat": "河口位置", "story": "这种鱼类体型纤薄，游动时迅疾如同潮汐般汹涌，常被用作流派诱饵。", "bait": "小虾群", "weather": null, "time": "夜晚", "min_size": "中型", "base_chance": 0.27}, {"name": "晨曦织梦", "rarity": "传说", "habitat": "晶石堆积的河心", "story": "传说晨曦织梦乃是古代水之精灵的化身，只有在星月初现之时，才能窥见它那梦幻般的身影。", "bait": "月光花瓣", "weather": "晴天", "time": "黎明", "min_size": "大型", "base_chance": 0.05}, {"name": "幻翠斑鱼", "rarity": "罕见", "habitat": "岸边草丛掩映之地", "story": "鱼体上散布着不规则的翠绿花纹，似乎映射着周围植物的生长节奏，传说其血液能治愈伤痛。", "bait": "嫩叶碎片", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.17}, {"name": "萤影银鲫", "rarity": "普通", "habitat": "静水中的浅滩", "story": "萤影银鲫在夜幕降临时分外活跃，其银光常被误认为是萤火虫群在水中徘徊。", "bait": "微光虫", "weather": "多云", "time": "夜晚", "min_size": "小型", "base_chance": 0.26}, {"name": "潮涌小鲤", "rarity": "普通", "habitat": "微流汇聚之处", "story": "这是一群群聚的小鲤鱼，虽然体型不大，却总在关键时刻掀起意想不到的水花。", "bait": "玉米粒", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.29}], "story": "幻晶溪流隐匿于密林深处，传说中隐藏着古代魔法遗忘的秘密。每一滴水珠都像极了时间的碎片，等待有缘人去解读其背后的传说。"}, {"name": "星辰秘渊", "level": 10, "geo_feature": "隐藏于群山之间的深渊，夜空星辰映射下，仿佛蕴藏着远古文明的秘密与神秘力量。", "aquatic_params": {"water_temp": 12.0, "water_salinity": 5.0, "water_depth": 30.0, "water_flow": 0.8}, "fish_list": [{"name": "幻星封灵", "rarity": "传说", "habitat": "秘渊深处", "story": "传说此鱼乃古文明封印的星辰之灵，只有在星月交辉之时才能现世，散发神秘光芒。", "bait": "星尘碎片", "weather": "晴天", "time": "夜晚", "min_size": "巨型", "base_chance": 0.005}, {"name": "夜辉幽鳞", "rarity": "罕见", "habitat": "秘渊中层", "story": "其鳞片在夜间闪烁着幽蓝光芒，仿佛映射出宇宙的深邃秘密。", "bait": "月光虾", "weather": null, "time": null, "min_size": "大型", "base_chance": 0.05}, {"name": "深渊蓝鲨", "rarity": "罕见", "habitat": "秘渊低处", "story": "体态雄壮，犹如星辰碎片的化身，每次掠过水域都带起浪花轻晃。", "bait": "幽蓝软虫", "weather": null, "time": null, "min_size": "巨型", "base_chance": 0.05}, {"name": "碧海流光", "rarity": "罕见", "habitat": "中层暗域", "story": "散发出幽幽蓝光，据说能预示水域未来命运，其出现令人充满遐思。", "bait": "流光砂", "weather": null, "time": null, "min_size": "大型", "base_chance": 0.05}, {"name": "暮影幽鱼", "rarity": "罕见", "habitat": "边缘岩缝", "story": "在幽暗的水域边缘偶露身影，仿佛捕捉到了暮色的低语。", "bait": "暮色虫", "weather": null, "time": null, "min_size": "大型", "base_chance": 0.05}, {"name": "渊底小鰕", "rarity": "普通", "habitat": "渊底碎石区", "story": "秘渊中最常见的居民之一，虽小却顽强生活在坚硬的石砾间。", "bait": "微石藻", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.15}, {"name": "星纱鳗", "rarity": "普通", "habitat": "近底层", "story": "全身鳞片似带着淡淡的星光，在幽深水域中若隐若现。", "bait": "星辰碎屑", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.15}, {"name": "秘渊螺灵", "rarity": "普通", "habitat": "岩石间", "story": "螺旋般的身形象征着无尽秘密与时光流转，是秘渊中平凡而神秘的存在。", "bait": "幽岩藻", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.15}, {"name": "昏光鲶", "rarity": "普通", "habitat": "暗流环绕区", "story": "在幽暗水层中闪烁微光，它的存在为秘渊平添几分活力与神秘。", "bait": "暗影虾", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.15}, {"name": "微澜鳚", "rarity": "普通", "habitat": "近岸岩缝", "story": "行动低调，却总能在水流中捕捉到微弱的光影，是秘渊中不可小觑的存在。", "bait": "微澜碎米", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.15}], "story": "在这幽深的星辰秘渊中，传说埋藏着远古文明的星辰秘密，只有在星月交辉之夜，才能触碰到那被封印的神秘力量。"}, {"name": "流光锦渊", "level": 12, "geo_feature": "隐匿于竹林幽径中的清流，水面似锦，映照出东方神秘的光影。", "aquatic_params": {"water_temp": 22.0, "water_salinity": 0.1, "water_depth": 3.0, "water_flow": 1.0}, "fish_list": [{"name": "碧影锦鲤", "rarity": "罕见", "habitat": "流光锦渊的浅水区", "story": "传说受水灵庇佑，每当晨曦初照，鳞身便散发着柔和的光辉。", "bait": "青竹叶", "weather": null, "time": "黎明", "min_size": "中型", "base_chance": 12.0}, {"name": "墨云水蜥", "rarity": "普通", "habitat": "岸边芦苇丛中", "story": "身形细长，传说能隐身于浓雾之中，令过客心生疑窦。", "bait": "蚯蚓", "weather": null, "time": null, "min_size": "小型", "base_chance": 20.0}, {"name": "玉珑鱼", "rarity": "罕见", "habitat": "隐蔽在水草丛中", "story": "据说其背鳍上刻有上古神秘符文，窥见其中便能知晓远古传说。", "bait": "莲花子", "weather": null, "time": null, "min_size": "小型", "base_chance": 10.0}, {"name": "晨曦微鳞", "rarity": "普通", "habitat": "晨光映照的水面", "story": "在初升的阳光中闪烁，这鱼仿佛捎带着一天的希望。", "bait": "水蜜桃果", "weather": null, "time": "黎明", "min_size": "中型", "base_chance": 18.0}, {"name": "蓝幽丝魁", "rarity": "罕见", "habitat": "岩隙幽深处", "story": "其幽蓝的光泽传说能引动水灵的安抚，使周围生物趋于平静。", "bait": "河豚卵", "weather": null, "time": null, "min_size": "中型", "base_chance": 8.0}, {"name": "琉璃飞鳞", "rarity": "普通", "habitat": "水面近处", "story": "行动敏捷，每当雨珠击打水面，便蹿出水花，似与天空共舞。", "bait": "彩虹虾", "weather": null, "time": null, "min_size": "小型", "base_chance": 22.0}, {"name": "翠烟影鲤", "rarity": "罕见", "habitat": "浓密的水草丛中", "story": "隐匿于翠绿之间，它的出现常被视为吉祥的预兆。", "bait": "小麦虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 14.0}, {"name": "落霞幻鳞", "rarity": "普通", "habitat": "夕阳映射的水域", "story": "每当夕阳下沉，鱼鳞反射出的余晖，仿佛凝固了时光。", "bait": "蜜虫", "weather": null, "time": "夜晚", "min_size": "中型", "base_chance": 12.0}, {"name": "锦瑟螺魅", "rarity": "普通", "habitat": "隐秘的湖底缝隙", "story": "传说其体内蕴藏着远古乐章的节拍，令人心驰神往。", "bait": "水生昆虫", "weather": null, "time": null, "min_size": "小型", "base_chance": 10.0}, {"name": "龙吟苍鲀", "rarity": "传说", "habitat": "湖心深处", "story": "古老龙吟之力凝聚而成的圣兽，谁能捕捉到它，谁便掌握了水域的秘密。", "bait": "天蚕丝", "weather": null, "time": null, "min_size": "大型", "base_chance": 3.0}], "story": "流光锦渊位于隐秘的竹林深处，水面如锦，记录着古老东方传说的余韵。传说每一道光影都是远古守护神的低语，呼唤着有缘人前来探秘。"}, {"name": "碧影幽湾", "level": 15, "geo_feature": "隐匿于苍翠森林深处的一片湖泊，湖水碧绿幽深，仿佛藏着古老的预言", "aquatic_params": {"water_temp": 14.0, "water_salinity": 10.0, "water_depth": 8.0, "water_flow": 1.0}, "fish_list": [{"name": "幻蓝灵鳗", "rarity": "传说", "habitat": "深潭裂缝", "story": "传说其体内蕴藏着被遗忘的水之精魂，每当月光与星辉交织时便会显露真容", "bait": "灵光浮萍", "weather": "雷雨", "time": "夜晚", "min_size": "巨型", "base_chance": 0.005}, {"name": "碧鳞青鲤", "rarity": "罕见", "habitat": "湖畔浅滩", "story": "鳞片闪出淡淡碧光，仿佛包裹着湖中传说", "bait": "青草虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.08}, {"name": "幽虾", "rarity": "罕见", "habitat": "湖边藻丛", "story": "群聚而行的小虾轻巧敏捷，总与微风共舞", "bait": "水蚯蚓", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.08}, {"name": "碧波银鲹", "rarity": "罕见", "habitat": "中层水域", "story": "身体侧面的银光似乎映照着湖底的古老秘闻", "bait": "砂虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.08}, {"name": "林泉赤鳟", "rarity": "罕见", "habitat": "溪流接入处", "story": "在流动的泉水中，其红色斑纹宛若秋叶飘零", "bait": "嫩虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.08}, {"name": "静水白鲢", "rarity": "普通", "habitat": "湖中央", "story": "常见于静谧水面，体态虽朴实却充满生命力", "bait": "面包虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.2}, {"name": "青影银鲤", "rarity": "普通", "habitat": "浅水边缘", "story": "倒映在波光中的身形使人仿若见到幻影", "bait": "青蚯蚓", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.2}, {"name": "枫舞落鳞", "rarity": "普通", "habitat": "湖边枫树林中", "story": "秋风中轻扬的落鳞仿佛诉说着岁月的故事", "bait": "枫叶虫", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.2}, {"name": "微澜草鱼", "rarity": "普通", "habitat": "草丛慢流", "story": "在缓缓流动的水中，草鱼显得格外悠闲", "bait": "草虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.2}, {"name": "浮光尾鲶", "rarity": "普通", "habitat": "湖边浮萍覆盖处", "story": "尾巴闪烁着微光，令人在追逐中倍感神秘", "bait": "水草", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.2}], "story": "碧影幽湾隐藏在森林深处，湖水幽绿如同远古预言的眼睛，每当微风起，湖中的传说便悄然诉说。"}, {"name": "梦璃幻泉", "level": 18, "geo_feature": "坐落在彩虹余晖中，这座泉水呈现出奇异的水色，仿佛抽离了现实，直抵二次元梦境的心脏。", "aquatic_params": {"water_temp": 20.0, "water_salinity": 0.0, "water_depth": 7.0, "water_flow": 1.0}, "fish_list": [{"name": "梦璃摇曳", "rarity": "普通", "habitat": "泉边花丛", "story": "在泉水的倒影中轻轻摇曳，每一丝波纹都记录着梦境的温柔。", "bait": "花露", "weather": null, "time": null, "min_size": "小型", "base_chance": 20.0}, {"name": "虹彩幻鳞", "rarity": "罕见", "habitat": "彩虹桥下", "story": "鳞片闪烁出彩虹的光辉，每个角度都诉说着不同的奇幻传说。", "bait": "虹石碎片", "weather": null, "time": null, "min_size": "中型", "base_chance": 15.0}, {"name": "流光梦魟", "rarity": "普通", "habitat": "泉边石隙", "story": "流动的身影恰似梦境中掠过的幻影，让人目光难以捕捉。", "bait": "细沙", "weather": null, "time": null, "min_size": "中型", "base_chance": 17.0}, {"name": "泪痕水灵", "rarity": "罕见", "habitat": "泉中深处", "story": "据说每当雨后现身，其泪痕便能洗去过往的烦忧，为钓者带来新生的希望。", "bait": "雨露", "weather": null, "time": null, "min_size": "小型", "base_chance": 13.0}, {"name": "幻月微鲈", "rarity": "普通", "habitat": "泉边浅滩", "story": "月光下其身影微弱闪烁，似乎藏有远古水神的低语与祝福。", "bait": "微月露珠", "weather": null, "time": null, "min_size": "小型", "base_chance": 19.0}, {"name": "彩翼飞鱼", "rarity": "罕见", "habitat": "空中之镜", "story": "冲破水面的瞬间展露出宛如彩翼般的光影，令人惊叹于自然奇观的绚丽。", "bait": "飞羽", "weather": null, "time": null, "min_size": "小型", "base_chance": 12.0}, {"name": "梦谷守望者", "rarity": "传说", "habitat": "幽谷之泉", "story": "传说中守护梦境的神秘存在，每当它现身，命运的转机便随之降临。", "bait": "守护符", "weather": null, "time": null, "min_size": "大型", "base_chance": 5.0}, {"name": "霓虹碎影", "rarity": "普通", "habitat": "泉边微光处", "story": "如同破碎的霓虹一般，在水面上划出稍纵即逝的美丽瞬间。", "bait": "碎光", "weather": null, "time": null, "min_size": "中型", "base_chance": 16.0}, {"name": "璃光幽魂", "rarity": "罕见", "habitat": "泉石缝隙", "story": "在幽静的角落中偶现其身，传闻每一道光影都蕴藏着古老的秘密。", "bait": "幽魂粉", "weather": null, "time": null, "min_size": "小型", "base_chance": 14.0}, {"name": "泉语琴鸣", "rarity": "普通", "habitat": "古琴石旁", "story": "传说倾听泉水奏出的乐章能唤醒沉睡的智慧，那琴音如梦如幻，余音绕梁。", "bait": "琴弦", "weather": null, "time": null, "min_size": "中型", "base_chance": 18.0}], "story": "梦璃幻泉不仅以奇异的水色著称，更因那传说中的水精灵而散发着独特魅力，仿佛二次元与现实的交汇处，等待着有缘人去探寻其中无尽的秘密。"}, {"name": "青蓝幽渊", "level": 20, "geo_feature": "被魔法污染的幽蓝河流，暗流涌动间传说封印着远古的力量", "aquatic_params": {"water_temp": 15.0, "water_salinity": 0.0, "water_depth": 12.0, "water_flow": 5.0}, "fish_list": [{"name": "封印之灵", "rarity": "传说", "habitat": "幽蓝幽渊深处", "story": "传说中被古代文明封印的水之精灵，在月影交织的夜晚悄然现身，散发淡蓝光辉。", "bait": "幽梦花瓣", "weather": "多云", "time": "夜晚", "min_size": "巨型", "base_chance": 0.02}, {"name": "幽蓝鲤", "rarity": "罕见", "habitat": "河边静谧的浅滩", "story": "在幽蓝河中常见，身上带有淡蓝色光泽，仿佛映射着深渊中的秘密。", "bait": "银色小虾", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.12}, {"name": "月影鲶鱼", "rarity": "罕见", "habitat": "河底沉积的暗处", "story": "常在月光下游动，传闻其鳞片中藏有淡淡的月光能量。", "bait": "月光蠕虫", "weather": "多云", "time": "夜晚", "min_size": "中型", "base_chance": 0.1}, {"name": "灵溪鳟鱼", "rarity": "罕见", "habitat": "清澈支流", "story": "因传说中曾吸收古代魔法而拥有灵动的身姿和神秘的鳞光。", "bait": "闪烁虫卵", "weather": null, "time": "黎明", "min_size": "大型", "base_chance": 0.11}, {"name": "星辰鱿鱼", "rarity": "罕见", "habitat": "河流中晚间漩涡附近", "story": "仿佛蘸染了星辰的光辉，游曳在幽蓝深处，引来众多钓客猜测其来历。", "bait": "星光碎片", "weather": "晴天", "time": null, "min_size": "中型", "base_chance": 0.09}, {"name": "迷雾剑尾", "rarity": "罕见", "habitat": "雾气弥漫的中流区域", "story": "细长的体型和锐利的尾鳍赋予其如同剑影般的神秘气势。", "bait": "薄雾蚯蚓", "weather": "多云", "time": "黎明", "min_size": "大型", "base_chance": 0.1}, {"name": "平静小鳝", "rarity": "普通", "habitat": "静水边缘", "story": "体型细小，常在浅滩中悠然自在地穿梭。", "bait": "小型昆虫", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.15}, {"name": "微光蓝虾", "rarity": "普通", "habitat": "河床岩缝中", "story": "散发微弱蓝光，常聚集在河石之间，成为低调的风景。", "bait": "细小藻类", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.14}, {"name": "细鳞泥鱼", "rarity": "普通", "habitat": "泥沙沉积区", "story": "适应泥泞环境的小生物，其细小鳞片仿佛隐藏了河流的秘密。", "bait": "沼泽蚯蚓", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.13}, {"name": "碧流蛤蜊", "rarity": "普通", "habitat": "岩石堆积的河滩", "story": "固守在岩石间，随着水流静静生长，低调而神秘。", "bait": "海藻碎片", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.14}], "story": "青蓝幽渊因其神秘而诡谲的水域传说吸引着无数探险者，传闻在月黑风高之夜，封印之灵会短暂苏醒，散发着诱人的幽蓝光芒。"}, {"name": "云梦仙涧", "level": 22, "geo_feature": "位于苍翠古木下的幽谷之中，云雾缭绕，仿佛仙境一般的水涧。", "aquatic_params": {"water_temp": 16.0, "water_salinity": 0.05, "water_depth": 4.0, "water_flow": 2.0}, "fish_list": [{"name": "梦迷青鳞", "rarity": "罕见", "habitat": "雾气弥漫的浅水处", "story": "青色鳞片在薄雾中若隐若现，仿佛引人进入梦幻世界。", "bait": "青螺肉", "weather": null, "time": null, "min_size": "中型", "base_chance": 12.0}, {"name": "幽藤滑鳝", "rarity": "普通", "habitat": "岩石缝隙间", "story": "体型柔韧，能在湿滑的岩缝间自由穿行，是水涧中常见的生物。", "bait": "腐叶虫", "weather": null, "time": null, "min_size": "小型", "base_chance": 18.0}, {"name": "仙云碎影", "rarity": "罕见", "habitat": "雾气深处的隐秘潭边", "story": "其身形轻盈飘渺，如同被仙云化作的碎影，难以捕捉。", "bait": "灵芝孢子", "weather": null, "time": null, "min_size": "中型", "base_chance": 10.0}, {"name": "露染灵鲤", "rarity": "普通", "habitat": "荷花盛开的水边", "story": "鱼体上似乎沾染了晨露的清光，闪烁着柔和的光泽。", "bait": "荷花花瓣", "weather": null, "time": "黎明", "min_size": "中型", "base_chance": 16.0}, {"name": "翠羽瑶鱼", "rarity": "罕见", "habitat": "水藻丛生之处", "story": "鳞片如翠羽般亮丽，每一次闪动都诉说着古老的传说。", "bait": "竹虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 10.0}, {"name": "微霜月虾", "rarity": "普通", "habitat": "冰凉的溪流段", "story": "在明月下闪烁着微霜般的光芒，被视为水涧中的精灵。", "bait": "冻水螺", "weather": null, "time": "夜晚", "min_size": "小型", "base_chance": 20.0}, {"name": "星语流光", "rarity": "罕见", "habitat": "星辰倒映的水面", "story": "每当细雨微霖，仿佛能听见星辰与流水低语的声音。", "bait": "流光虾", "weather": null, "time": null, "min_size": "中型", "base_chance": 12.0}, {"name": "香风垂尾", "rarity": "普通", "habitat": "水边落叶与花瓣交织之处", "story": "尾鳍轻摆，随风送来淡淡花香，令人心旷神怡。", "bait": "花蜜虫", "weather": null, "time": null, "min_size": "小型", "base_chance": 18.0}, {"name": "幻梦花魟", "rarity": "普通", "habitat": "水域深处", "story": "身形如梦似幻，令钓者仿佛置身于仙境之中。", "bait": "微光贝", "weather": null, "time": null, "min_size": "中型", "base_chance": 10.0}, {"name": "霄汉仙魟", "rarity": "传说", "habitat": "最深的幽潭中", "story": "传说乃仙界赐予的灵兽，身躯蕴含着调和天地的秘力，极为罕见。", "bait": "仙露", "weather": null, "time": null, "min_size": "大型", "base_chance": 3.0}], "story": "云梦仙涧隐藏于古木环绕的幽谷中，常年笼罩着轻柔的云雾。传说这里是仙人遗落的梦境，每条鱼类都承载着一段遥远而迷离的故事。"}, {"name": "焰纹秘渊", "level": 25, "geo_feature": "隐藏在火山脚下的熔岩湖泊，湖内热浪翻滚，映出宛如火焰般的色彩", "aquatic_params": {"water_temp": 28.0, "water_salinity": 15.0, "water_depth": 15.0, "water_flow": 3.0}, "fish_list": [{"name": "血焰巨虾", "rarity": "传说", "habitat": "岩浆边缘", "story": "据传这只巨虾曾在古代火祭中获得火元素祝福，每当火山预兆现时便会现身", "bait": "炽焰虾茧", "weather": "雷雨", "time": "夜晚", "min_size": "巨型", "base_chance": 0.005}, {"name": "熔岩赤鳜", "rarity": "罕见", "habitat": "温热水边", "story": "身披赤红色鳞片，闪烁着熔岩般的热光", "bait": "炽热虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.08}, {"name": "炽光短鳍", "rarity": "罕见", "habitat": "近岸浅滩", "story": "小巧而敏捷的鱼群在温暖水域中穿梭，如同跳动的火花", "bait": "热砂虫", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.08}, {"name": "火影银鲤", "rarity": "罕见", "habitat": "水面微热区域", "story": "在火红与银白交织的光影中穿行，显得神秘莫测", "bait": "火花蚯蚓", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.08}, {"name": "焰纹电鳗", "rarity": "罕见", "habitat": "深水边缘", "story": "鳗身上闪烁着淡淡的电光，仿佛蕴藏着火焰的怒吼", "bait": "电光虾", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.08}, {"name": "温玉鳜鱼", "rarity": "普通", "habitat": "退火区", "story": "常见于熔岩冷却后的区域，体态朴实而温和", "bait": "温泉虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.2}, {"name": "赤光鲢鱼", "rarity": "普通", "habitat": "湖心", "story": "体色灼热，映照着岩石上的余辉", "bait": "火纹虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.2}, {"name": "火舌青鱼", "rarity": "普通", "habitat": "靠近岩壁", "story": "纤细游动如同火舌拂水，令人感受到热力四射", "bait": "烤火虫", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.2}, {"name": "热浪小虾", "rarity": "普通", "habitat": "水边藻丛", "story": "成群结队的小虾在温暖的水域中孕育出独特的热浪律动", "bait": "热藻虫", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.2}, {"name": "焦痕白鱼", "rarity": "普通", "habitat": "浅水区", "story": "身上带有烤焦的痕迹，仿佛是火焰洗礼后的见证", "bait": "焦糖虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.2}], "story": "焰纹秘渊是一处隐秘于火山之下的熔岩湖泊，湖面映照着火焰般的色彩，传说中每当火山气息浓厚时，血焰巨虾便会现身，昭示着古老火祭的重燃。"}, {"name": "幻霞幽渊潭", "level": 26, "geo_feature": "坐落于深山幽谷，潭水幻彩流转，时而映出奇异天象。", "aquatic_params": {"water_temp": 16.0, "water_salinity": 0.3, "water_depth": 12.0, "water_flow": 0.3}, "fish_list": [{"name": "蕾米莉亚·斯卡雷特", "rarity": "传说", "habitat": "潭边阴影处", "story": "这位传说中的吸血鬼领主，常在月夜降临潭畔，以神秘的血红光辉照亮黑暗。", "bait": "血红花瓣", "weather": "多云", "time": null, "min_size": "大型", "base_chance": 0.05}, {"name": "芙兰朵露·斯卡雷特", "rarity": "罕见", "habitat": "深潭中心", "story": "她狂放不羁的性情与破晓时分的血色交相辉映，令人既畏且惧。", "bait": "夜色浆果", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.14}, {"name": "琪露诺", "rarity": "普通", "habitat": "潭水表层", "story": "这位冰之妖精在幻彩潭中展现天真活泼的身影，为水面带来一抹清冽的寒光。", "bait": "冻结水珠", "weather": "雪", "time": null, "min_size": "小型", "base_chance": 0.18}, {"name": "洩矢诹访子", "rarity": "罕见", "habitat": "潭石周边", "story": "身形虽小，却能操控大地的重力，据说她每次出场都伴随着微震般的湖底涟漪。", "bait": "重力颗粒", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.12}, {"name": "灵乌路空", "rarity": "普通", "habitat": "垂柳掩映之处", "story": "他被传为湖中梦境的使者，仿佛能将现实与虚幻轻易交融。", "bait": "梦幻羽毛", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.15}, {"name": "魂魄妖梦", "rarity": "普通", "habitat": "幽深潭底", "story": "化作飘忽幽灵的传说，她似乎承载了昔日的悲欢离合，静静沉寂在湖心。", "bait": "幽灵花瓣", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.16}, {"name": "八云紫", "rarity": "罕见", "habitat": "潭中迷雾", "story": "传言她能随意穿越界限，紫色光晕伴随她的每一次现身，总让人捉摸不透其真正目的。", "bait": "幻影烟霞", "weather": "雾", "time": null, "min_size": "大型", "base_chance": 0.1}, {"name": "河城荷取", "rarity": "普通", "habitat": "浅潭边缘", "story": "这位水中隐居的河童，以对湖水脉络的独到理解，常在夜色中嬉戏。", "bait": "河童草", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.2}, {"name": "藤原妹红", "rarity": "普通", "habitat": "潭底火山岩区", "story": "传说中的不死之身使她在火山岩中焕发出不灭火花，她的出场总伴随一缕神秘火光。", "bait": "不灭火花", "weather": "暮光", "time": null, "min_size": "大型", "base_chance": 0.08}, {"name": "蓬莱山辉夜", "rarity": "普通", "habitat": "月影环绕的潭边", "story": "据说她来自月宫，每当月光洒落之时，便于潭边留下一段跨越时空的传说。", "bait": "月光花粉", "weather": "暮光", "time": null, "min_size": "中型", "base_chance": 0.12}], "story": "幻霞幽渊潭隐匿于深山幽谷之中，潭水幻彩流转，映出异世天象，东方魔法的神秘力量仿佛在每一次水波荡漾中悄然低语。"}, {"name": "霓裳映月池", "level": 28, "geo_feature": "一处隐于古园之中的静谧池塘，水面映出斑斓霓裳，仿佛宫廷秘语般优雅迷人。", "aquatic_params": {"water_temp": 20.0, "water_salinity": 0.0, "water_depth": 5.0, "water_flow": 1.0}, "fish_list": [{"name": "月影游鲤", "rarity": "罕见", "habitat": "池边清浅之处", "story": "在皓月下银白色的鳞光如流水般荡漾，映出朦胧的月影。", "bait": "柳絮虫", "weather": null, "time": "夜晚", "min_size": "中型", "base_chance": 12.0}, {"name": "霓虹碎鳞", "rarity": "普通", "habitat": "水面波光粼粼处", "story": "鳞片折射出霓虹般的梦幻色彩，使整池水域焕发奇异光芒。", "bait": "彩虹虾", "weather": null, "time": null, "min_size": "中型", "base_chance": 20.0}, {"name": "苏幕轻吟", "rarity": "罕见", "habitat": "隐秘的水草丛下", "story": "似有若无的轻吟仿佛低诉着宫廷秘史，每次出现都令人心动。", "bait": "莲子虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 10.0}, {"name": "花影织梦", "rarity": "普通", "habitat": "莲花丛边", "story": "月光下花影婆娑，似梦似幻，编织出一场场静谧的水中芭蕾。", "bait": "花瓣虫", "weather": null, "time": "夜晚", "min_size": "中型", "base_chance": 16.0}, {"name": "翠月流辉", "rarity": "罕见", "habitat": "水中倒映斑驳处", "story": "传说此鱼一现，便意味月神将降，带来无尽的祥瑞。", "bait": "月光草", "weather": null, "time": null, "min_size": "中型", "base_chance": 8.0}, {"name": "莲心帆鳝", "rarity": "普通", "habitat": "池中幽深部", "story": "其身形绰约，如同水中轻舟缓行，带着恰到好处的优雅。", "bait": "莲花籽", "weather": null, "time": null, "min_size": "小型", "base_chance": 14.0}, {"name": "落潮蓝翎", "rarity": "罕见", "habitat": "波纹起伏之处", "story": "细长的身姿在水中若隐若现，蓝色鳞片恰似夜空中流动的羽毛。", "bait": "蓝藻虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 12.0}, {"name": "花舞水灵", "rarity": "普通", "habitat": "莲叶交错的静水区", "story": "轻盈如花瓣般的身影在水中优雅地舞动，仿佛能唤醒沉睡的花香。", "bait": "水仙花粉", "weather": null, "time": null, "min_size": "小型", "base_chance": 18.0}, {"name": "幽兰缱绻", "rarity": "普通", "habitat": "池边岩石缝隙中", "story": "传说这鱼是幽兰化生，带来夏末的柔情与浪漫。", "bait": "石蚕", "weather": null, "time": null, "min_size": "中型", "base_chance": 12.0}, {"name": "玉露仙魟", "rarity": "传说", "habitat": "池心最深渊", "story": "据说体内蕴含玄妙仙力，能够令星辰为之低语，是传说中唯一天降的水中灵兽。", "bait": "仙露珠", "weather": null, "time": null, "min_size": "巨型", "base_chance": 3.0}], "story": "霓裳映月池被古园秘境所环绕，池水映出霓裳般的光辉，仿佛在诉说着皇朝往昔的诗意与传奇。每当月华洒落，池中鱼跃，便奏响一曲东风古韵。"}, {"name": "寂静冰川溪", "level": 30, "geo_feature": "静谧的冰川融水聚成的小溪，冰蓝色的水流寒意袭人", "aquatic_params": {"water_temp": 5.0, "water_salinity": 0.0, "water_depth": 8.0, "water_flow": 4.0}, "fish_list": [{"name": "冰魄圣凰", "rarity": "传说", "habitat": "冰川深处的隐秘裂缝", "story": "传说中凝结着永恒寒霜的圣凰，只有在极寒与宁静交融时才会显现其光芒。", "bait": "寒冰碎晶", "weather": "雪", "time": "黎明", "min_size": "巨型", "base_chance": 0.02}, {"name": "霜角鳟", "rarity": "罕见", "habitat": "冰冷溪边岩石下", "story": "背部覆盖着细小冰晶，仿佛自然铸就的霜之角。", "bait": "冰蚯蚓", "weather": "晴天", "time": "黎明", "min_size": "中型", "base_chance": 0.11}, {"name": "寒冰银鲤", "rarity": "罕见", "habitat": "溪流中冰蓝石旁", "story": "闪烁银光，生活在冰川融水中，似能感知外界寒意。", "bait": "冰蓝小虾", "weather": null, "time": "白天", "min_size": "大型", "base_chance": 0.1}, {"name": "晶羽鲶", "rarity": "罕见", "habitat": "暗处冰影区域", "story": "细长的身躯闪烁寒光，每一次摆动都仿佛在诉说着冰川的传说。", "bait": "晶莹虫卵", "weather": "多云", "time": "夜晚", "min_size": "中型", "base_chance": 0.1}, {"name": "冰眸金虾", "rarity": "罕见", "habitat": "溪流深处", "story": "金色的眼眸在寒冰中闪烁，传闻能映出未来的幻影。", "bait": "雪花虫", "weather": null, "time": "夜晚", "min_size": "小型", "base_chance": 0.09}, {"name": "雪影鳕", "rarity": "罕见", "habitat": "冰川激流中", "story": "身披淡淡雪白，如同冬日幻影般迅捷游动。", "bait": "冰晶碎屑", "weather": "暴雪", "time": "白天", "min_size": "大型", "base_chance": 0.09}, {"name": "冻水小鳝", "rarity": "普通", "habitat": "溪流边细小岩石间", "story": "适应寒冷环境的小型鱼类，通常成群出没于冰水边缘。", "bait": "冰冻蚯蚓", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.14}, {"name": "冰心泥鱼", "rarity": "普通", "habitat": "溪床泥沙中", "story": "身形矮小，色泽朴实，常在夜晚的冰冷水域中潜行。", "bait": "冻泥虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.13}, {"name": "微霜斑鱼", "rarity": "普通", "habitat": "清澈水面附近", "story": "随着微风拂过，斑点上染上了初霜的色彩，成为冬日一道温柔的风景。", "bait": "冰霜藻粒", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.12}, {"name": "流冰贝壳", "rarity": "普通", "habitat": "溪边冰块堆积处", "story": "虽无鲜艳色彩，但坚固的贝壳上刻着岁月的痕迹，仿佛述说着寒冰久远的故事。", "bait": "碎冰藻", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.13}], "story": "在寂静冰川溪中，冰与水的交响谱写出一曲古老而悠远的传说。勇者们前来探求那冰魄圣凰的踪迹，也许能揭开百年寒冰背后的秘密。"}, {"name": "浮空幻湖", "level": 35, "geo_feature": "悬浮岛屿环绕的湖泊，湖面常笼罩着薄雾与梦幻光影", "aquatic_params": {"water_temp": 20.0, "water_salinity": 0.0, "water_depth": 15.0, "water_flow": 1.0}, "fish_list": [{"name": "幻影鱼王", "rarity": "传说", "habitat": "湖心幻境", "story": "只在双月蚀交汇时现身的幻影鱼王，其出现预示着湖水中隐藏着更古老的传说。", "bait": "银月碎片", "weather": "多云", "time": "夜晚", "min_size": "巨型", "base_chance": 0.015}, {"name": "云浮虹鳟", "rarity": "罕见", "habitat": "浮岛边缘的清水区", "story": "身披彩虹鳞片，常在岛屿边际与云雾中嬉戏。", "bait": "浮云小虾", "weather": "多云", "time": "黎明", "min_size": "中型", "base_chance": 0.11}, {"name": "空灵鲈鱼", "rarity": "罕见", "habitat": "湖边岩石缝隙", "story": "身形修长如同灵动的琴弦，偶尔跃出水面似在诉说古老传说。", "bait": "湖边虫卵", "weather": null, "time": null, "min_size": "大型", "base_chance": 0.1}, {"name": "飘渺金鲤", "rarity": "罕见", "habitat": "湖心微光水域", "story": "金色鱼鳞在日光下闪烁，恰似湖中飘渺的不真实幻影。", "bait": "金粉颗粒", "weather": "晴天", "time": "白天", "min_size": "大型", "base_chance": 0.12}, {"name": "幻羽鲶", "rarity": "罕见", "habitat": "静谧浮岛暗角", "story": "一旦低吟湖底，便能听到仿佛来自远古的呼唤，其鳞翼如飘渺幻影。", "bait": "幽蓝虫", "weather": "多云", "time": "夜晚", "min_size": "中型", "base_chance": 0.1}, {"name": "雾隐鳕鱼", "rarity": "罕见", "habitat": "湖水深处迷雾缭绕处", "story": "常在薄雾中穿行，身形隐约，仿佛与湖水融为一体。", "bait": "冰冷小虾", "weather": "多云", "time": "夜晚", "min_size": "大型", "base_chance": 0.09}, {"name": "轻羽小鲤", "rarity": "普通", "habitat": "湖边浅水", "story": "体态纤细活泼，常在湖边捕食浮游生物。", "bait": "湖藻碎片", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.14}, {"name": "漂浮泥鳅", "rarity": "普通", "habitat": "湖底沙泥", "story": "行动缓慢，但在薄雾中却能意外溜出水面，成为趣味一景。", "bait": "微小昆虫", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.13}, {"name": "静水锦鲤", "rarity": "普通", "habitat": "平静湖岸", "story": "是湖中最常见的居民，华丽的花纹常吸引初学者的目光。", "bait": "彩虹碎屑", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.15}, {"name": "柔光斑点鱼", "rarity": "普通", "habitat": "湖中浅滩", "story": "斑点闪烁柔和光芒，是湖水中最平易近人的生物。", "bait": "湖中浮游", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.13}], "story": "传说中浮空幻湖拥有连接天空与大地的神秘力量，每当湖面起雾，幻影鱼王便在双月蚀之夜显现，为勇者留下探索未知的线索。"}, {"name": "熔岩心潭", "level": 40, "geo_feature": "被火山熔岩洗礼的炽热火山湖，湖水中燃烧着不灭的热光", "aquatic_params": {"water_temp": 35.0, "water_salinity": 5.0, "water_depth": 20.0, "water_flow": 3.0}, "fish_list": [{"name": "炽焰灵魄", "rarity": "传说", "habitat": "熔岩湖心", "story": "传闻湖中孕育着操控火与水元素的奇异生物，其炽热的灵魂可以将冰冷融化。", "bait": "火红熔岩石", "weather": "晴天", "time": "白天", "min_size": "巨型", "base_chance": 0.02}, {"name": "燃烧鲤", "rarity": "罕见", "habitat": "湖边温泉流域", "story": "全身带着火红色斑纹，仿佛体内燃烧着永不熄灭的火焰。", "bait": "炙热虫卵", "weather": "晴天", "time": "白天", "min_size": "大型", "base_chance": 0.11}, {"name": "熔岩虎鱼", "rarity": "罕见", "habitat": "岩浆涌动的暗流中", "story": "凶猛且迅捷，虎斑样的鳞片在火光映衬下格外耀眼。", "bait": "熔岩碎屑", "weather": "晴天", "time": "夜晚", "min_size": "大型", "base_chance": 0.1}, {"name": "火花鳗", "rarity": "罕见", "habitat": "炽热水底裂缝", "story": "每次闪过水面都伴随火花飞溅，传说其体内蕴藏着火山之心。", "bait": "红晶碎片", "weather": "晴天", "time": "白天", "min_size": "大型", "base_chance": 0.09}, {"name": "热流鲶", "rarity": "罕见", "habitat": "温热流动区", "story": "在炽热的水流中游动，似乎与湖中的熔岩能量融为一体。", "bait": "蒸汽小虫", "weather": null, "time": "夜晚", "min_size": "大型", "base_chance": 0.1}, {"name": "赤焰银虾", "rarity": "罕见", "habitat": "湖边矿岩区", "story": "小而闪亮，犹如湖底燃起的星火，身形迅捷。", "bait": "微热浮游", "weather": "晴天", "time": "白天", "min_size": "小型", "base_chance": 0.09}, {"name": "焦炭小鳞", "rarity": "普通", "habitat": "湖边沉积的火山灰区", "story": "体型微小，皮肤呈现被熔岩碳化的深色。", "bait": "火山灰", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.14}, {"name": "岩心泥鳅", "rarity": "普通", "habitat": "湖底砂砾间", "story": "适应高温环境的生物，时而从岩缝中探出头来觅食。", "bait": "微热小虫", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.13}, {"name": "微熔贝壳", "rarity": "普通", "habitat": "湖边温泉石块上", "story": "外表微熔，透露出火山独特的纹理美感，是湖中常见的装饰。", "bait": "熔岩藻类", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.13}, {"name": "温泉小虾", "rarity": "普通", "habitat": "温泉微流处", "story": "生活在温暖水域的小虾群，总能集结成迷人的涟漪。", "bait": "温热藻粒", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.13}], "story": "熔岩心潭的水域因受火山熔岩洗礼而炽热非凡，传说这片湖水中蕴育的生物拥有操控自然元素的力量，吸引着无数勇者前来探寻火与水交织的秘密。"}, {"name": "苍蓝深潭", "level": 45, "geo_feature": "位于远古遗迹的深处，苍蓝深潭静谧而神秘，湖面微波荡漾，仿佛蕴藏着海洋远古的低语。", "aquatic_params": {"water_temp": 12.0, "water_salinity": 0.0, "water_depth": 20.0, "water_flow": 0.5}, "fish_list": [{"name": "幽蓝幻鳞", "rarity": "罕见", "habitat": "湖底碎石堆间", "story": "幽蓝幻鳞的鳞片散发着淡淡的蓝光，每当月光映照下，湖底仿佛铺满了蓝色星辰。", "bait": "蓝晶碎屑", "weather": "晴天", "time": "夜晚", "min_size": "中型", "base_chance": 0.17}, {"name": "苍浪幽鲤", "rarity": "普通", "habitat": "潭水边缘的草丛中", "story": "苍浪幽鲤群游于湖边浅滩，它们的身影在水中来回穿梭，如同隐秘的守护者。", "bait": "水草茎尖", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.28}, {"name": "秘蓝流光", "rarity": "罕见", "habitat": "湖中幽深处", "story": "秘蓝流光们传说是深潭古文明遗留的见证，它们的身体闪烁着昔日祭典的余辉。", "bait": "灵光水珠", "weather": "多云", "time": "黎明", "min_size": "大型", "base_chance": 0.16}, {"name": "深潭影鲫", "rarity": "普通", "habitat": "岩石环绕的湖边", "story": "传说深潭影鲫易于群聚，它们的突然出现常预示着潭底隐藏的秘密即将揭开。", "bait": "细小藻类", "weather": null, "time": "夜晚", "min_size": "中型", "base_chance": 0.27}, {"name": "碧水幽灵", "rarity": "传说", "habitat": "湖心幽静的漩涡中", "story": "据古书记载，碧水幽灵乃是海洋之神的遗魂，每当满月之夜，便在深潭中显现其神秘身影。", "bait": "月华碎片", "weather": "多云", "time": "夜晚", "min_size": "巨型", "base_chance": 0.06}, {"name": "蓝翎细鳞", "rarity": "罕见", "habitat": "湖水中央的浮游区", "story": "蓝翎细鳞体态轻盈，鳞片犹如羽毛般柔软，传闻其鳞片可炼制神秘蓝药。", "bait": "浮游虾", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.15}, {"name": "湖心舞影", "rarity": "普通", "habitat": "湖中隐秘的水草丛", "story": "湖心舞影在潭水中优雅游弋，仿佛在演绎一场无声的水中芭蕾，吸引无数钓客驻足观望。", "bait": "嫩水藻", "weather": null, "time": "夜晚", "min_size": "中型", "base_chance": 0.25}, {"name": "暮光微鳞", "rarity": "普通", "habitat": "湖岸阴影处", "story": "暮光微鳞在夕阳余晖下闪烁微光，其稀有的鳞片常被视作护身符。", "bait": "夜光果", "weather": "多云", "time": "夜晚", "min_size": "中型", "base_chance": 0.24}, {"name": "旋蓝细鲷", "rarity": "罕见", "habitat": "水流缓缓回旋的中心区", "story": "这种鲷鱼以其灵巧的身姿和蓝色斑纹闻名，被认为是湖水脉动的流动符号。", "bait": "蓝色小虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.18}, {"name": "星痕小鱼", "rarity": "普通", "habitat": "湖边砂石间", "story": "星痕小鱼体表隐约泛出星星点点的光泽，仿佛每一处都是天空在水中的映像。", "bait": "微砂藻", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.29}], "story": "苍蓝深潭坐落于古树遗迹深处，湖水幽蓝，似乎凝固了时间。传说每一缕水波都记录了远古祭祀的低语，等待有心人来解开那被历史尘埃掩埋的秘密。"}, {"name": "珊瑚风暴海域", "level": 50, "geo_feature": "常伴随暴风雨天气的珊瑚礁海域，海面波涛汹涌，诡谲莫测", "aquatic_params": {"water_temp": 25.0, "water_salinity": 35.0, "water_depth": 30.0, "water_flow": 8.0}, "fish_list": [{"name": "残骸巨兽", "rarity": "传说", "habitat": "破碎渔船残骸和珊瑚丛中", "story": "以渔船残骸为巢的变异巨兽，据说是暴风雨中海神愤怒的化身，常在狂潮中出没。", "bait": "破碎木片混合魔渣", "weather": "雷雨", "time": "夜晚", "min_size": "巨型", "base_chance": 0.01}, {"name": "暴潮鲨魟", "rarity": "罕见", "habitat": "暗流涌动的珊瑚缝隙", "story": "体型魁梧，能在暴潮中尾随巨兽而游。", "bait": "海藻碎块", "weather": "雷雨", "time": "夜晚", "min_size": "大型", "base_chance": 0.1}, {"name": "雷光鳗鱼", "rarity": "罕见", "habitat": "深邃的珊瑚礁裂隙", "story": "闪电般的迅捷与光芒，使人联想到天际雷霆。", "bait": "电光虾", "weather": "雷雨", "time": "夜晚", "min_size": "大型", "base_chance": 0.09}, {"name": "怒海金枪", "rarity": "罕见", "habitat": "岩石与珊瑚交汇处", "story": "全身覆盖着金色鳞片，每一次穿梭都伴随着海水激荡。", "bait": "金色小鱼", "weather": null, "time": null, "min_size": "大型", "base_chance": 0.11}, {"name": "碎浪海虾", "rarity": "罕见", "habitat": "风暴激起的海床", "story": "体型虽小，但聚集成群时足以掀起海浪的碎片。", "bait": "海底藻类", "weather": "雷雨", "time": "白天", "min_size": "小型", "base_chance": 0.1}, {"name": "海影鳕", "rarity": "罕见", "habitat": "珊瑚阴影下", "story": "在暴风雨来临前后常现身，身影飘忽，似幻似真。", "bait": "珊瑚碎屑", "weather": "多云", "time": "夜晚", "min_size": "大型", "base_chance": 0.09}, {"name": "浅滩小鱼", "rarity": "普通", "habitat": "近岸礁石旁", "story": "体形小巧，成群结队在浅水区穿梭。", "bait": "小型海藻", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.14}, {"name": "海靛斑鱼", "rarity": "普通", "habitat": "珊瑚礁边缘", "story": "斑驳的体色与海中的光影交融，极易融入背景。", "bait": "微光海砂", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.13}, {"name": "微风贝壳鱼", "rarity": "普通", "habitat": "岩石堆积区域", "story": "壳体坚硬且形态独特，常被海中弱水流携带轻舞。", "bait": "碎珊瑚", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.12}, {"name": "浪花银鲦", "rarity": "普通", "habitat": "波涛拍打的浅水区", "story": "在暴风雨后频繁出现在浪花中，银白色的身影分外耀眼。", "bait": "细沙海藻", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.12}], "story": "珊瑚风暴海域以其狂暴的气候和迷离的海景著称，那里的每一次暴风雨，都像是一场史诗般的召唤，将古老传说重新带入人们的视野。"}, {"name": "苍穹沧渊", "level": 55, "geo_feature": "位于极北冰原边缘的辽阔湖泊，水面映衬着苍穹，冰封与风暴交织，展现出极地奇观", "aquatic_params": {"water_temp": 4.0, "water_salinity": 20.0, "water_depth": 60.0, "water_flow": 4.0}, "fish_list": [{"name": "极冰幻鲸", "rarity": "传说", "habitat": "深冰海渊", "story": "这头巨鲸传说是冰神的化身，每当极光闪烁之时，它便带着远古低吟悄然浮现", "bait": "极光藻", "weather": "暴雪", "time": "黎明", "min_size": "巨型", "base_chance": 0.005}, {"name": "冰魄青鳟", "rarity": "罕见", "habitat": "急流冷水中", "story": "身披冰蓝鳞片，仿佛蕴含了冰川的精魂，在急流中疾游", "bait": "冰晶虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.08}, {"name": "北风银鲤", "rarity": "罕见", "habitat": "冰边浅滩", "story": "在寒风呼啸中闪现银光，传闻它能解冻冰封的河流", "bait": "冻鳞虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.08}, {"name": "霜影裸鲶", "rarity": "罕见", "habitat": "湖底沙层", "story": "黯淡的色彩和冰层渗透的寒意相互交织，令其身影愈发诡秘", "bait": "霜蚯蚓", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.08}, {"name": "雪舞小虾", "rarity": "罕见", "habitat": "冰缝中", "story": "在逐渐融解的雪水中摇曳，仿佛舞动着冬日的旋律", "bait": "雪花虫", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.08}, {"name": "寒冰鳜鱼", "rarity": "普通", "habitat": "冻结湖面附近", "story": "常见于冰封水域，体态圆润以适应极低温的环境", "bait": "冻鱼虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.2}, {"name": "冰川棱鲤", "rarity": "普通", "habitat": "冰底暗区", "story": "身上奇异的鳞片如同碎冰拼凑，展现出独特的寒冷之美", "bait": "冰川虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.2}, {"name": "极光眸鲷", "rarity": "普通", "habitat": "珊瑚礁边缘", "story": "眼神中仿佛映出极光的绚烂，为冰冷的深渊带来一丝温暖", "bait": "极光虾", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.2}, {"name": "寒风鳖", "rarity": "普通", "habitat": "湖边冰层", "story": "这只鳖经过极寒磨砺，壳体坚硬如冰，传说曾是冰神的侍者", "bait": "冰碎虾", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.2}, {"name": "冰泪斑马鱼", "rarity": "普通", "habitat": "冰层裂隙", "story": "绚丽的花纹如同冰泪，在晶莹剔透的冰层中闪烁，成为极地湖泊的一道风景", "bait": "寒冰虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.2}], "story": "苍穹沧渊坐落于极北冰原边缘，冰封湖面映衬着浩瀚苍穹。传说中极冰幻鲸的出现预示着冰神的低语，每一缕冰风都似在诉说远古的传说。"}, {"name": "魔影悬渊", "level": 60, "geo_feature": "悬于陡峭幽暗的悬崖侧壁，一汪暗影水潭始终笼罩在暮色之中，仿佛有穿梭于现实与梦境间的神秘生灵出没。", "aquatic_params": {"water_temp": 8.0, "water_salinity": 0.0, "water_depth": 25.0, "water_flow": 2.0}, "fish_list": [{"name": "影纹蛇鳗", "rarity": "罕见", "habitat": "悬渊深处的暗流中", "story": "这类蛇鳗以其身上的暗纹得名，据说每一道纹路都刻有一段被遗忘的古咒。", "bait": "夜影软泥", "weather": "多云", "time": "夜晚", "min_size": "大型", "base_chance": 0.16}, {"name": "幽渊鬼鲶", "rarity": "普通", "habitat": "潭底沉沙区", "story": "幽渊鬼鲶形状古怪且行踪诡秘，传闻它们能在水中消失于无形。", "bait": "腐植碎块", "weather": null, "time": "夜晚", "min_size": "中型", "base_chance": 0.27}, {"name": "暮影碎鳞", "rarity": "罕见", "habitat": "水潭暗角", "story": "暮影碎鳞体表破碎的鳞片在昏暗光线下闪烁，仿佛记录着古老废墟的记忆。", "bait": "暮色虫", "weather": "多云", "time": "夜晚", "min_size": "中型", "base_chance": 0.18}, {"name": "黑曜幽灵", "rarity": "传说", "habitat": "悬渊中心的漩涡中", "story": "传说黑曜幽灵乃是暗影精灵的化身，只有在极夜之时才能现身，其出现预示着两个界域的交汇与变革。", "bait": "暗夜晶石", "weather": "多云", "time": "夜晚", "min_size": "大型", "base_chance": 0.05}, {"name": "暗流微光", "rarity": "普通", "habitat": "水潭边缘缓流区", "story": "暗流微光因体表微弱的荧光而得名，其光芒平淡中透出神秘的气息。", "bait": "暗涌藻类", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.25}, {"name": "午夜浮萍", "rarity": "普通", "habitat": "漂浮在水面零星的花团", "story": "午夜浮萍虽无声息，但群聚时水面泛起幽蓝波纹，似乎在低语着夜晚的秘密。", "bait": "暗夜浮藻", "weather": "多云", "time": "夜晚", "min_size": "中型", "base_chance": 0.27}, {"name": "暗影溪虾", "rarity": "罕见", "habitat": "潭边岩石缝隙中", "story": "这种小虾体型虽小，却以敏捷著称，仿佛暗影中的使者在水中穿梭。", "bait": "碎石微藻", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.17}, {"name": "暮雨妖鳞", "rarity": "普通", "habitat": "临近出水的悬崖边", "story": "暮雨妖鳞在夜幕降临时最为活跃，其散落的鳞片似能引发短暂的幻觉。", "bait": "雨霖花瓣", "weather": "雨", "time": "夜晚", "min_size": "中型", "base_chance": 0.26}, {"name": "悬渊薄暮", "rarity": "罕见", "habitat": "水潭幽深之处", "story": "悬渊薄暮总在暮色中隐现，仿佛是夜色本身的投影，稍纵即逝。", "bait": "薄暮虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.18}, {"name": "幽暗脊魟", "rarity": "普通", "habitat": "潭底岩石间隙中", "story": "幽暗脊魟行动缓慢，却总在不经意间冲出水面，让人心惊胆寒。", "bait": "碎裂珊瑚碎片", "weather": null, "time": null, "min_size": "大型", "base_chance": 0.28}], "story": "魔影悬渊隐于幽暗悬崖之侧，终年被暮色笼罩。每一次垂钓都仿佛与未知界域的低语碰面，带来不可预测的命运转折。"}, {"name": "幻月秘池", "level": 65, "geo_feature": "隐匿在远古遗迹裂隙中的月光秘池", "aquatic_params": {"water_temp": 15.0, "water_salinity": 0.0, "water_depth": 8.0, "water_flow": 0.2}, "fish_list": [{"name": "月影幻鲤", "rarity": "传说", "habitat": "秘池深处", "story": "传说在月光最圆之夜，它会浮现于水面，其体内流淌着星辰与梦境的光芒，被视为连接现实与幻想的神圣使者。", "bait": "银月草", "weather": "晴天", "time": "夜晚", "min_size": "巨型", "base_chance": 0.01}, {"name": "梦纹鳞鲈", "rarity": "罕见", "habitat": "月光映照的浅水区", "story": "它的鳞片上闪烁着梦境般的纹路，仿佛记录着古老的预言。", "bait": "幽梦蚯蚓", "weather": null, "time": null, "min_size": "大型", "base_chance": 0.1}, {"name": "星辰银鳟", "rarity": "罕见", "habitat": "月夜秘域", "story": "据说吸食过星辰的碎片后，它便能感受到宇宙脉动的神秘律动。", "bait": "星尘粉末", "weather": null, "time": null, "min_size": "大型", "base_chance": 0.12}, {"name": "幽蓝电鲨", "rarity": "罕见", "habitat": "秘池边缘", "story": "体内闪烁的电光为这静谧的秘池平添了几分魔幻色彩。", "bait": "发光水母", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.15}, {"name": "墨云真鲷", "rarity": "罕见", "habitat": "深水昏影处", "story": "每当水面起雾，它便犹如浮云般出没，令人难以捉摸。", "bait": "墨夜虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.1}, {"name": "银鳞小鳜", "rarity": "普通", "habitat": "秘池浅滩", "story": "群游于浅滩，银色闪光为清晨增添了一抹灵动。", "bait": "蚯蚓", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.3}, {"name": "翠羽斑鱼", "rarity": "普通", "habitat": "池边苔藓丛", "story": "色彩斑斓的身躯在薄雾中若隐若现，是秘池间最引人注目的生物之一。", "bait": "小虾", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.28}, {"name": "泡影鼠鱼", "rarity": "普通", "habitat": "静谧水域", "story": "行动迅捷，如同水中泡影，一闪即逝，常让垂钓者难以捕捉。", "bait": "泡泡虫", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.35}, {"name": "碎梦小鳉", "rarity": "普通", "habitat": "微光映照区", "story": "一群小鳉常在梦幻般的水中嬉戏，传说它们能带来美好的梦境。", "bait": "面包屑", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.4}, {"name": "静夜蛙鲤", "rarity": "普通", "habitat": "幽静密处", "story": "它安静而神秘的存在仿佛能倾听夜的低语，令人心生敬畏。", "bait": "昆虫屑", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.32}], "story": "幻月秘池隐藏于古老遗迹之中，每当皓月当空，便有传说中的神秘生物现身，向有缘人传授远古秘密。"}, {"name": "暮红流溪", "level": 70, "geo_feature": "悬崖边的绯红溪流", "aquatic_params": {"water_temp": 28.0, "water_salinity": 0.0, "water_depth": 12.0, "water_flow": 0.8}, "fish_list": [{"name": "血焰幽魟", "rarity": "传说", "habitat": "溪流深处", "story": "传说它是火神遗留的泪珠，只有在血月之夜现身，释放出炽热而神秘的光辉，预示着命运的转折。", "bait": "红莲花瓣", "weather": "血月", "time": "夜晚", "min_size": "巨型", "base_chance": 0.005}, {"name": "焰纹斑瓢", "rarity": "罕见", "habitat": "红色激流边缘", "story": "其身上布满仿佛燃烧的斑纹，每到夕阳下闪现出隐秘的火光。", "bait": "火炭虫", "weather": null, "time": null, "min_size": "大型", "base_chance": 0.1}, {"name": "朱砂鳞鲤", "rarity": "罕见", "habitat": "激流漩涡中", "story": "鳞片如同染上朱砂，传闻捕获者将获得火神的祈福。", "bait": "红蚯蚓", "weather": null, "time": null, "min_size": "大型", "base_chance": 0.09}, {"name": "晨曦火鲫", "rarity": "罕见", "habitat": "溪流初光处", "story": "在初升的晨曦中，它的身影犹如燃烧的希望，为新的一天带来温暖。", "bait": "曦光虾", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.12}, {"name": "暮赤梭鱼", "rarity": "罕见", "habitat": "急流狭谷", "story": "神出鬼没，速度快如梭，常被视为暮色中的守护者。", "bait": "赤纹虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.1}, {"name": "绯红小鲤", "rarity": "普通", "habitat": "浅滩边缘", "story": "群游于溪流浅处，它们的红色身影犹如流动的血霞。", "bait": "蚯蚓", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.3}, {"name": "余晖细鳞", "rarity": "普通", "habitat": "流水边的岩石上", "story": "在夕阳的余晖中，它们的鳞片反射出温暖的光芒，仿佛记载了古老传说。", "bait": "小虾", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.28}, {"name": "夕暮浮鱼", "rarity": "普通", "habitat": "水面漂浮处", "story": "优雅地漂于水面，伴随着夕阳余辉，留下荡漾的涟漪。", "bait": "浮萍", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.35}, {"name": "暗浪细鳞", "rarity": "普通", "habitat": "暗流深处", "story": "在暗流中悄然徜徉，每一个转瞬即逝的身影都让人神往。", "bait": "水蚤", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.33}, {"name": "红霞微尖", "rarity": "普通", "habitat": "浅水温区", "story": "体型虽小，却充满斗志，犹如最后一抹灿烂的晚霞。", "bait": "血蚊", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.32}], "story": "暮红流溪以其血色水流和神秘传说而闻名，据说每当血月降临，溪中火神的祝福便会降临于此，预示着命运的转机。"}, {"name": "雷音空渊", "level": 75, "geo_feature": "镶嵌于雷鸣峡谷深处，空渊中雷声震天，水域与天际交响共鸣。", "aquatic_params": {"water_temp": 12.0, "water_salinity": 0.4, "water_depth": 20.0, "water_flow": 0.8}, "fish_list": [{"name": "东风谷早苗", "rarity": "传说", "habitat": "雷域岩洞", "story": "传说中的神社巫女，她的真言引得天雷震响，在空渊中化作神秘鱼形，仅在雷鸣之际现身。", "bait": "雷鸣香炉", "weather": "雷雨", "time": null, "min_size": "巨型", "base_chance": 0.04}, {"name": "八意永琳", "rarity": "罕见", "habitat": "瀑布边际", "story": "掌握禁咒的贤者，其身影于雷电交织之时悄然显现，带来一段尘封的传说。", "bait": "禁咒卷轴", "weather": null, "time": null, "min_size": "大型", "base_chance": 0.1}, {"name": "古明地觉", "rarity": "罕见", "habitat": "雷鸣岩壁", "story": "洞悉尘世秘密的魔法少年，他的出现仿佛与天雷共振，激起一阵神秘回响。", "bait": "星辰碎片", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.12}, {"name": "古明地恋", "rarity": "普通", "habitat": "浅渊边缘", "story": "少女的梦幻与雷霆交织，她的笑容在电闪雷鸣中闪现，带来一缕温柔光影。", "bait": "梦蝶花瓣", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.16}, {"name": "射命丸文", "rarity": "罕见", "habitat": "悬崖水边", "story": "那灵动的身姿在雷鸣与浪涛间穿梭，仿佛在追逐知识与命运的碎片。", "bait": "雷光羽毛", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.13}, {"name": "雾雨魔理沙", "rarity": "普通", "habitat": "急流之中", "story": "勇敢的魔法少女在雷电交织中探寻失落秘宝，她的身影总与电闪雷鸣同现。", "bait": "星尘魔杖", "weather": "雷雨", "time": null, "min_size": "中型", "base_chance": 0.15}, {"name": "十六夜咲夜", "rarity": "普通", "habitat": "悬崖下静水", "story": "时钟与雷鸣同行，她的舞步在水面上留下不可捉摸的时间印记。", "bait": "布偶怀表", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.17}, {"name": "红美铃", "rarity": "罕见", "habitat": "暗流涌动区", "story": "热情洋溢的战士，在雷电照耀下，她的身姿闪耀着战火般炽热的光芒。", "bait": "火焰花瓣", "weather": "雷雨", "time": null, "min_size": "中型", "base_chance": 0.11}, {"name": "帕秋莉", "rarity": "普通", "habitat": "幽暗角落", "story": "智慧的魔法使者，她在雷雨中施展秘法，与雷鸣共鸣，释放出神秘的法力光辉。", "bait": "法力水晶", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.14}, {"name": "铃仙·优雅", "rarity": "普通", "habitat": "水底遗迹", "story": "古老传说于她曼妙身姿中重现，仿佛雷电凝结在湖心，讲述着时空交错的神秘篇章。", "bait": "幻月水晶", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.16}], "story": "雷音空渊深藏于雷鸣峡谷，天雷与水波交响，共同奏响远古神话的回响，凡响之声激发出东方奇幻传说中的神秘力量。"}, {"name": "碧幻幽涧", "level": 80, "geo_feature": "幽谷中的清澈地下涧流", "aquatic_params": {"water_temp": 10.0, "water_salinity": 0.0, "water_depth": 4.0, "water_flow": 0.3}, "fish_list": [{"name": "幽灵水精", "rarity": "传说", "habitat": "幽涧最深处", "story": "相传这是远古水灵的化身，只有在浓雾与暮色交织时才会现身，带来古老的祝福与神秘启示。", "bait": "灵草水珠", "weather": "多云", "time": "夜晚", "min_size": "巨型", "base_chance": 0.007}, {"name": "泪痕蝶鱼", "rarity": "罕见", "habitat": "涧边花丛", "story": "身如蝶翼，游曳于涧边，偶尔闪现出的泪光似乎诉说着久远的悲欢离合。", "bait": "花瓣虾", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.11}, {"name": "青雾裸魟", "rarity": "罕见", "habitat": "薄雾缭绕的浅层", "story": "在迷蒙的雾气中滑行，其身影总与幽蓝奇光相伴，令人难以捕捉。", "bait": "雾隐甲虫", "weather": null, "time": null, "min_size": "大型", "base_chance": 0.1}, {"name": "幻蝶凤尾", "rarity": "罕见", "habitat": "涧水边缘", "story": "其尾部如同彩蝶轻扬，每一次的摆动都似在低语古老传说的秘密。", "bait": "鲜花虫", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.12}, {"name": "魅影银步", "rarity": "罕见", "habitat": "幽暗涧谷", "story": "在水中轻盈游走，宛如银色精灵般闪现，为幽涧增添一抹神秘色彩。", "bait": "细砂蚕", "weather": null, "time": null, "min_size": "大型", "base_chance": 0.09}, {"name": "碧浪细鳞", "rarity": "普通", "habitat": "涧流明澈处", "story": "在阳光照耀下，碧色鳞片映出幽涧往昔的倒影。", "bait": "蚯蚓", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.3}, {"name": "幽涟小虾", "rarity": "普通", "habitat": "岩石聚集处", "story": "成群结队地聚集在岩石间，为幽涧增添了生气勃勃的景象。", "bait": "小虾", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.35}, {"name": "蒸汽跳鱼", "rarity": "普通", "habitat": "温泉激流区", "story": "因微热水温而偶有跃出水面的壮举，带来温暖且奇幻的视觉感受。", "bait": "蝇蛆", "weather": null, "time": null, "min_size": "中型", "base_chance": 0.33}, {"name": "绿鳞溪鲤", "rarity": "普通", "habitat": "涧中幽处", "story": "蓝绿色的鳞片在清澈水中折射出梦幻般的光芒，仿佛在讲述一个被遗忘的传说。", "bait": "水蚤", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.32}, {"name": "纤梦长尾", "rarity": "普通", "habitat": "静谧水面", "story": "修长的身形仿佛梦境中的幽影，每一次轻摆都带来诗意的遐想。", "bait": "微型面团", "weather": null, "time": null, "min_size": "小型", "base_chance": 0.34}], "story": "碧幻幽涧透露出梦幻般的蓝绿色光辉，传说古老水灵曾在此留有痕迹，每当暮色苍然之时，幽涧便会召唤出神秘异兽，为有缘人开启通往未知的神秘旅程。"}]}