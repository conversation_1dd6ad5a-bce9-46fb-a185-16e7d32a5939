from enum import Enum
import random
from typing import Literal, Union
from pydantic import BaseModel, Field
from pydantic_ai.format_as_xml import format_as_xml
from datetime import date, datetime
import math
import logfire
import nonebot_plugin_saa as saa


# 仿照ff14的钓鱼系统进行相关数据类型的定义
class BaseModel(BaseModel):

    def to_xml(self, root_tag: str | None = None, item_tag: str | None = None) -> str:
        if not root_tag:
            root_tag = self.__class__.__name__.lower()
        if not item_tag:
            item_tag = "item"
        return format_as_xml(self, root_tag=root_tag, item_tag=item_tag)


class Weather(str, Enum):
    clear = "晴天"
    cloudy = "多云"
    rain = "雨"
    thunderstorm = "雷雨"
    snow = "雪"
    blizzard = "暴雪"
    fog = "雾"
    enchanted = "魔法迷雾"
    twilight = "暮光"
    aurora = "极光"
    blood_moon = "血月"


class InGameTime(str, Enum):
    dawn = "黎明"
    day = "白天"
    night = "夜晚"


"""
<!DOCTYPE html>
<html>
<body>

<script>

function getEorzeaTime() {
  const E_TIME = 20.5714285714; // 时间粒子, 取自官网
  const NOW_TIME = new Date().getTime(); // 取当前时间
  // (时间粒子 x 当前时间) 的结果 向下取整
  // 取后的值大约3秒一变
  const FLOOR_TIME = new Date().setTime(Math.floor(NOW_TIME * E_TIME));
  const eorzeaTime = new Date(new Date().setTime(FLOOR_TIME)); // 艾欧泽亚的时间对象

  let hh = eorzeaTime.getUTCHours(); // 时
  let mm = eorzeaTime.getUTCMinutes(); // 分

  // 时间格式化
  hh = hh < 10 ? '0' + hh : hh;
  mm = mm < 10 ? '0' + mm : mm;

  return hh + ':' + mm; // 22:58
};

// 使用栗子
setInterval(function() {
  let ET = getEorzeaTime();
  console.log(ET);
}, 1000);

</script>
</body>
</html>
"""


class GameTime(BaseModel):
    real_time: datetime = Field(..., description="真实时间")
    in_game_period: InGameTime = Field(..., description="游戏时间段")
    weather: Weather = Field(Weather.clear, description="天气")
    moon_phase: float = Field(0, description="月相", ge=-1, le=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.moon_phase = math.sin(2 * math.pi * self.real_time.day / 30)
        self.moon_phase = round(self.moon_phase, 2)

    # 根据真实时间，计算游戏时间以及相关天气
    @classmethod
    def from_real_time(cls) -> "GameTime":
        now = datetime.utcnow()

        # 艾欧泽亚时间转换系数
        E_TIME = 20.5714285714

        # 计算游戏内时间
        game_timestamp = int(now.timestamp() * E_TIME * 1000)
        game_time = datetime.fromtimestamp(game_timestamp / 1000)

        # 确定游戏时间段
        hour = game_time.hour
        if 5 <= hour < 7:
            period = InGameTime.dawn
        elif 7 <= hour < 17:
            period = InGameTime.day
        else:
            period = InGameTime.night
        logfire.info(f"当前时间段：{period}")
        # seed = int((game_timestamp / 1000) / 3600 % 189898)
        # 让天气尽量多变（至少10分钟变化一次）
        seed = int((game_timestamp / 1000) / 600)
        logfire.info(f"种子：{seed}")
        weather_rand = (seed * 137) % 100
        logfire.info(f"天气随机数：{weather_rand}")
        if weather_rand < 40:
            weather = Weather.clear
        elif weather_rand < 60:
            weather = Weather.cloudy
        elif weather_rand < 75:
            weather = Weather.rain
        elif weather_rand < 85:
            weather = Weather.fog
        elif weather_rand < 90:
            weather = Weather.snow
        elif weather_rand < 95:
            weather = Weather.thunderstorm
        else:
            # 特殊天气的概率较低
            special_weather_rand = (seed * 137) % 100
            if special_weather_rand < 30:
                weather = Weather.enchanted
            elif special_weather_rand < 60:
                weather = Weather.twilight
            elif special_weather_rand < 80:
                weather = Weather.aurora
            else:
                weather = Weather.blood_moon
                period = InGameTime.night  # 强制修改时间段为夜晚

        return cls(real_time=now, in_game_period=period, weather=weather)

    # 月相2cn
    def moon_phase_to_chinese(self) -> str:
        if self.moon_phase < -0.9:
            return "新月"
        if self.moon_phase < -0.7:
            return "上弦月"
        if self.moon_phase < -0.3:
            return "盈凸月"
        if self.moon_phase < 0.3:
            return "满月"
        if self.moon_phase < 0.7:
            return "亏凸月"
        if self.moon_phase < 0.9:
            return "下弦月"
        return "新月"

    # 获取中文描述，不包括现实时间
    def to_chinese(self) -> str:
        return f"{self.in_game_period.value}，{self.weather.value}，{self.moon_phase_to_chinese()}"


# --------------------
# 钓场(FishingSpot)模块
# --------------------


# 鱼类体型
class FishSize(str, Enum):
    small = "小型"
    medium = "中型"
    large = "大型"
    huge = "巨型"

    def to_value(self) -> int:
        return {
            FishSize.small: 1,
            FishSize.medium: 2,
            FishSize.large: 3,
            FishSize.huge: 4,
        }[self]

    @classmethod
    def random(cls, min_size: "FishSize" = None) -> "FishSize":
        if min_size:
            return random.choice(
                [size for size in FishSize if size.to_value() >= min_size.to_value()]
            )
        return random.choice(list(FishSize))


class Rarity(str, Enum):
    common = "普通"
    uncommon = "罕见"
    rare = "稀有"
    epic = "史诗"
    legendary = "传说"

    # 稀有度转数值
    def to_value(self) -> int:
        return {
            Rarity.common: 1,
            Rarity.uncommon: 2,
            Rarity.rare: 3,
            Rarity.epic: 4,
            Rarity.legendary: 5,
        }[self]

    # to String
    def to_string(self) -> str:
        return {
            Rarity.common: "普通",
            Rarity.uncommon: "罕见",
            Rarity.rare: "稀有",
            Rarity.epic: "史诗",
            Rarity.legendary: "传说",
        }[self]


class AquaticParams(BaseModel):
    water_temp: float = Field(..., description="水温")
    water_salinity: float = Field(..., description="水盐度")
    water_depth: float = Field(..., description="水深")
    water_flow: float = Field(..., description="水流速度")


class FishBaseInfo(BaseModel):
    name: str = Field(
        ..., description="鱼类名称(不一定是鱼，可能是任何水生生物，包括幻想种类)"
    )
    rarity: Rarity = Field(..., description="稀有度")
    habitat: str = Field(..., description="栖息地")
    story: str = Field(..., description="鱼的故事")
    bait: str = Field(
        ..., description="鱼饵，也可能是前置鱼（在以小钓大的情况下，用前置鱼作为鱼饵）"
    )
    weather: Weather | None = Field(
        None, description="天气要求，如果没有则可能在任何天气下出现"
    )
    time: InGameTime | None = Field(
        None, description="出现时间，如果没有则可能在任何时间段出现"
    )
    min_size: FishSize = Field(..., description="这种鱼的最小体型")
    base_chance: float = Field(..., description="基础出现几率")
    next_fish: Union["FishBaseInfo", str, None] = Field(
        None, description="如果存在，下一种鱼的名称或者FishBaseInfo，用于以小钓大"
    )

    async def can_fish_with_game_time(self, game_time: GameTime) -> bool:
        if self.weather and self.weather != game_time.weather:
            return False
        if self.time and self.time != game_time.in_game_period:
            return False
        return True


class FishingSpot(BaseModel):
    name: str = Field(..., description="钓场名称")
    level: int = Field(..., description="钓场等级")
    geo_feature: str = Field(..., description="地理特征")
    aquatic_params: AquaticParams = Field(..., description="水域参数")
    fish_list: list[FishBaseInfo] = Field(..., description="鱼类列表", min_length=10)
    story: str = Field(..., description="钓场故事")


class FishingSpotAgentResponse(BaseModel):
    fishing_spot_list: list[FishingSpot] = Field(..., description="钓场列表")


class BaitRequirement(str, Enum):
    gather = "采集"
    purchase = "购买"
    craft = "制作"


class CraftRecipe(BaseModel):
    craft_materials: dict[str, int] = Field(
        ..., description="制作材料，key为材料名称，value为数量"
    )
    # 产出数量
    output_count: int = Field(..., description="制作产出数量", min=1)


class PurchaseRequirement(BaseModel):
    price: int = Field(..., description="购买价格")


class GatherRequirement(BaseModel):
    location: str = Field(..., description="采集地点")
    # 采集要求
    weather: Weather | None = Field(
        description="采集天气要求，如果没有则可能在任何天气下采集"
    )
    time: InGameTime | None = Field(
        description="采集时间要求，如果没有则可能在任何时间段采集"
    )


SourceConfig = Union[PurchaseRequirement, GatherRequirement, CraftRecipe]


class Bait(BaseModel):
    name: str = Field(..., description="鱼饵名称")
    level: int = Field(..., description="鱼饵等级")
    rarity: Rarity = Field(..., description="鱼饵稀有度")
    description: str = Field(..., description="鱼饵描述")
    source: BaitRequirement = Field(..., description="鱼饵来源")
    source_config: SourceConfig = Field(..., description="鱼饵来源配置")


class BaitAgentResponse(BaseModel):
    bait_list: list[Bait] = Field(..., description="鱼饵列表")


class BaitMeta(BaseModel):
    bait_name: str = Field(..., description="鱼饵名称")
    target_rarity_list: list[Rarity] = Field(..., description="目标鱼的稀有度列表")
    target_fish_list: list[str] = Field(..., description="目标鱼列表")
    target_level_list: list[int] = Field(..., description="目标钓场的等级列表")
    target_fishing_spot_list: list[str] = Field(..., description="目标钓场列表")


class LevelConfig(BaseModel):
    level: int = Field(..., description="等级")
    exp: int = Field(..., description="经验值")
    exp_to_next_level: int = Field(..., description="升级所需经验值")


# 背包结构
class Inventory(BaseModel):
    # 物品名，数量
    items: dict[str, int] = Field({}, description="物品列表，key为物品名，value为数量")


# 鱼实体（背包中的鱼），不包含基本信息
class FishEntity(BaseModel):
    name: str = Field(..., description="鱼类名称")
    size: FishSize = Field(..., description="鱼类体型")

    @classmethod
    def from_fish_base_info(cls, fish_base_info: FishBaseInfo) -> "FishEntity":
        random_size = FishSize.random(fish_base_info.min_size)
        return cls(name=fish_base_info.name, size=random_size)


class FishEntityInventory(BaseModel):
    fish_entity_list: list[FishEntity] = Field(..., description="鱼实体列表")


class ActivityLimit(BaseModel):
    # 各类型行为当日已执行次数字典
    action_counts: dict[str, int] = Field(default_factory=dict, description="行为次数")
    last_reset_date: datetime = Field(datetime.utcnow(), description="上次重置日期")

    def __init__(self, **data):
        super().__init__(**data)
        if self._needs_reset():
            self.reset()

    def check_limit(self, action_key: str, max_allowed: int) -> bool:
        if self._needs_reset():
            self.reset()
        count = self.action_counts.get(action_key, 0)
        return count < max_allowed

    def _needs_reset(self):
        current_date = date.today()
        return current_date > self.last_reset_date.date()

    def reset(self):
        self.action_counts = {}
        self.last_reset_date = datetime.utcnow()

    def increase_action_count(self, action_key: str):
        self.action_counts.setdefault(action_key, 0)
        self.action_counts[action_key] += 1
        return self.action_counts[action_key]


class ActionPolicy:
    # 每日限制：采集3次，狩猎3次，传说级别鱼抓捕1次，签到1次
    DAILY_LIMITS = {"gather": 3, "hunting": 3, "legendary_catch": 1, "sign_in": 1}
    ENERGY_COST_MAP = {"_default_": 5, "deep_dive_fish": 15}


class UserInfo(BaseModel):
    user_id: int
    user_name: str = Field("默认用户名", description="用户名")
    level: int = Field(1, description="等级")
    exp: int = Field(0, description="经验值")
    # 鱼饵包
    bait_inventory: Inventory = Field(Inventory(items={}), description="鱼饵包")
    # 鱼包
    fish_inventory: FishEntityInventory = Field(
        FishEntityInventory(fish_entity_list=[]), description="鱼包"
    )
    # 精力值
    energy: int = Field(100, description="精力值")
    # 鱼饵图鉴
    bait_book: dict[str, bool] = Field({}, description="鱼饵图鉴")
    # 鱼图鉴
    fish_book: dict[str, bool] = Field({}, description="鱼图鉴")
    # 活动限制
    activity_limits: ActivityLimit = Field(
        default_factory=ActivityLimit, description="活动限制"
    )
    # 目前所在钓场
    current_fishing_spot: str | None = Field(None, description="当前所在钓场")
    # 目前使用的鱼饵
    current_bait: str | None = Field(None, description="当前使用的鱼饵")

    def __init__(self, **data):
        super().__init__(**data)
        self.update_bait_book()
        self.update_fish_book()
        if self.current_bait:
            count = self.bait_inventory.items.get(self.current_bait, 0)
            if count <= 0:
                self.current_bait = None

    # 更新鱼饵图鉴
    def update_bait_book(self):
        for bait_name in self.bait_inventory.items.keys():
            self.bait_book[bait_name] = True

    # 更新鱼图鉴
    def update_fish_book(self):
        for fish in self.fish_inventory.fish_entity_list:
            self.fish_book[fish.name] = True

    # 检查是否有足够的精力
    def check_energy(self, action_key: str) -> bool:
        return self.energy >= ActionPolicy.ENERGY_COST_MAP.get(
            action_key, ActionPolicy.ENERGY_COST_MAP["_default_"]
        )

    # 检查是否可采集
    def check_gather_limit(self) -> bool:
        return self.activity_limits.check_limit(
            "gather", ActionPolicy.DAILY_LIMITS["gather"]
        )

    # 获取每日可采集次数
    def get_gather_limit(self) -> int:
        return ActionPolicy.DAILY_LIMITS["gather"]

    # 获取已采集次数
    def get_gather_count(self) -> int:
        return self.activity_limits.action_counts.get("gather", 0)

    def increase_action_count(self, action_key: str):
        self.activity_limits.increase_action_count(action_key)

    def increase_gather_count(self):
        self.increase_action_count("gather")

    def check_limit(self, action_key: str, max_allowed: int) -> bool:
        return self.activity_limits.check_limit(action_key, max_allowed)

    # 签到
    def sign_in(self):
        if self.check_limit("sign_in", ActionPolicy.DAILY_LIMITS["sign_in"]):
            self.increase_action_count("sign_in")
            return True
        return False

    def get_fish_into_inventory(self, fish: FishEntity):
        self.fish_inventory.fish_entity_list.append(fish)

    # 获取鱼饵图鉴
    def get_bait_book(self) -> list[str]:
        return list(self.bait_book.keys())

    # 获取鱼图鉴
    def get_fish_book(self) -> list[str]:
        return list(self.fish_book.keys())

    def is_sign_in_today(self) -> bool:
        return self.activity_limits.action_counts.get("sign_in", 0) > 0


class FishingState(BaseModel):
    user_id: int | str
    group_id: int | str
    target_fish: FishBaseInfo | None = Field(None, description="目标鱼")
    current_fishing_spot: str | None = Field(None, description="当前钓场")
    current_bait: str | None = Field(None, description="当前鱼饵")
    last_action_time: datetime | None = Field(None, description="上次行动时间")
    # 本次操作允许的最大时间间隔，如果超过了时间没有进行相应的操作，则本次钓鱼结束（失败）
    max_action_interval: int = Field(60, description="最大行动间隔")
    message: str | None = Field(None, description="消息")
    base_chance: float = Field(0, description="基础几率")  # 通过一系列操作来提高几率
    # 根据鱼的稀有度决定最多出现多少事件需要处理
    max_event_count: int = Field(1, description="最大事件数")
    current_event_count: int = Field(0, description="当前事件数")
    # 当前操作
    current_action: str | None = Field(None, description="当前操作")
    # 当前所需的正确操作
    current_correct_action: str | None = Field(None, description="当前正确操作")
    # 是否已经上钩
    is_hooked: bool = Field(False, description="是否已经上钩")

    async def send_message(self, message: str | None = None):
        logfire.info(f"发送消息：{message or self.message}")
        msg = saa.Mention(self.user_id) + saa.Text(message or self.message)
        await msg.send_to(saa.TargetQQGroup(group_id=self.group_id))


async def test():
    from pydantic_ai import Agent
    from pydantic_ai.models.openai import OpenAIModel
    from openai import AsyncOpenAI
    import logfire

    logfire.configure(send_to_logfire="if-token-present", scrubbing=False)

    class Config:
        openai_model = "o3-mini"
        openai_api_key = "sk-RNpcm6cVPGlq7cfaF9352f4bD6B04f5190Fb2c504977Bd14"
        openai_base_url = "https://oneapi.hy.nyanners.moe/v1"

    config = Config()
    openai_client = AsyncOpenAI(
        api_key=config.openai_api_key, base_url=config.openai_base_url
    )

    model = OpenAIModel(config.openai_model, openai_client=openai_client)
    system_prompt = r"""
你是一位资深的虚拟钓场设计师，正在协助开发一款幻想风格的钓鱼RPG。请遵循以下规范生成内容:
    
━━━〔世界观设定〕━━━  
• 基调参考《最终幻想14》的水域生态及传说体系
 ⚠️每个区域应有独特的奇幻元素（如被魔法污染的河流、浮空岛湖泊）

示例搭配： 
-珊瑚礁海域 → 常伴随暴风雨天气，适合深海巨物  


━━━〔内容审核规则〕━━━  
请确保生成内容符合以下校验：
✅ 稀有度分布律 
   ● {Rarity.legendary}: ≤1种/钓场
   ● {Rarity.uncommon}:占比30-60%
 
⛔️禁止出现:  
• 鱼类描述中包含现实存在的品牌或人物名称  
••同一区域内地理特征与气候严重冲突（如「冰川河流」搭配热带鱼）

━━━〔创意提示〕━━━
参考这些传说级鱼类故事，自由发挥，创造独特的鱼类传说：
¤被古代文明封印的水之精灵 
¤以渔船残骸为巢的变异巨兽  
¤只在双月蚀交汇时现身的幻影鱼王

生成[3-5]个风格各异的钓场
""".strip()

    agent = Agent(
        model,
        result_type=FishingSpotAgentResponse,
        system_prompt=system_prompt,
        retries=5,
    )
    import json
    from pathlib import Path

    async def gen_fishing_spot(user_prompt: str):
        r = await agent.run(user_prompt=user_prompt)
        data = r.data
        return data
        json_data = data.model_dump_json()
        print(json_data)
        Path("fishing_spot.json").write_text(json_data, encoding="utf-8")

    def load_fishing_spot():
        json_data = Path("fishing_spot.json").read_text(encoding="utf-8")
        data = FishingSpotAgentResponse.model_validate_json(json_data)
        return data

    data = load_fishing_spot()
    bait_list = [
        fish.bait for spot in data.fishing_spot_list for fish in spot.fish_list
    ]
    # 获取(level, name, bait)列表
    level_name_bait_list = [
        (spot.level, spot.name, spot.story) for spot in data.fishing_spot_list
    ]
    level_name_bait_list.sort(key=lambda x: x[0])
    #    user_prompt = f"""
    # 目前已有的钓场信息：
    # {format_as_xml(level_name_bait_list, root_tag="current_fishing_spot_list", item_tag="fishing_spot")}
    # 用户要求：生成三个新的钓场，等级和名称不得与现有钓场重复，注意选择合适的等级
    #    """
    # new_data = await gen_fishing_spot(user_prompt)
    # print(new_data.to_xml())

    def merge(
        cls, fishing_spot_response: FishingSpotAgentResponse
    ) -> FishingSpotAgentResponse:
        return cls(
            fishing_spot_list=data.fishing_spot_list
            + fishing_spot_response.fishing_spot_list
        )

    # new_data = merge(FishingSpotAgentResponse, new_data)
    # print(new_data.to_xml())
    # new_data.fishing_spot_list.sort(key=lambda x: x.level)
    # 保存新的钓场信息
    # json_data = new_data.model_dump_json()
    # Path("fishing_spot.json").write_text(json_data, encoding="utf-8")

    # 获取鱼饵的列表
    def get_bait_list(data: FishingSpotAgentResponse):
        return [fish.bait for spot in data.fishing_spot_list for fish in spot.fish_list]

    logfire.info(f"未去重的鱼饵数目：{len(get_bait_list(data))}")
    bait_list = list(set(get_bait_list(data)))
    logfire.info(f"去重后的鱼饵数目：{len(bait_list)}")

    # 鱼饵信息生成专用agent
    bait_model = OpenAIModel(config.openai_model, openai_client=openai_client)
    bail_system_prompt = r"""
你是一位幻想钓鱼游戏的物件设计师，负责创作具有深度的魔法鱼饵。请按步骤思考：

━━━【世界观建构指南】━━━  
每个鱼饵需有独特起源故事（参照FF14传说），必须至少包含：
☑ 材料来源的历史事件（如星灾、众神战争）
☑ 与某种族/文化的关联（例如：蜥蜴人萨满的仪式用饵）
注意结合鱼饵对应的鱼的稀有度和鱼名，创作合理的鱼饵描述，不要什么鱼饵都有传说级别的故事。
如果你的描述里体现了采集的条件等内容，那么对应的配置也需要体现
→样例结构←
**「虚空鲸脂」**
"采集自漂浮在次元裂隙中的幻兽尸体，炼金学派将其加工成接触深渊生物的媒介。据说过量使用会导致垂钓者听见旧日支配者的低语。"
"""
    bait_agent = Agent(
        bait_model,
        result_type=BaitAgentResponse,
        system_prompt=bail_system_prompt,
        retries=5,
    )

    # 根据钓场数据获得list[BaitMeta]
    def get_bait_meta_list(data: FishingSpotAgentResponse):
        bait_name_list = list(set(get_bait_list(data)))
        bait_meta_dict = {}

        for spot in data.fishing_spot_list:
            for fish in spot.fish_list:
                if fish.bait not in bait_meta_dict:
                    bait_meta_dict[fish.bait] = BaitMeta(
                        bait_name=fish.bait,
                        target_rarity_list=[fish.rarity],
                        target_level_list=[spot.level],
                        target_fishing_spot_list=[spot.name],
                        target_fish_list=[fish.name],
                    )
                else:
                    bait_meta_dict[fish.bait].target_rarity_list.append(fish.rarity)
                    bait_meta_dict[fish.bait].target_level_list.append(spot.level)
                    bait_meta_dict[fish.bait].target_fishing_spot_list.append(spot.name)
                    bait_meta_dict[fish.bait].target_fish_list.append(fish.name)
        list_data = list(bait_meta_dict.values())
        list_data.sort(
            key=lambda x: (
                x.target_level_list[0],
                x.target_rarity_list[0],
                x.target_fishing_spot_list[0],
            )
        )

        return list_data

    bait_meta_list = get_bait_meta_list(data)
    batch_size = 25
    total_bait = []
    for i in range(0, len(bait_meta_list), batch_size):
        batch = bait_meta_list[i : i + batch_size]
        message_history = []
        realy_len = len(batch)
        user_prompt = f"""
        请生成以下{realy_len}种鱼饵的描述：
        {format_as_xml(batch, root_tag="bait_meta_list", item_tag="bait_meta")}
        """
        r = await bait_agent.run(
            user_prompt=user_prompt, message_history=message_history
        )
        data = r.data
        message_history = r.all_messages()
        while len(data.bait_list) != realy_len:
            user_prompt = f"""
            生成的鱼饵数量与要求不符：我们需要{realy_len}种鱼饵，但是只生成了{len(data.bait_list)}种鱼饵，请重新生成
            源数据：
            {format_as_xml(batch, root_tag="bait_meta_list", item_tag="bait_meta")}
            """
            r = await bait_agent.run(
                user_prompt=user_prompt, message_history=message_history
            )
            data = r.data
            message_history = r.all_messages()

        # 保存生成的鱼饵信息
        json_data = data.model_dump_json()
        Path(f"bait_{i}.json").write_text(json_data, encoding="utf-8")
        total_bait.extend(data.bait_list)

    # 保存所有生成的鱼饵信息
    json_data = BaitAgentResponse(bait_list=total_bait).model_dump_json()
    Path("bait.json").write_text(json_data, encoding="utf-8")

    return data


if __name__ == "__main__":
    import asyncio

    asyncio.run(test())
