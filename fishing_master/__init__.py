from nonebot import require
from pydantic_graph import End

require("nonebot_plugin_chatrecorder")
require("nonebot_plugin_userinfo")
require("nonebot_plugin_session")
require("nonebot_plugin_session_orm")
require("nonebot_plugin_orm")
import datetime
import re
from nonebot import get_plugin_config, logger, require
import urllib
from nonebot_plugin_saa import enable_auto_select_bot

enable_auto_select_bot()
from nonebot_plugin_alconna import (
    Alconna,
    AlconnaMatches,
    Args,
    Arparma,
    Option,
    Subcommand,
    on_alconna,
    Match,
    MsgId,
)
import logfire
from nonebot.adapters.onebot.v11.message import MessageSegment
import json, re
from io import BytesIO
from nonebot_plugin_alconna.builtins.extensions import ReplyRecordExtension
from nonebot_plugin_session import Session, extract_session

from nonebot.plugin import PluginMetadata
from nonebot.adapters import Bot, Event
from nonebot.params import Depends


from asyncio import gather
from zipfile import ZipFile
import datetime

from .config import Config
from pathlib import Path
from .db import (
    daily_sign_in,
    get_my_bait_str,
    get_my_bait_detail_str,
    get_my_bait_book_str,
    get_my_fish_book_str,
    get_my_fishing_spot_str,
    get_or_create_user,
    get_simple_fishing_spot_str,
    goto_fishing_spot,
    my_info,
    my_location,
    set_my_current_bait,
    can_fish,
)
from .core import (
    StartFishing,
    FishDecision,
    CastLineWait,
    CheckFish,
    ReelingCheck,
    FishResult,
    fishing_graph,
    FishFail,
    global_fishing_state,
    global_node_info,
    global_history_info,
    need_input_nodes,
)
from .model import FishingState

__plugin_meta__ = PluginMetadata(
    name="fishing_master",
    description="",
    usage="",
    config=Config,
)

config = get_plugin_config(Config)


fish_help = """
钓鱼大师 v0.1
===================
命令概述:
• /fish sign                  : 进行每日签到以领取奖励。
• /fish mybait                : 查看你的鱼饵收藏，附加 '--detail' 参数可获得详细信息。
• /fish bait                  : 查看鱼饵图鉴。
• /fish fish                  : 查看鱼类图鉴。
• /fish location              : 列出所有可用的钓鱼地点。
• /fish goto [spot_name]      : 导航至指定的钓场，如需查看可用钓场，请先使用 '/fish location'。
• /fish set_bait [bait_name]  : 设置你的默认鱼饵。
• /fish start [bait_name?] [spot_name?] : 开始钓鱼
• /fish i [action?]           : 进行钓鱼交互操作。
• /fish my                   : 获取你的个人信息。
• /fish help                 : 显示此帮助信息。
""".strip()

fish_command = on_alconna(
    Alconna(
        "fish",
        Option("--detail|-d|detail", Args["detail?", str]),
        Subcommand(
            "sign",
            alias=["签到"],
        ),
        Subcommand(
            "mybait",
            alias=["我的鱼饵"],
        ),
        # 鱼饵图鉴
        Subcommand(
            "bait",
            alias=["鱼饵图鉴"],
        ),
        # 鱼类图鉴
        Subcommand(
            "fish",
            alias=["鱼类图鉴"],
        ),
        # 获取可垂钓的地点
        Subcommand(
            "location",
            alias=["地点", "钓场"],
        ),
        Subcommand(
            "goto",
            Args["spot_name?", str],
            alias=["前往"],
        ),
        # 设置鱼饵
        Subcommand(
            "set_bait",
            Args["bait_name", str],
            alias=["设置鱼饵"],
        ),
        Subcommand(
            "start",
            Args["bait_name?", str],
            Args["spot_name?", str],
            alias=["开始", "垂钓", "钓鱼", "fishing"],
        ),
        Subcommand(
            "my",
            alias=["我的", "my", "me", "我的信息"],
        ),
        Subcommand(
            "help",
            alias=["帮助", "h"],
        ),
        # 一个专门用于钓鱼交互的子命令，用于处理动态的钓鱼交互
        Subcommand(
            "interact",
            Args["action?", str],
            alias=["i", "交互"],
        ),
    ),
    aliases=["fishing_master", "/fish", "/fishing"],
)


@fish_command.assign("sign")
async def sign(event: Event):
    user_id = event.user_id
    try:
        await fish_command.send(await daily_sign_in(user_id))
    except Exception as e:
        await fish_command.send(str(e))


@fish_command.assign("mybait")
async def mybait(event: Event, result: Arparma = AlconnaMatches()):
    user_id = event.user_id
    print(result)
    try:
        if not result.find("detail"):
            logfire.info("未传入detail参数，返回简略信息")
            await fish_command.send(await get_my_bait_str(user_id))
        else:
            logfire.info("传入detail参数，返回详细信息")
            await fish_command.send(await get_my_bait_detail_str(user_id))
    except Exception as e:
        await fish_command.send(str(e))


@fish_command.assign("bait")
async def bait(event: Event):
    user_id = event.user_id
    try:
        await fish_command.send(await get_my_bait_book_str(user_id))
    except Exception as e:
        await fish_command.send(str(e))


@fish_command.assign("fish")
async def fish(event: Event):
    user_id = event.user_id
    try:
        await fish_command.send(await get_my_fish_book_str(user_id))
    except Exception as e:
        await fish_command.send(str(e))


@fish_command.assign("location")
async def location(event: Event):
    user_id = event.user_id
    try:
        await fish_command.send(await get_my_fishing_spot_str(user_id))
    except Exception as e:
        await fish_command.send(str(e))


@fish_command.assign("goto")
async def goto(event: Event, spot_name: Match[str]):
    user_id = event.user_id
    if not spot_name.available:
        await fish_command.finish(
            f"请提供钓场名称，您目前可以前往的钓场有：\n{await get_simple_fishing_spot_str(user_id)}\n如有需要，请使用`/fish location`查看详细信息"
        )
    try:
        await fish_command.send(await goto_fishing_spot(user_id, spot_name.result))
    except Exception as e:
        await fish_command.send(str(e))


@fish_command.assign("set_bait")
async def set_bait(event: Event, bait_name: Match[str]):
    user_id = event.user_id
    if not bait_name.available:
        await fish_command.finish("请提供鱼饵名称")
    try:
        await fish_command.send(await set_my_current_bait(user_id, bait_name.result))
    except Exception as e:
        await fish_command.send(str(e))


@fish_command.assign("my")
async def my(event: Event):
    user_id = event.user_id
    try:
        await fish_command.send(await my_info(user_id))
    except Exception as e:
        await fish_command.send(str(e))


@fish_command.assign("help")
async def help(event: Event):
    await fish_command.finish(fish_help)


@fish_command.assign("start")
async def start(event: Event, bait_name_m: Match[str], spot_name_m: Match[str]):
    user_id = event.user_id
    group_id = event.group_id
    key = f"{user_id}_{group_id}"
    if key in global_fishing_state:
        await fish_command.finish("您正在垂钓的过程中，无法开始新的钓鱼任务")
    bait_name, spot_name = None, None
    if bait_name_m.available:
        bait_name = bait_name_m.result
    if spot_name_m.available:
        spot_name = spot_name_m.result
    try:
        data = await can_fish(user_id, bait_name, spot_name)
        if not data:
            await fish_command.send(f"现在还不是用该鱼饵钓鱼的好时候...")
        else:
            import random

            cur_fish = random.choice(data)
            user = await get_or_create_user(user_id)
            logfire.info(f"当前用户信息：{user}")
            state = FishingState(
                user_id=user_id,
                group_id=group_id,
                target_fish=cur_fish,
                current_bait=user.current_bait if bait_name is None else bait_name,
                current_fishing_spot=(
                    user.current_fishing_spot if spot_name is None else spot_name
                ),
            )
            logfire.info(f"开始钓鱼，当前状态：{state}")
            global_fishing_state[key] = state
            global_node_info[key] = StartFishing()
            global_history_info[key] = []
            while not isinstance(global_node_info[key], tuple(need_input_nodes)):
                global_node_info[key] = await fishing_graph.next(
                    global_node_info[key],
                    history=global_history_info[key],
                    state=global_fishing_state[key],
                )
                if isinstance(global_node_info[key], tuple(need_input_nodes)):
                    logfire.info(
                        f"当前节点{global_node_info[key].__class__.__name__}需要等待用户输入，结束循环"
                    )
                    break
                if isinstance(global_node_info[key], End):
                    logfire.info(f"钓鱼结束")
                    del global_fishing_state[key]
                    del global_node_info[key]
                    del global_history_info[key]
                    break
    except Exception as e:
        await fish_command.send(str(e))


@fish_command.assign("interact")
async def interact(event: Event, action: Match[str]):
    user_id = event.user_id
    group_id = event.group_id
    key = f"{user_id}_{group_id}"
    if key not in global_fishing_state:
        await fish_command.finish("您没有正在进行的钓鱼任务")
    if not action.available:
        await fish_command.finish("请提供交互操作")
    try:
        global_fishing_state[key].current_action = action.result
        logfire.info(
            f"当前用户交互操作：{action.result}，准备进入节点：{global_node_info[key].__class__.__name__}"
        )
        while True:
            global_node_info[key] = await fishing_graph.next(
                global_node_info[key],
                history=global_history_info[key],
                state=global_fishing_state[key],
            )
            if isinstance(global_node_info[key], tuple(need_input_nodes)):
                logfire.info(
                    f"当前节点{global_node_info[key].__class__.__name__}需要等待用户输入"
                )
                break
            if isinstance(global_node_info[key], End):
                logfire.info(f"钓鱼结束")
                del global_fishing_state[key]
                del global_node_info[key]
                del global_history_info[key]
                break
    except Exception as e:
        await fish_command.send(str(e))
