from dataclasses import dataclass
import logfire
from pydantic_graph import BaseNode, Edge, End, Graph, GraphRunContext, HistoryStep
from pydantic import BaseModel
from . import db
from .model import FishingState
from datetime import datetime
import random
import asyncio
from typing import Optional, Union

global_fishing_state: dict[str, FishingState] = {}


@dataclass
class StartFishing(BaseNode[FishingState]):
    async def run(self, ctx: GraphRunContext[FishingState]) -> "FishDecision":
        ctx.state.last_action_time = datetime.now()
        ctx.state.message = f"已在{ctx.state.current_fishing_spot}开始钓鱼，使用鱼饵{ctx.state.current_bait}。"
        # ctx.state.max_event_count = ctx.state.target_fish.rarity.to_value()
        ctx.state.max_event_count = random.randint(
            2, max(2, ctx.state.target_fish.rarity.to_value())
        )
        await ctx.state.send_message()
        return FishDecision()


@dataclass
class CastLineWait(BaseNode[FishingState]):
    async def run(self, ctx: GraphRunContext[FishingState]) -> "CheckFish":
        # 随机等待一段时间
        wait_time = random.randint(1, 10)
        logfire.info(f"等待{wait_time}秒以判断是否有鱼上钩。")
        await asyncio.sleep(wait_time)
        return CheckFish()


# 失败节点
@dataclass
class FishFail(BaseNode[FishingState]):
    async def run(self, ctx: GraphRunContext[FishingState]) -> End:
        # 随机判断是否失去鱼饵
        if random.random() > 0.5:
            check = True
        else:
            check = False
        # 根据是否失去鱼饵返回不同的消息
        if check:
            ctx.state.message = "本次钓鱼失败，鱼饵被鱼吃掉了。"
        else:
            ctx.state.message = "本次钓鱼失败，好消息是鱼饵还在。"
        await ctx.state.send_message()
        return End(data=None)


@dataclass
class ReelingPhase(BaseNode[FishingState]):
    async def run(self, ctx: GraphRunContext[FishingState]) -> "FishDecision":
        ctx.state.is_hooked = True  # 只给一次上钩的机会
        ctx.state.message = "正在收杆..."
        await ctx.state.send_message()
        return FishDecision()


# 摇摆阶段
@dataclass
class SwingingPhase(BaseNode[FishingState]):
    async def run(self, ctx: GraphRunContext[FishingState]) -> "FishDecision":
        ctx.state.message = "操作正确！"
        await ctx.state.send_message()
        return FishDecision()


@dataclass
class SwingingCheck(BaseNode[FishingState]):
    async def run(
        self, ctx: GraphRunContext[FishingState]
    ) -> Union[SwingingPhase, End]:
        logfire.info(f"当前位于节点：{self.__class__.__name__}")
        if ctx.state.current_correct_action != ctx.state.current_action:
            logfire.info(
                f"操作错误，正确操作是`/fish i {ctx.state.current_correct_action}`。"
            )
            ctx.state.message = (
                f"操作错误，正确操作是`/fish i {ctx.state.current_correct_action}`。"
            )
            await ctx.state.send_message()
            return SwingingCheck()

        if (
            datetime.now() - ctx.state.last_action_time
        ).seconds > ctx.state.max_action_interval:
            logfire.info(f"操作超时，鱼逃走了。")
            ctx.state.message = "操作超时，鱼逃走了。"
            await ctx.state.send_message()
            return End(data=None)

        logfire.info(f"操作正确！")
        return SwingingPhase()


# 摇摆阶段
@dataclass
class SwingingWait(BaseNode[FishingState]):
    async def run(self, ctx: GraphRunContext[FishingState]) -> SwingingCheck:
        # 等待时间
        wait_time = random.randint(1, 5)
        logfire.info(f"摇杆等待{wait_time}秒。")
        await asyncio.sleep(wait_time)
        # 随机一个动作，在鱼往左摆还是往右摆时，需要相应的操作
        actions = {
            "loose": "鱼快速向前游动，需要松杆（/fish i loose）。",
            "reel": "鱼看起来有些疲惫，可以收杆（/fish i reel）。",
            "left": "鱼向右摆动，需要向左摆杆（/fish i left）。",
            "right": "鱼向左摆动，需要向右摆杆（/fish i right）。",
        }
        action = random.choice(list(actions.keys()))
        ctx.state.current_correct_action = action
        # 设置允许的操作时间
        ctx.state.last_action_time = datetime.now()
        ctx.state.max_action_interval = random.randint(
            10, 20 - ctx.state.target_fish.rarity.to_value()
        )
        ctx.state.message = (
            f"{actions[action]}请在{ctx.state.max_action_interval}秒内操作。"
        )
        await ctx.state.send_message()
        return SwingingCheck()


@dataclass
class FishDecision(BaseNode[FishingState]):
    async def run(
        self, ctx: GraphRunContext[FishingState]
    ) -> Union[CastLineWait, SwingingWait, "FishResult"]:
        if ctx.state.current_event_count >= ctx.state.max_event_count:
            logfire.info(f"事件次数达到上限，结束钓鱼。")
            return FishResult()
        if not ctx.state.is_hooked:
            logfire.info(f"还未上钩，等待鱼上钩。")
            ctx.state.current_event_count += 1
            return CastLineWait()
        else:
            logfire.info(f"已经上钩，准备摇杆。")
            ctx.state.current_event_count += 1
            return SwingingWait()


@dataclass
class ReelingCheck(BaseNode[FishingState]):
    async def run(self, ctx: GraphRunContext[FishingState]) -> Union[ReelingPhase, End, 'ReelingCheck']:
        logfire.info(f"当前位于节点：{self.__class__.__name__}")
        if ctx.state.current_correct_action == "reel":
            if not ctx.state.current_action == "reel":
                logfire.info(f"操作错误，正确操作是`/fish i reel`。")
                ctx.state.message = f"操作错误，正确操作是`/fish i reel`。"
                await ctx.state.send_message()
                return ReelingCheck()
            if (
                datetime.now() - ctx.state.last_action_time
            ).seconds > ctx.state.max_action_interval:
                logfire.info(f"收杆超时，鱼逃走了。")
                ctx.state.message = "收杆超时，鱼逃走了。"
                await ctx.state.send_message()
                return End(data=None)
            else:
                logfire.info(f"收杆成功！")
                return ReelingPhase()
        else:
            raise Exception("错误的操作。")


@dataclass
class CheckFish(BaseNode[FishingState]):
    async def run(self, ctx: GraphRunContext[FishingState]) -> Union[ReelingCheck, End, 'CheckFish']:
        p = random.random()
        if p > 0.9:
            logfire.info(f"没有鱼上钩。")
            ctx.state.message = "没有鱼上钩。"
            return CheckFish()
        elif p < 0.05:
            logfire.info(f"鱼逃走了！")
            ctx.state.message = "鱼逃走了！"
            await ctx.state.send_message()
            return End(data=None)
        else:
            # 设置允许的收杆时间
            ctx.state.last_action_time = datetime.now()
            ctx.state.max_action_interval = random.randint(
                8, 20 - ctx.state.target_fish.rarity.to_value()
            )
            ctx.state.current_correct_action = "reel"
            ctx.state.message = f"有鱼上钩了！请在{ctx.state.max_action_interval}秒内输入`/fish i reel`收杆。"
            await ctx.state.send_message()
            return ReelingCheck()


@dataclass
class FishResult(BaseNode[FishingState]):
    async def run(self, ctx: GraphRunContext[FishingState]) -> End:
        resp = await db.add_fish_to_user(ctx.state.user_id, ctx.state.target_fish)
        if resp:
            ctx.state.message = f"恭喜！鱼儿上钩了，是一条{ctx.state.target_fish.name}，体型{resp.size.value}。"
        else:
            ctx.state.message = f"恭喜！鱼儿上钩了，是一条{ctx.state.target_fish.name}，但由于未知原因，您并未获得该鱼。"
        ctx.state.message += f"\n鱼类故事：{ctx.state.target_fish.story}"
        resp = await db.get_exp_by_fish(
            ctx.state.user_id, ctx.state.target_fish, ctx.state.current_fishing_spot
        )
        ctx.state.message += f"\n{resp}"
        await ctx.state.send_message()
        await db.consume_bait(ctx.state.user_id, ctx.state.current_bait)
        return End(data=None)


fishing_graph = Graph(
    nodes=[
        StartFishing,
        FishDecision,
        CastLineWait,
        CheckFish,
        ReelingCheck,
        ReelingPhase,
        FishResult,
        FishFail,
        SwingingCheck,
        SwingingPhase,
        SwingingWait,
    ],
    state_type=FishingState,
)

global_node_info: dict[
    str,
    StartFishing
    | FishDecision
    | CastLineWait
    | CheckFish
    | ReelingCheck
    | ReelingPhase
    | FishResult
    | FishFail
    | SwingingCheck
    | SwingingPhase
    | SwingingWait,
] = {}

global_history_info: dict[str, list[HistoryStep[FishingState, None]]] = {}
need_input_nodes = [ReelingCheck, SwingingCheck]


def export_mermaid():
    return fishing_graph.mermaid_save("fishing_master.jpg")

if __name__ == "__main__":
    fishing_graph.mermaid_save("fishing_master.jpg")
