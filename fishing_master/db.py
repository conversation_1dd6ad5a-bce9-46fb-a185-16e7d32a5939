from typing import List
from .model import (
    Bait,
    BaitAgentResponse,
    BaitMeta,
    FishingSpotAgentResponse,
    FishBaseInfo,
    FishingSpot,
    GatherRequirement,
    UserInfo,
    GameTime,
    FishEntity,
)
from pathlib import Path
from pymongo import MongoClient
import logfire

logfire.configure(send_to_logfire="if-token-present", scrubbing=False)

data_dir = Path(__file__).parent / "data"

client = MongoClient("mongodb://100.107.90.115:27017/")
db = client["fishing_master"]
user_collection = db["user"]


bait_data_path = data_dir / "bait.json"
fishing_spot_data_path = data_dir / "fishing_spot.json"
fishing_spot_agent_response = FishingSpotAgentResponse.model_validate_json(
    fishing_spot_data_path.read_text(encoding="utf-8")
)
bait_agent_response = BaitAgentResponse.model_validate_json(
    bait_data_path.read_text(encoding="utf-8")
)
bait_list = bait_agent_response.bait_list
fishing_spot_list = fishing_spot_agent_response.fishing_spot_list
bait_dict = {bait.name: bait for bait in bait_list}


def get_bait_list(data: FishingSpotAgentResponse):
    return [fish.bait for spot in data.fishing_spot_list for fish in spot.fish_list]


# 鱼饵的meta数据：鱼饵可以钓什么鱼
def get_bait_meta_dict(data: FishingSpotAgentResponse) -> dict[str, BaitMeta]:
    bait_meta_dict = {}

    for spot in data.fishing_spot_list:
        for fish in spot.fish_list:
            if fish.bait not in bait_meta_dict:
                bait_meta_dict[fish.bait] = BaitMeta(
                    bait_name=fish.bait,
                    target_rarity_list=[fish.rarity],
                    target_level_list=[spot.level],
                    target_fishing_spot_list=[spot.name],
                    target_fish_list=[fish.name],
                )
            else:
                bait_meta_dict[fish.bait].target_rarity_list.append(fish.rarity)
                bait_meta_dict[fish.bait].target_level_list.append(spot.level)
                bait_meta_dict[fish.bait].target_fishing_spot_list.append(spot.name)
                bait_meta_dict[fish.bait].target_fish_list.append(fish.name)
    return bait_meta_dict


bait_meta_dict = get_bait_meta_dict(fishing_spot_agent_response)


async def what_fish_can_bait_get(bait_name: str) -> list[FishBaseInfo]:
    return [
        fish
        for spot in fishing_spot_list
        for fish in spot.fish_list
        if fish.bait == bait_name
    ]


# 钓场列表显示
def get_fishing_spot_overview() -> str:
    # 返回钓场的概况，包括钓场名称、等级、鱼的数量、钓场的描述
    overview = ""
    for spot in fishing_spot_list:
        overview += f"{spot.name}({spot.level}级)：{spot.story}\n"
    return overview


# 获取钓场名
def get_fishing_spot_name_list() -> list[str]:
    return [spot.name for spot in fishing_spot_list]


# 根据传入的钓场名，获取可能采集到的鱼饵的列表
def get_bait_list_by_fishing_spot_name(fishing_spot_name: str) -> list[Bait]:
    if fishing_spot_name not in get_fishing_spot_name_list():
        raise ValueError(f"没有这个地方哦：{fishing_spot_name}")
    global bait_list
    data = []
    for bait in bait_list:
        if isinstance(bait.source_config, GatherRequirement):
            location = bait.source_config.location
            if fishing_spot_name in location:
                data.append(bait)
    return data


# 根据鱼饵的稀有度，用户的等级和钓场的等级，计算用户可能获得什么鱼饵，以及可能获得的鱼的稀有度
async def random_get_bait_by_fishing_spot_name(
    player_level: int,
    fishing_spot_name: str,
):
    if fishing_spot_name not in get_fishing_spot_name_list():
        raise ValueError(f"没有这个地方哦：{fishing_spot_name}")

    fishing_spot = next(
        spot for spot in fishing_spot_list if spot.name == fishing_spot_name
    )

    fishing_spot_level = fishing_spot.level

    # 根据钓场等级，鱼饵稀有度，来决定用户可能获得的鱼饵
    maybe_bait_list = await get_bait_list_by_fishing_spot_name(fishing_spot_name)

    import random

    # 默认获得5种鱼饵，用户等级越高，获得的鱼饵种类越多，获得的鱼饵数量越多
    level_diff = player_level - fishing_spot_level

    if level_diff < 0:
        raise ValueError("您的等级太低了，无法在这个地方采集哦")
    bait_type_count = min(len(maybe_bait_list), max(5, 5 + level_diff))
    bait_count = random.randint(3, 3 + int(level_diff * 1.8))
    rarity_list = [bait.rarity.to_value() for bait in maybe_bait_list]
    maxval = max(rarity_list) + 1
    rarity_list = [maxval - rarity for rarity in rarity_list]
    rarity_sum = sum(rarity_list)
    bait_name_list = random.choices(
        [bait.name for bait in maybe_bait_list],
        weights=rarity_list,
        k=bait_type_count,
    )
    bait_count_list = random.choices(range(1, int(bait_count * 5)), k=bait_type_count)

    bait_dict = dict(zip(bait_name_list, bait_count_list))

    return bait_dict


"""
User Play Step:
1. Get some bait
2. Go to a fishing spot
3. Fish

Should Need:
"""


async def get_or_create_user(user_id: int) -> UserInfo:
    user = user_collection.find_one({"user_id": user_id})
    if user is None:
        logfire.info(f"用户{user_id}不存在，创建新用户")
        user = UserInfo(user_id=user_id)
        user_collection.insert_one(user.model_dump())
    return UserInfo.model_validate(user)


async def save_user(user: UserInfo):
    user_collection.update_one({"user_id": user.user_id}, {"$set": user.model_dump()})


async def gather_bait(user_id: int, fishing_spot_name: str):
    user = await get_or_create_user(user_id)
    logfire.info(f"用户{user_id}开始在{fishing_spot_name}采集鱼饵")
    # 输出最大采集次数，以及已经采集的次数
    logfire.info(
        f"用户{user_id}今天的采集次数上限为{user.get_gather_limit()}次，已经采集了{user.get_gather_count()}次"
    )
    if not user.check_gather_limit():
        raise ValueError(
            f"你今天的采集次数已经用完了哦，每天可采集{user.get_gather_limit()}次"
        )
    bait_dict = await random_get_bait_by_fishing_spot_name(
        user.level, fishing_spot_name
    )
    for bait_name, count in bait_dict.items():
        user.bait_inventory.items.setdefault(bait_name, 0)
        user.bait_inventory.items[bait_name] += count
    user.update_bait_book()
    user.increase_gather_count()
    await save_user(user)
    return bait_dict


async def get_game_time():
    return GameTime.from_real_time()


async def get_my_bait(user_id: int):
    data = (await get_or_create_user(user_id)).bait_inventory.items
    # 按照稀有度，等级，数量排序
    global bait_dict
    my_bait_dict = dict(
        sorted(
            data.items(),
            key=lambda item: (
                bait_dict[item[0]].rarity.to_value(),
                bait_dict[item[0]].level,
                item[1],
            ),
            reverse=True,
        )
    )
    return my_bait_dict


async def get_my_bait_book(user_id: int) -> list[Bait]:
    user = await get_or_create_user(user_id)
    book = user.get_bait_book()
    logfire.info(f"用户{user_id}的鱼饵图鉴：{book}")
    d = [bait for bait in bait_list if bait.name in book]
    return sorted(d, key=lambda x: (x.rarity.to_value(), x.level), reverse=True)


# 获取目前可达地点
async def get_my_fishing_spot(user_id: int) -> list[FishingSpot]:
    user = await get_or_create_user(user_id)
    return [spot for spot in fishing_spot_list if spot.level <= user.level]


async def get_my_fishing_spot_str(user_id: int):
    spot_list = await get_my_fishing_spot(user_id)
    return "\n".join(f"{spot.name}({spot.level}级)：{spot.story}" for spot in spot_list)


async def get_simple_fishing_spot_str(user_id: int):
    spot_list = await get_my_fishing_spot(user_id)
    return "\n".join(f"{spot.name}" for spot in spot_list)


async def my_location(user_id: int):
    user = await get_or_create_user(user_id)
    return (
        f"你现在在{user.current_fishing_spot}"
        if user.current_fishing_spot
        else "你现在哪也不在"
    )


async def my_info(user_id: int):
    user = await get_or_create_user(user_id)
    resp = f"""
[用户信息]
等级：{user.level}
经验：{user.exp}
是否签到：{user.is_sign_in_today() and "是" or "否"}
当前所在钓场：{user.current_fishing_spot or "无"}
当前使用鱼饵：{user.current_bait or "无"}
游戏时间：{(await get_game_time()).to_chinese()}
""".strip()
    return resp


async def goto_fishing_spot(user_id: int, fishing_spot_name: str):
    user = await get_or_create_user(user_id)
    spot = next(spot for spot in fishing_spot_list if spot.name == fishing_spot_name)
    if spot.level > user.level:
        raise ValueError(
            f"你的等级太低了，无法在{fishing_spot_name}钓鱼哦，请{spot.level}级再来"
        )
    user.current_fishing_spot = fishing_spot_name
    await save_user(user)
    return f"你已经来到了{fishing_spot_name}，开始钓鱼吧！"


async def set_my_current_bait(user_id: int, bait_name: str):
    user = await get_or_create_user(user_id)
    if not bait_dict.get(bait_name):
        raise ValueError(f"你确定有这种鱼饵吗？{bait_name}")
    if user.bait_inventory.items.get(bait_name, 0) <= 0:
        raise ValueError(f"你没有{bait_name}这种鱼饵哦")
    user.current_bait = bait_name
    await save_user(user)
    return f"设置成功，你现在使用的鱼饵是{bait_name}"


async def get_my_bait_book_str(user_id: int):
    # 按稀有度，等级排序，输出名字，等级，稀有度，描述
    book = await get_my_bait_book(user_id)
    logfire.info(f"用户{user_id}的鱼饵图鉴：{book}")
    return "\n".join(
        f"{bait.name}({bait.level}级) {bait.rarity.to_string()} {bait.description}"
        for bait in book
    )


# 获取鱼类图鉴
async def get_my_fish_book(user_id: int) -> list[FishBaseInfo]:
    user = await get_or_create_user(user_id)
    book = user.get_fish_book()
    logfire.info(f"用户{user_id}的鱼类图鉴：{book}")
    d = [
        fish
        for spot in fishing_spot_list
        for fish in spot.fish_list
        if fish.name in book
    ]
    return sorted(d, key=lambda x: (x.rarity.to_value()), reverse=True)


async def get_my_fish_book_str(user_id: int):
    book = await get_my_fish_book(user_id)
    logfire.info(f"用户{user_id}的鱼类图鉴：{book}")
    return "\n".join(
        f"【{fish.rarity.to_string()}】{fish.name} : {fish.story}" for fish in book
    )


async def get_my_bait_with_full_info(user_id: int) -> dict[str, Bait]:
    bait_dict = await get_my_bait(user_id)
    return {
        bait_name: next(bait for bait in bait_list if bait.name == bait_name)
        for bait_name in bait_dict.keys()
    }


async def build_item_str(item: dict[str, int]):
    return "，".join(f"{k}x{v}" for k, v in item.items())


async def get_my_bait_str(user_id: int):
    my_bait_dict = await get_my_bait(user_id)
    return f"你的鱼饵库存：{await build_item_str(my_bait_dict)}"


async def from_bait_name_to_bait(bait_name: str):
    return next(bait for bait in bait_list if bait.name == bait_name)


async def from_baits_name_to_baits(bait_names: list[str]):
    return [await from_bait_name_to_bait(bait_name) for bait_name in bait_names]


# 详情
async def get_my_bait_detail_str(user_id: int):
    # {name}x{count}({rarity}) description
    my_bait = await get_my_bait(user_id)
    bait_str_list = []
    for name, count in my_bait.items():
        bait = next(bait for bait in bait_list if bait.name == name)
        bait_str_list.append(
            f"{name}x{count}({bait.rarity.to_string()}) {bait.description}"
        )
    return "\n".join(bait_str_list)


# 鱼饵+钓场+天气确定能否钓到某种鱼
# 该函数只需返回能钓到的鱼的列表
# 具体钓鱼的逻辑在其他地方实现
async def can_fish(
    user_id: int,
    bait_name: str | None = None,
    fishing_spot_name: str | None = None,
    game_time: GameTime | None = None,
) -> List[FishBaseInfo]:
    user = await get_or_create_user(user_id)
    if bait_name is None:
        bait_name = user.current_bait
    if fishing_spot_name is None:
        fishing_spot_name = user.current_fishing_spot
    if not bait_name:
        raise ValueError("你没有选择鱼饵哦，请指明使用的鱼饵或者设置默认鱼饵")
    if not fishing_spot_name:
        raise ValueError("你没有选择钓场哦，请指明钓场或者设置默认钓场")
    if bait_name not in bait_meta_dict:
        raise ValueError(f"你确定有这种鱼饵吗？{bait_name}")
    if bait_name not in user.bait_inventory.items:
        raise ValueError(f"你没有{bait_name}这种鱼饵哦")
    if fishing_spot_name not in get_fishing_spot_name_list():
        raise ValueError(f"没有这个地方哦：{fishing_spot_name}")
    fish_list = await what_fish_can_bait_get(bait_name)
    if not fish_list:
        raise ValueError(f"你确定有这种鱼饵吗？{bait_name}")
    fishing_spot = next(
        spot for spot in fishing_spot_list if spot.name == fishing_spot_name
    )
    if fishing_spot_name not in bait_meta_dict[bait_name].target_fishing_spot_list:
        raise ValueError(f"鱼饵{bait_name}在{fishing_spot_name}钓不到鱼哦")
    if fishing_spot.level > user.level:
        raise ValueError(
            f"你的等级太低了，无法在{fishing_spot_name}钓鱼哦，请{fishing_spot.level}级再来"
        )
    # 看一下gametime符不符合要求
    game_time = await get_game_time() if game_time is None else game_time
    fish_list = [
        fish for fish in fish_list if await fish.can_fish_with_game_time(game_time)
    ]
    if not fish_list:
        raise ValueError(f"现在的时机不适合使用{bait_name}钓鱼哦")
    return fish_list


# 根据传入的基本鱼类信息，添加鱼实体进用户的背包
async def add_fish_to_user(user_id: int, fish: FishBaseInfo) -> FishEntity:
    user = await get_or_create_user(user_id)
    fish_entity = FishEntity.from_fish_base_info(fish)
    user.fish_inventory.fish_entity_list.append(fish_entity)
    await save_user(user)
    return fish_entity


# 获取经验(根据场地等级，鱼的稀有度)
async def get_exp_by_fish(user_id: int, fish: FishBaseInfo, fishing_spot_name: str):
    fishing_spot = next(
        spot for spot in fishing_spot_list if spot.name == fishing_spot_name
    )
    user = await get_or_create_user(user_id)
    # 经验=鱼的稀有度*钓场等级
    exp = fish.rarity.to_value() * fishing_spot.level
    # 衰减：用户等级-钓场等级
    exp = exp - (user.level - fishing_spot.level)
    exp = max(1, exp)
    user.exp += exp
    resp_str = f"获得了{exp}点经验"
    # 判断是否能升级
    if user.exp >= user.level * 10:
        user.level += 1
        user.exp -= user.level * 10
        resp_str += f"，升级了，当前等级{user.level}"
    await save_user(user)
    return resp_str


# 扣除指定鱼饵
async def consume_bait(user_id: int, bait_name: str, count: int = 1):
    user = await get_or_create_user(user_id)
    if user.bait_inventory.items.get(bait_name, 0) < count:
        raise ValueError(f"你没有{bait_name}这种鱼饵哦")
    user.bait_inventory.items[bait_name] -= count
    await save_user(user)


# 获取不高于用户等级的最后一个钓场
async def get_last_fishing_spot(user_id: int):
    user = await get_or_create_user(user_id)
    for spot in reversed(fishing_spot_list):
        if spot.level <= user.level:
            return spot
    return None


# 获取钓场里的所有鱼饵
async def get_bait_list_by_fishing_spot_name(fishing_spot_name: str):
    if fishing_spot_name not in get_fishing_spot_name_list():
        raise ValueError(f"没有这个地方哦：{fishing_spot_name}")
    global bait_list
    data = []
    for bait in bait_list:
        if isinstance(bait.source_config, GatherRequirement):
            location = bait.source_config.location
            if fishing_spot_name in location:
                data.append(bait)
    return data


# 每日签到奖励
async def daily_sign_in(user_id: int):
    user = await get_or_create_user(user_id)
    if user.sign_in() or str(user_id) in ["1025799490"]:
        spot = await get_last_fishing_spot(user_id)
        bait_dict = await random_get_bait_by_fishing_spot_name(user.level, spot.name)
        for bait_name, count in bait_dict.items():
            user.bait_inventory.items.setdefault(bait_name, 0)
            user.bait_inventory.items[bait_name] += count
        await save_user(user)

        def build_bait_str(bait_name, count):
            return f"{bait_name}x{count}"

        bait_str = "，".join(
            build_bait_str(bait_name, count) for bait_name, count in bait_dict.items()
        )
        return f"签到成功，获得了{bait_str}"
    raise ValueError("今天已经签到过了哦")


async def main():
    # 检查是否存在重名鱼，如果存在，输出对应的钓场信息和鱼的信息
    fish_name_dict = {}
    for spot in fishing_spot_list:
        for fish in spot.fish_list:
            if fish.name in fish_name_dict:
                fish_name_dict[fish.name].append((spot, fish))
            else:
                fish_name_dict[fish.name] = [(spot, fish)]
    for name, info in fish_name_dict.items():
        if len(info) > 1:
            print(f"重名鱼：{name}")
            for spot, fish in info:
                print(f"钓场：{spot.name}({spot.level}级)，鱼的信息：{fish}")
    """
        print(get_fishing_spot_overview())
        print(get_fishing_spot_name_list())
        print(get_bait_list_by_fishing_spot_name("晨露溪湾"))
        print(random_get_bait_by_fishing_spot_name(1, "晨露溪湾"))
        print(await get_or_create_user(1))
    """
    print(await get_game_time())
    print(await can_fish(1, "柳絮碎片", "晨露溪湾"))
    print((await get_my_bait(1)))


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
