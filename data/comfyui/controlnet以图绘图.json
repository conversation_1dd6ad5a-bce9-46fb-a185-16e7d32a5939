{"1": {"inputs": {"image": "00063.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "8": {"inputs": {"size": 1024, "mode": true, "images": ["1", 0]}, "class_type": "easy imageScaleDownToSize", "_meta": {"title": "限制分辨率"}}, "19": {"inputs": {"control_net_name": "controlnet-union-sdxl-1.0.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "20": {"inputs": {"type": "canny/lineart/anime_lineart/mlsd", "control_net": ["19", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "21": {"inputs": {"strength": 1, "start_percent": 0, "end_percent": 1, "positive": ["25", 0], "negative": ["26", 0], "control_net": ["20", 0], "image": ["22", 0], "vae": ["24", 2]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "22": {"inputs": {"guassian_sigma": 6, "intensity_threshold": 8, "resolution": 1024, "image": ["28", 0]}, "class_type": "LineartStandardPreprocessor", "_meta": {"title": "Standard Lineart"}}, "24": {"inputs": {"ckpt_name": "checkpoint/wai-nsfw-illustrious-sdxl/illustrious/1761560-wainsfwillustrious_v140/1761560_waiNSFWIllustrious_v140.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "25": {"inputs": {"text": "best quality, amazing quality,{prompt}, very aesthetic, absurdres", "clip": ["24", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "26": {"inputs": {"text": "bad quality,worst quality,worst detail,sketch,censor,", "clip": ["24", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "27": {"inputs": {"seed": 122853239423469, "steps": 20, "cfg": 8, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["24", 0], "positive": ["21", 0], "negative": ["21", 1], "latent_image": ["29", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "28": {"inputs": {"image": ["8", 0]}, "class_type": "GetImageSizeAndCount", "_meta": {"title": "Get Image Size & Count"}}, "29": {"inputs": {"width": ["28", 1], "height": ["28", 2], "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "30": {"inputs": {"samples": ["27", 0], "vae": ["24", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "31": {"inputs": {"filename_prefix": "nb_comfyui/controlnet/cn", "images": ["30", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "32": {"inputs": {"images": ["22", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}}