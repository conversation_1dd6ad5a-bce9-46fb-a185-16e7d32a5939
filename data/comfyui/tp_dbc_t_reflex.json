{"output": 13, "command": "tipo打搅", "image_size": 3, "load_image": 17, "sampler": 10, "reg_args": {"20": {"args": [{"name_or_flags": ["-pr"], "type": "str", "dest": "text", "help": "画师串预设", "default": "", "preset": {"1": ",(artist:dsmile:0.5), (artist:<PERSON><PERSON><PERSON>:0.4),(artist:mom<PERSON> (momopoco):0.6)|(artist:maccha_(mochancc):0.4),(artist:colon br:1),best quality, amazing quality, very aesthetic, absurdres,", "2": ",(artist:re<PERSON>:1.1)|(artist:r<PERSON><PERSON>:1.3),(artist:colon br:0.8),(artist:mom<PERSON> (momopoco):0.6)|(artist:maccha_(mochancc):0.4),best quality, amazing quality, very aesthetic, absurdres,", "3": ",(artist:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:1),(artist:r<PERSON><PERSON>:1), (artist:to<PERSON> aqua:0.4),(artist:mom<PERSON> (momopoco):0.7),(artist:<PERSON><PERSON><PERSON> sei:1.1),(artist:yuki (yuki no 42):1),(artist:<PERSON><PERSON>:0.5),(artist:vs0mr:1.1),best quality, amazing quality, very aesthetic, absurdres,", "4": ",(artist:fur_y:1),(artist:a<PERSON><PERSON>6:1),(artist:chen bin:1),(artist:chon (chon33v):1),(artist:cogecha:1),,(artist:ch<PERSON><PERSON> sora:1),", "5": ",(artist:g<PERSON><PERSON> \\(g-size\\):0.75), (artist:ningen_mame:0.9),(artist:sho_(sho_lwlw):0.9),(artist:rhasta:0.9),(artist:wlop:0.7),(artist:ke-ta:0.6),(fkey:0.5),(fuzichoco:0.6),artist:chen bin,best quality, amazing quality, very aesthetic, absurdres, masterpiece,"}}]}}, "negative_prompt": {"2": {"override": {"text": "append_negative_prompt"}}}, "note": "使用wd-tagger插件为图片打标，tipo拓展，然后生成图片"}