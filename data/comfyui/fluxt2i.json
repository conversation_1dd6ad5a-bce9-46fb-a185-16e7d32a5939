{"6": {"inputs": {"text": ["49", 2], "clip": ["44", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "8": {"inputs": {"samples": ["13", 0], "vae": ["10", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "9": {"inputs": {"filename_prefix": "ComfyUI", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "10": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "13": {"inputs": {"noise": ["25", 0], "guider": ["22", 0], "sampler": ["16", 0], "sigmas": ["17", 0], "latent_image": ["27", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "自定义采样器（高级）"}}, "16": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "K采样器选择"}}, "17": {"inputs": {"scheduler": "simple", "steps": 8, "denoise": 1, "model": ["30", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "基本调度器"}}, "22": {"inputs": {"model": ["30", 0], "conditioning": ["26", 0]}, "class_type": "BasicGuider", "_meta": {"title": "基本引导器"}}, "25": {"inputs": {"noise_seed": 180967127657463}, "class_type": "RandomNoise", "_meta": {"title": "随机噪波"}}, "26": {"inputs": {"guidance": 3.5, "conditioning": ["6", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "27": {"inputs": {"width": ["54", 0], "height": ["55", 0], "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "空Latent图像（SD3）"}}, "30": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["54", 0], "height": ["55", 0], "model": ["45", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "采样算法（Flux）"}}, "44": {"inputs": {"model_type": "flux", "text_encoder1": "t5/t5xxl_fp16.safetensors", "text_encoder2": "clip_l.safetensors", "t5_min_length": 512, "use_4bit_t5": "disable", "int4_model": "none"}, "class_type": "NunchakuTextEncoderLoader", "_meta": {"title": "Nunchaku Text Encoder Loader (Deprecated)"}}, "45": {"inputs": {"model_path": "svdq-int4-flux.1-dev", "cache_threshold": 0, "attention": "nunchaku-fp16", "cpu_offload": "auto", "device_id": 0, "data_type": "bfloat16", "i2f_mode": "enabled"}, "class_type": "NunchakuFluxDiTLoader", "_meta": {"title": "Nunchaku FLUX DiT Loader"}}, "48": {"inputs": {"api_key": "sk-033a4e41eb5b442e81788f0376847823", "system_prompt": "你是一个英语专家，专门将用户的输入翻译为英文，你的输出以```english 开头，后面只包含翻译后的文本，例如：\n用户输入：她\n你返回：\n```english\nshe", "prompt": "{prompt}", "model": "DeepSeek-V3", "seed": 824517310358754}, "class_type": "DeepSeekV3", "_meta": {"title": "DeepSeek V3"}}, "49": {"inputs": {"split_tag": "```english", "pause": "", "string_editor_persistent_widget_-1": "", "input_string": ["48", 0]}, "class_type": "StringEditorPersistentTempFileNode", "_meta": {"title": "Show or Edit String"}}, "54": {"inputs": {"value": 1024}, "class_type": "INTConstant", "_meta": {"title": "width"}}, "55": {"inputs": {"value": 1024}, "class_type": "INTConstant", "_meta": {"title": "height"}}}