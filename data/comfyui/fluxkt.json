{"6": {"inputs": {"text": ["192", 0], "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "8": {"inputs": {"samples": ["31", 0], "vae": ["39", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "31": {"inputs": {"seed": 942876244174970, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["37", 0], "positive": ["35", 0], "negative": ["135", 0], "latent_image": ["124", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "35": {"inputs": {"guidance": 2.5, "conditioning": ["177", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "37": {"inputs": {"unet_name": "FLUX.1-Kontext-dev/flux1-kontext-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "UNet加载器"}}, "38": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5/t5xxl_fp16.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}, "39": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "42": {"inputs": {"image": ["146", 0]}, "class_type": "FluxKontextImageScale", "_meta": {"title": "FluxKontextImageScale"}}, "124": {"inputs": {"pixels": ["42", 0], "vae": ["39", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE编码"}}, "135": {"inputs": {"conditioning": ["6", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "条件零化"}}, "136": {"inputs": {"filename_prefix": "ComfyUI", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "146": {"inputs": {"direction": "right", "match_image_size": true, "spacing_width": 0, "spacing_color": "white", "image1": ["189", 0]}, "class_type": "ImageStitch", "_meta": {"title": "Image Stitch"}}, "173": {"inputs": {"images": ["42", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "177": {"inputs": {"conditioning": ["6", 0], "latent": ["124", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "189": {"inputs": {"image": "db8f1e4a-d27f-4aee-8c9c-35bd017e58cd.jpeg"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "190": {"inputs": {"api_key": "sk-033a4e41eb5b442e81788f0376847823", "system_prompt": "你是一个英语专家，专门将用户的输入翻译为英文，你的输出以```english 开头，后面只包含翻译后的文本，例如：\n用户输入：她\n你返回：\n```english\nshe", "prompt": "{prompt}", "model": "DeepSeek-V3", "seed": 1105705748220820}, "class_type": "DeepSeekV3", "_meta": {"title": "DeepSeek V3"}}, "191": {"inputs": {"split_tag": "```english", "pause": "", "string_editor_persistent_widget_-1": "", "input_string": ["190", 0]}, "class_type": "StringEditorPersistentTempFileNode", "_meta": {"title": "Show or Edit String"}}, "192": {"inputs": {"text_0": "Change the expression to a big laugh", "text": ["191", 2]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}}