{"3": {"inputs": {"seed": 1383541548955, "steps": 30, "cfg": 7, "sampler_name": "euler_ancestral", "scheduler": "karras", "denoise": 1, "model": ["4", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["71", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "4": {"inputs": {"ckpt_name": "checkpoint/wai-nsfw-illustrious-sdxl/illustrious/1761560-wainsfwillustrious_v140/1761560_waiNSFWIllustrious_v140.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Checkpoint加载器（简易）"}}, "6": {"inputs": {"text": ["48", 0], "clip": ["75", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive CLIP"}}, "7": {"inputs": {"text": "low quality, worst quality, text, signature, jpeg artifacts, bad anatomy, old, early, copyright name, watermark, artist name, signature, weibo username, mosaic censoring, bar censor, censored, animals", "clip": ["75", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negative CLIP"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "38": {"inputs": {"tags": ["73", 0], "nl_prompt": "", "ban_tags": "weapon, illustration, gun, ball, censor, tape, text, speeach, animal, name, sign, bondage, rope, multiple, heterochromia, open mouth, meme, sticker, dragon, from behind", "tipo_model": "KBlueLeaf/TIPO-500M-ft", "format": "<|special|>, \n<|characters|>, <|copyrights|>, \n<|artist|>, \n\n<|general|>\n\n<|extended|>\n\n<|quality|>, <|meta|>, <|rating|>", "width": 1344, "height": 896, "temperature": 0.5, "top_p": 1, "min_p": 0.05, "top_k": 100, "tag_length": "long", "nl_length": "short", "seed": 1383541548955, "device": "cuda"}, "class_type": "TIPO", "_meta": {"title": "TIPO"}}, "45": {"inputs": {"filename_prefix": "ComfyUI", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "TIPO Generated Prompt"}}, "48": {"inputs": {"text": ["38", 0], "text2": "1girl, \n\n(fkey:0.5), (fuzichoco:0.6), \n\n(artist:g<PERSON><PERSON> \\(g-size\\):0.75), artist:ningen mame, artist:sho, sho lwlw, (artist:rhasta:0.9), (artist:wlop:0.7), (artist:ke-ta:0.6), artist:chen bin, smile, open mouth, simple background, white background, hat, twintails, monochrome, :d, drill hair, eyepatch, lolita fashion, top hat, mini hat, mini top hat, medical eyepatch, chainsaw, dress, gothic lolita, greyscale, ahoge, ribbon, looking at viewer, holding, solo, short hair, long sleeves, frill\n\nA young girl with shoulder-length blonde hair styled in two pigtails. she is wearing a black jacket with a ruffled collar and a bow on the left side of her head. the girl has a big smile on her face and is looking directly at the camera"}, "class_type": "ShowText|pysssss", "_meta": {"title": "Formatted TIPO Output"}}, "71": {"inputs": {"width": 1344, "height": 896, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空Latent图像"}}, "72": {"inputs": {"text": "(artist:g<PERSON><PERSON> \\(g-size\\):0.75), (artist:ningen_mame:0.9),(artist:sho_(sho_lwlw):0.9),(artist:rhasta:0.9),(artist:wlop:0.7),(artist:ke-ta:0.6),(fkey:0.5),(fuzichoco:0.6),artist:chen bin,"}, "class_type": "Text Multiline", "_meta": {"title": "画师串"}}, "73": {"inputs": {"delimiter": ", ", "clean_whitespace": "true", "text_a": ["72", 0], "text_b": ["74", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "74": {"inputs": {"text": "{prompt}"}, "class_type": "Text Multiline", "_meta": {"title": "正面提示词"}}, "75": {"inputs": {"stop_at_clip_layer": -2, "clip": ["4", 1]}, "class_type": "CLIPSetLastLayer", "_meta": {"title": "设置CLIP最后一层"}}}