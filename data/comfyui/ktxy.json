{"6": {"inputs": {"text": "ZOZ_KTXY,ZOZ_KTXY,\nThe image depicts a fantasy-themed character with long,\nflowing blonde hair and pointed elf-like ears. She is adorned in intricate,\ndark,\nand light-colored armor that includes a high collar and decorative elements. Her outfit is complemented by a striking,\nglowing blue sword that she wields,\nemitting a bright light. The background is a vibrant,\nethereal scene with swirling blue and purple hues,\ncreating a mystical atmosphere. The character is kneeling on the ground,\nsurrounded by glowing blue and purple tendrils that add to the magical ambiance.,", "clip": ["44", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "8": {"inputs": {"samples": ["13", 0], "vae": ["10", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "9": {"inputs": {"filename_prefix": "ComfyUI", "images": ["64", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "10": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "13": {"inputs": {"noise": ["25", 0], "guider": ["22", 0], "sampler": ["16", 0], "sigmas": ["17", 0], "latent_image": ["27", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "自定义采样器（高级）"}}, "16": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "K采样器选择"}}, "17": {"inputs": {"scheduler": "simple", "steps": 20, "denoise": 1, "model": ["30", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "基本调度器"}}, "22": {"inputs": {"model": ["30", 0], "conditioning": ["26", 0]}, "class_type": "BasicGuider", "_meta": {"title": "基本引导器"}}, "25": {"inputs": {"noise_seed": 1018892688870670}, "class_type": "RandomNoise", "_meta": {"title": "随机噪波"}}, "26": {"inputs": {"guidance": 3.5, "conditioning": ["6", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "27": {"inputs": {"width": ["54", 0], "height": ["55", 0], "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "空Latent图像（SD3）"}}, "30": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["54", 0], "height": ["55", 0], "model": ["58", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "采样算法（Flux）"}}, "44": {"inputs": {"model_type": "flux", "text_encoder1": "t5/t5xxl_fp16.safetensors", "text_encoder2": "clip_l.safetensors", "t5_min_length": 512, "use_4bit_t5": "disable", "int4_model": "none"}, "class_type": "NunchakuTextEncoderLoader", "_meta": {"title": "Nunchaku Text Encoder Loader (Deprecated)"}}, "45": {"inputs": {"model_path": "svdq-int4-flux.1-dev", "cache_threshold": 0, "attention": "nunchaku-fp16", "cpu_offload": "auto", "device_id": 0, "data_type": "bfloat16", "i2f_mode": "enabled"}, "class_type": "NunchakuFluxDiTLoader", "_meta": {"title": "Nunchaku FLUX DiT Loader"}}, "54": {"inputs": {"value": 768}, "class_type": "INTConstant", "_meta": {"title": "width"}}, "55": {"inputs": {"value": 1536}, "class_type": "INTConstant", "_meta": {"title": "height"}}, "58": {"inputs": {"lora_name": "ZOZ_芙露德莉斯-大卡提希娅_极致逼真人像.safetensors", "lora_strength": 0.8000000000000002, "model": ["45", 0]}, "class_type": "NunchakuFluxLoraLoader", "_meta": {"title": "Nunchaku FLUX.1 LoRA Loader"}}, "62": {"inputs": {"operation": "division", "number_a": ["67", 0], "number_b": ["63", 0]}, "class_type": "Number Operation", "_meta": {"title": "Number Operation"}}, "63": {"inputs": {"text": ["68", 0]}, "class_type": "Text to Number", "_meta": {"title": "Text to Number"}}, "64": {"inputs": {"upscale_method": "nearest-exact", "scale_by": ["62", 1], "image": ["65", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "65": {"inputs": {"upscale_model": ["71", 0], "image": ["66", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "66": {"inputs": {"size": 1216, "mode": true, "images": ["8", 0]}, "class_type": "easy imageScaleDownToSize", "_meta": {"title": "限制分辨率"}}, "67": {"inputs": {"text": ["69", 0]}, "class_type": "Text to Number", "_meta": {"title": "Text to Number"}}, "68": {"inputs": {"text": "4"}, "class_type": "Text Multiline", "_meta": {"title": "Text Multiline"}}, "69": {"inputs": {"text": "3"}, "class_type": "Text Multiline", "_meta": {"title": "超分倍率"}}, "71": {"inputs": {"model_name": "RealESRGAN_x4plus.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}}