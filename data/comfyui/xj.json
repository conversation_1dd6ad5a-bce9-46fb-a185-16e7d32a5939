{"3": {"inputs": {"seed": 564019172415028, "steps": 20, "cfg": 6, "sampler_name": "uni_pc", "scheduler": "simple", "denoise": 1, "model": ["55", 0], "positive": ["50", 0], "negative": ["50", 1], "latent_image": ["50", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "6": {"inputs": {"text": "一个女孩脱掉了自己的衣服", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "7": {"inputs": {"text": "Overexposure, static, blurred details, subtitles, paintings, pictures, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, mutilated, redundant fingers, poorly painted hands, poorly painted faces, deformed, disfigured, deformed limbs, fused fingers, cluttered background, three legs, a lot of people in the background, upside down, white flashlights, glow light effect, post edit, ugly distorted hands, scrambled fingers, wild arm movement, text, icons, logo, post production", "clip": ["55", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["39", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "28": {"inputs": {"filename_prefix": "ComfyUI", "fps": 16, "lossless": false, "quality": 90, "method": "default", "images": ["63", 0]}, "class_type": "SaveAnimatedWEBP", "_meta": {"title": "保存动画（WEBP）"}}, "37": {"inputs": {"unet_name": "wan2.1_i2v_480p_14B_fp8_e4m3fn.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "UNet加载器"}}, "38": {"inputs": {"clip_name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "type": "wan", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "加载CLIP"}}, "39": {"inputs": {"vae_name": "wan_2.1_vae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "49": {"inputs": {"clip_name": "clip_vision_h.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "加载CLIP视觉"}}, "50": {"inputs": {"width": ["64", 0], "height": ["64", 1], "length": 33, "batch_size": 1, "positive": ["6", 0], "negative": ["7", 0], "vae": ["39", 0], "clip_vision_output": ["51", 0], "start_image": ["56", 0]}, "class_type": "WanImageToVideo", "_meta": {"title": "Wan图像到视频"}}, "51": {"inputs": {"crop": "none", "clip_vision": ["49", 0], "image": ["52", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP视觉编码"}}, "52": {"inputs": {"image": "652FE3439411E9ECE963D58F19774243.jpg"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "54": {"inputs": {"shift": 8, "model": ["37", 0]}, "class_type": "ModelSamplingSD3", "_meta": {"title": "采样算法（SD3）"}}, "55": {"inputs": {"lora_name": "I2V-tittydrop-e26.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["54", 0], "clip": ["38", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "加载LoRA"}}, "56": {"inputs": {"upscale_method": "nearest-exact", "width": 0, "height": 480, "crop": "disabled", "image": ["52", 0]}, "class_type": "ImageScale", "_meta": {"title": "缩放图像"}}, "57": {"inputs": {"image": ["52", 0]}, "class_type": "GetImageSize", "_meta": {"title": "Get Image Size"}}, "63": {"inputs": {"upscale_method": "nearest-exact", "width": ["57", 0], "height": ["57", 1], "crop": "disabled", "image": ["8", 0]}, "class_type": "ImageScale", "_meta": {"title": "缩放图像"}}, "64": {"inputs": {"image": ["56", 0]}, "class_type": "GetImageSize", "_meta": {"title": "Get Image Size"}}}