{"27": {"inputs": {"api_key": "sk-033a4e41eb5b442e81788f0376847823", "system_prompt": "你是卓越的歌词作者. 用户给你一个主题, 围绕主题严格按照要求写一篇歌词.\n格式指引：\n# 要求包含 [verse], [chorus], [bridge], [outro] 英文标签, 并放在每一节的前面, \n# 歌词是中文, 并按如下格式输出:\n\n[verse]\n<歌词...>\n\n[chorus]\n<歌词...>\n\n[bridge]\n<歌词...>\n\n...\n\n# 再用不超过 50 个单词的 tags: \"英文极短语或单词\" 简要描述首歌的风格, 乐器伴凑, 男或女声音等, 重点突出, \n# 重点参考使用以下 tags, 可简单扩展和组合:\n\nelectronic, rock, pop, funk, soul, cyberpunk, Acid jazz, electro, electronic music, soft electric drums, melodic, background music for parties, radio broadcasts, workout playlists, saxophone, jazz, piano, violin, female voice, male voice, clean vocals, 110 bpm, fast tempo, slow tempo, loops, fills, acoustic guitar, electric bass\n\n# 只输出 歌词 和 tags, tags 之前必须有标签 \"tags:\" 隔开", "prompt": "{prompt}", "model": "DeepSeek-V3", "seed": 559039091393956}, "class_type": "DeepSeekV3", "_meta": {"title": "DeepSeek V3"}}, "35": {"inputs": {"split_tag": "tags", "pause": "", "string_editor_persistent_widget_-1": "", "input_string": ["27", 0]}, "class_type": "StringEditorPersistentTempFileNode", "_meta": {"title": "Show or Edit String"}}, "37": {"inputs": {"ckpt_name": "ace_step_v1_3.5b.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Checkpoint加载器（简易）"}}, "38": {"inputs": {"tags": ["35", 2], "lyrics": ["45", 0], "lyrics_strength": 1, "clip": ["37", 1]}, "class_type": "TextEncodeAceStepAudio", "_meta": {"title": "TextEncodeAceStepAudio"}}, "39": {"inputs": {"shift": 4.000000000000001, "model": ["37", 0]}, "class_type": "ModelSamplingSD3", "_meta": {"title": "采样算法（SD3）"}}, "40": {"inputs": {"seconds": 60, "batch_size": 1}, "class_type": "EmptyAceStepLatentAudio", "_meta": {"title": "EmptyAceStepLatentAudio"}}, "41": {"inputs": {"seed": 1025695799411261, "steps": 50, "cfg": 4, "sampler_name": "res_multistep", "scheduler": "simple", "denoise": 1, "model": ["39", 0], "positive": ["38", 0], "negative": ["44", 0], "latent_image": ["40", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "42": {"inputs": {"samples": ["41", 0], "vae": ["37", 2]}, "class_type": "VAEDecodeAudio", "_meta": {"title": "VAE解码（音频）"}}, "43": {"inputs": {"format": "WAV", "filename_prefix": "audio/ComfyUI", "save_audio_player_button": "", "audio": ["42", 0]}, "class_type": "SaveAudioMW", "_meta": {"title": "Save Audio @MW"}}, "44": {"inputs": {"conditioning": ["38", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "条件零化"}}, "45": {"inputs": {"lyrics": ["35", 1]}, "class_type": "LyricsLangSwitch", "_meta": {"title": "ACE-Step Lyrics Language Switch"}}, "47": {"inputs": {"text": ["27", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "查看歌词"}}}