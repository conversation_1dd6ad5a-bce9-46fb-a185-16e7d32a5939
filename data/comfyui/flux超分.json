{"319": {"inputs": {"model_name": "8x_NMKD-Superscale_150000_G.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "加载放大模型"}}, "320": {"inputs": {"upscale_by": 2.0000000000000004, "seed": 185265965688199, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 0.6, "mode_type": "Chess", "tile_width": 1280, "tile_height": 1280, "mask_blur": 8, "tile_padding": 32, "seam_fix_mode": "None", "seam_fix_denoise": 0.48, "seam_fix_width": 64, "seam_fix_mask_blur": 8, "seam_fix_padding": 16, "force_uniform_tiles": "enable", "tiled_decode": false, "image": ["733", 0], "model": ["664", 0], "positive": ["663", 0], "negative": ["665", 0], "vae": ["570", 0], "upscale_model": ["319", 0]}, "class_type": "UltimateSDUpscale", "_meta": {"title": "Ultimate SD Upscale"}}, "321": {"inputs": {"text": ["474", 0], "clip": ["666", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "322": {"inputs": {"text": ["325", 0], "clip": ["666", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "325": {"inputs": {"part1": "", "part2": "(worst quality, low quality, illustration, 3d, 2d, painting, cartoons, sketch), open mouth", "part3": "", "part4": "", "separator": ","}, "class_type": "CR Combine Prompt", "_meta": {"title": "PROMPT NEG"}}, "332": {"inputs": {"strength": 0.9, "start_percent": 0, "end_percent": 1, "positive": ["321", 0], "negative": ["322", 0], "control_net": ["333", 0], "image": ["733", 0], "vae": ["570", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "应用ControlNet（旧版高级）"}}, "333": {"inputs": {"control_net_name": "jasperai/Flux.1-dev-Controlnet-Upscaler/diffusion_pytorch_model.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "加载ControlNet模型"}}, "352": {"inputs": {"action": "append", "tidy_tags": "no", "text_a": ["446", 0], "text_b": "_UPSCALED_STAGE_3", "text_c": "", "result": "E36A2A49C5C598919CC2C7DBFB49D5A4_UPSCALED_STAGE_3"}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "386": {"inputs": {"rgthree_comparer": {"images": [{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_nmpld_00001_.png&type=temp&subfolder=&rand=0.32083813416627316"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_nmpld_00002_.png&type=temp&subfolder=&rand=0.44432221549505524"}]}, "image_a": ["538", 0], "image_b": ["556", 0]}, "class_type": "Image Comparer (rgthree)", "_meta": {"title": "Base vs Stage 4"}}, "393": {"inputs": {"filename_prefix": ["352", 0], "images": ["556", 0]}, "class_type": "SaveImage", "_meta": {"title": "最终结果"}}, "446": {"inputs": {"text_0": "E36A2A49C5C598919CC2C7DBFB49D5A4", "text": ["538", 2]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "450": {"inputs": {"rgthree_comparer": {"images": [{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_pozpt_00001_.png&type=temp&subfolder=&rand=0.6762430133848341"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_pozpt_00002_.png&type=temp&subfolder=&rand=0.7627212230855318"}]}, "image_a": ["556", 0], "image_b": ["733", 0]}, "class_type": "Image Comparer (rgthree)", "_meta": {"title": "Stage 3 vs Stage 4"}}, "474": {"inputs": {"part1": "polaroid, film, photography, fashion photography, award-winning photography, aesthetic and beauty, skillful details, no blur, f/1.4, innovative, lens flare, female, vogue cover style,  vibrant (lofi, analog, kodak film)", "part2": "smooth skin, perfect skin, perfect without deformations, extreme meticulous detailing, closed mouth,", "part3": ", RAW, ultra HD, photo, analog photography, film grain, high quality, Cinematic, High Contrast, highly detailed, taken using a Canon EOS R camera, hyper detailed, photo realistic, maximum detail, 32k, Color Grading, hyper sharpness, hyper detailed, depth of field, bokeh, blurred background", "part4": "", "separator": ","}, "class_type": "CR Combine Prompt", "_meta": {"title": "PROMPT GENERAL"}}, "514": {"inputs": {"color_match_mode": "Wavelet", "color_ref_image": ["538", 0], "image": ["320", 0]}, "class_type": "DicksonColorMatch", "_meta": {"title": "Dickson Color Match"}}, "538": {"inputs": {"image": "E36A2A49C5C598919CC2C7DBFB49D5A4.png"}, "class_type": "DicksonLoadImage", "_meta": {"title": "Dickson Load Image"}}, "539": {"inputs": {"text_0": "Width: 1080\nHeight: 608", "text": ["538", 5]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "542": {"inputs": {"model_name": "4xRealWebPhoto_v4_dat2.safetensors"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "加载放大模型"}}, "543": {"inputs": {"upscale_model": ["542", 0], "image": ["538", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "使用模型放大图像"}}, "545": {"inputs": {"filename_prefix": ["546", 0], "images": ["753", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "546": {"inputs": {"action": "append", "tidy_tags": "no", "text_a": ["446", 0], "text_b": "_UPSCALED_STAGE_1", "text_c": "", "result": "E36A2A49C5C598919CC2C7DBFB49D5A4_UPSCALED_STAGE_1"}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "548": {"inputs": {"color_match_mode": "Wavelet", "color_ref_image": ["538", 0], "image": ["555", 0]}, "class_type": "DicksonColorMatch", "_meta": {"title": "Dickson Color Match"}}, "555": {"inputs": {"width": 2048, "height": 2048, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["543", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "556": {"inputs": {"width": 4096, "height": 4096, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["514", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "567": {"inputs": {"rgthree_comparer": {"images": [{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_oqkzj_00001_.png&type=temp&subfolder=&rand=0.6404222760955043"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_oqkzj_00002_.png&type=temp&subfolder=&rand=0.46864602976736913"}]}, "image_a": ["753", 0], "image_b": ["538", 0]}, "class_type": "Image Comparer (rgthree)", "_meta": {"title": "Base vs Stage 1"}}, "570": {"inputs": {"vae_name": "FLUX.1-Fill-dev/ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "630": {"inputs": {"model_name": "OmniSR_X4_DF2K/checkpoints/epoch994_OmniSR.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "加载放大模型"}}, "631": {"inputs": {"upscale_model": ["630", 0], "image": ["548", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "使用模型放大图像"}}, "634": {"inputs": {"color_match_mode": "Wavelet", "color_ref_image": ["538", 0], "image": ["756", 0]}, "class_type": "DicksonColorMatch", "_meta": {"title": "Dickson Color Match"}}, "663": {"inputs": {"guidance": 4, "conditioning": ["332", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "664": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "UNet加载器"}}, "665": {"inputs": {"conditioning": ["332", 1]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "条件零化"}}, "666": {"inputs": {"clip_name1": "t5xxl_fp8_e4m3fn.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}, "711": {"inputs": {"use_tiled_vae": true, "encoder_tile_size": 512, "decoder_tile_size": 512, "encoder_dtype": "auto", "SUPIR_VAE": ["716", 1], "image": ["753", 0]}, "class_type": "SUPIR_first_stage", "_meta": {"title": "SUPIR First Stage (Denoiser)"}}, "712": {"inputs": {"seed": 174277455657964, "steps": 26, "cfg_scale_start": 2, "cfg_scale_end": 1.5, "EDM_s_churn": 5, "s_noise": 1.0030000000000001, "DPMPP_eta": 1, "control_scale_start": 1, "control_scale_end": 0.9, "restore_cfg": 1, "keep_model_loaded": false, "sampler": "TiledRestoreEDMSampler", "sampler_tile_size": 1024, "sampler_tile_stride": 512, "SUPIR_model": ["716", 0], "latents": ["715", 0], "positive": ["713", 0], "negative": ["713", 1]}, "class_type": "SUPIR_sample", "_meta": {"title": "SUPIR Sampler"}}, "713": {"inputs": {"positive_prompt": ["474", 0], "negative_prompt": ["325", 0], "SUPIR_model": ["716", 0], "latents": ["711", 2]}, "class_type": "SUPIR_conditioner", "_meta": {"title": "SUPIR Conditioner"}}, "714": {"inputs": {"use_tiled_vae": true, "decoder_tile_size": 512, "SUPIR_VAE": ["716", 1], "latents": ["712", 0]}, "class_type": "SUPIR_decode", "_meta": {"title": "SUPIR Decode"}}, "715": {"inputs": {"use_tiled_vae": true, "encoder_tile_size": 512, "encoder_dtype": "auto", "SUPIR_VAE": ["711", 0], "image": ["711", 1]}, "class_type": "SUPIR_encode", "_meta": {"title": "SUPIR Encode"}}, "716": {"inputs": {"supir_model": "SUPIR-v0Q_fp16.safetensors", "fp8_unet": false, "diffusion_dtype": "auto", "high_vram": false, "model": ["717", 0], "clip": ["736", 0], "vae": ["737", 0]}, "class_type": "SUPIR_model_loader_v2", "_meta": {"title": "SUPIR Model Loader (v2)"}}, "717": {"inputs": {"ckpt_name": "boltningRealistic_hyperD.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Checkpoint加载器（简易）"}}, "721": {"inputs": {"rgthree_comparer": {"images": [{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_nsygb_00001_.png&type=temp&subfolder=&rand=0.0805599553293137"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_nsygb_00002_.png&type=temp&subfolder=&rand=0.20630961306275974"}]}, "image_a": ["733", 0], "image_b": ["538", 0]}, "class_type": "Image Comparer (rgthree)", "_meta": {"title": "Base vs Stage 3"}}, "725": {"inputs": {"action": "append", "tidy_tags": "no", "text_a": ["446", 0], "text_b": "_UPSCALED_STAGE_2", "text_c": "", "result": "E36A2A49C5C598919CC2C7DBFB49D5A4_UPSCALED_STAGE_2"}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "726": {"inputs": {"filename_prefix": ["725", 0], "images": ["733", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "733": {"inputs": {"color_match_mode": "Wavelet", "color_ref_image": ["538", 0], "image": ["714", 0]}, "class_type": "DicksonColorMatch", "_meta": {"title": "Dickson Color Match"}}, "736": {"inputs": {"stop_at_clip_layer": -1, "clip": ["717", 1]}, "class_type": "CLIPSetLastLayer", "_meta": {"title": "设置CLIP最后一层"}}, "737": {"inputs": {"vae_name": "sdxl_vae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "750": {"inputs": {"model_name": "4xNomos2_hq_dat2.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "加载放大模型"}}, "751": {"inputs": {"upscale_model": ["750", 0], "image": ["634", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "使用模型放大图像"}}, "752": {"inputs": {"width": 2048, "height": 2048, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["751", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "753": {"inputs": {"color_match_mode": "Wavelet", "color_ref_image": ["538", 0], "image": ["752", 0]}, "class_type": "DicksonColorMatch", "_meta": {"title": "Dickson Color Match"}}, "756": {"inputs": {"width": 3072, "height": 3072, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["631", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}}