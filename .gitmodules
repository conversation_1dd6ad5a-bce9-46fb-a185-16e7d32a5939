[submodule "nonebot-plugin-comfyui"]
	path = nonebot-plugin-comfyui
	url = https://github.com/DiaoDaiaChan/nonebot-plugin-comfyui
[submodule "gemini-vision"]
	path = gemini-vision
	url = **************:X-Zero-L/nonebot-plugin-gemini-vision.git
[submodule "nonebot-plugin-mcp"]
	path = nonebot-plugin-mcp
	url = **************:X-Zero-L/nonebot-plugin-mcp.git
[submodule "nonebot-plugin-message-bridge"]
	path = nonebot-plugin-message-bridge
	url = **************:X-Zero-L/nonebot-plugin-message-bridge.git
[submodule "mcp_servers/bilibili-mcp-server"]
	path = mcp_servers/bilibili-mcp-server
	url = **************:huccihuang/bilibili-mcp-server.git
[submodule "nonebot-plugin-mhguesser"]
	path = nonebot-plugin-mhguesser
	url = **************:X-Zero-L/nonebot-plugin-mhguesser.git
[submodule "nonebot-plugin-aniguessr"]
	path = nonebot-plugin-aniguessr
	url = **************:X-Zero-L/nonebot-plugin-aniguessr.git
[submodule "nonebot-plugin-gpt-image"]
	path = nonebot-plugin-gpt-image
	url = **************:X-Zero-L/nonebot-plugin-gpt-image.git
[submodule "nonebot-plugin-permission"]
	path = nonebot-plugin-permission
	url = **************:RF-Tar-Railt/nonebot-plugin-permission.git
[submodule "nonebot-plugin-oi-helper"]
	path = nonebot-plugin-oi-helper
	url = **************:X-Zero-L/nonebot-plugin-oi-helper.git
