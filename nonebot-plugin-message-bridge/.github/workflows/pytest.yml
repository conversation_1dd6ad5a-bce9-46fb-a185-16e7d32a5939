name: Pytest

on:
  push:
    branches:
      - 'master'
    paths-ignore:
      - 'README.md'
  pull_request:

jobs:
  pytest:
    runs-on: ubuntu-latest
    env:
      LOG_LEVEL: DEBUG
    strategy:
      matrix:
        python-version:
          - "3.10"
          - "3.11"
          - "3.12"

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Install uv and set the python version
      uses: astral-sh/setup-uv@v5
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install Dependencies for test group
      run: |
        uv sync --group test

    - name: Run all tests
      run: |
        uv run pytest