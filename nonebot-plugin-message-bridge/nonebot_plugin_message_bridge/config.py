from nonebot import get_driver, get_plugin_config
from pydantic import BaseModel


class Config(BaseModel):
    """Plugin Config Here"""

    openai_api_key: str | int
    openai_base_url: str
    openai_model: str
    aggregate_message: bool = True
    timezone: str = "Asia/Shanghai"


# 配置加载
config: Config = get_plugin_config(Config)
global_config = get_driver().config

# 全局名称
NICKNAME: str = next(iter(global_config.nickname), "")
