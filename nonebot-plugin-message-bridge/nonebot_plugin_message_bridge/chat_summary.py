import logfire
from nonebot import logger
from pydantic import BaseModel, Field
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider

from .client import openai_client
from .config import config

provider = OpenAIProvider(
    openai_client=openai_client,
)
model = OpenAIModel(config.openai_model, provider=provider)


class ChatTopic(BaseModel):
    topic: str = Field(..., description="话题主题")
    content: str = Field(..., description="话题内容")
    time_start: str = Field(..., description="话题讨论的开始时间")
    time_end: str = Field(..., description="话题讨论的结束时间")
    participants: list[str] = Field(..., description="参与者列表")


class ChatTopicList(BaseModel):
    chat_summary: list[ChatTopic] = Field(..., description="群聊记录的总结列表")
    summary: str = Field(..., description="总结")

    @classmethod
    def merge(cls, topic_lists: list["ChatTopicList"]) -> "ChatTopicList":
        all_topics = []
        summaries = []

        for topic_list in topic_lists:
            all_topics.extend(topic_list.chat_summary)
            if topic_list.summary and topic_list.summary != "出现错误，无法生成总结":
                summaries.append(topic_list.summary)

        all_topics.sort(key=lambda x: x.time_start)

        final_summary = "这些聊天记录令人忍俊不禁，" + "；".join(summaries).replace("这些聊天记录令人忍俊不禁，", "")

        return cls(chat_summary=all_topics, summary=final_summary)


agent = Agent(model, result_type=ChatTopicList, retries=5)


@agent.system_prompt
async def system_prompt():
    return """
你是一个QQ群聊天记录总结助手，你的任务是从用户提供的聊天记录中提取并总结出多个话题的信息，包含话题的主题、内容、开始时间、结束时间和参与者。
最后给出一句话总结，固定句式：这些聊天记录令人忍俊不禁，xxx。
"""


async def get_summary(chat_records_str: str, retries: int = 5) -> ChatTopicList:
    logfire.info(f"开始生成群聊记录总结，群聊记录：{chat_records_str}")
    try:
        return (await agent.run(chat_records_str)).data
    except Exception as e:
        logger.error(f"Error in get_summary: {e}")
        if retries > 0:
            return await get_summary(chat_records_str, retries - 1)
        return ChatTopicList(chat_summary=[], summary="出现错误，无法生成总结")


async def get_summary_str(chat_records_str: str) -> str:
    data = await get_summary(chat_records_str)
    return await build_summary_str(data)


async def build_summary_str(chat_topic_list: ChatTopicList) -> str:
    return "\n".join(await build_summary_list(chat_topic_list))


async def build_summary_list(chat_topic_list: ChatTopicList) -> list[str]:
    s = []
    for i, topic in enumerate(chat_topic_list.chat_summary):
        s.append(
            f"""
{i + 1}. 【{topic.topic}】 在 {topic.time_start} 到 {topic.time_end} 期间：{topic.content}
参与者：{", ".join(topic.participants)}
""".strip()
        )
    s.append(chat_topic_list.summary)
    return s
