import asyncio
import datetime

import logfire
from nonebot import logger, on_message, require
from nonebot.plugin import PluginMetadata, inherit_supported_adapters

require("nonebot_plugin_waiter")
require("nonebot_plugin_uninfo")
require("nonebot_plugin_alconna")
require("nonebot_plugin_localstore")
require("nonebot_plugin_apscheduler")
require("nonebot_plugin_chatrecorder")

from .config import Config

_help = ""
__plugin_meta__ = PluginMetadata(
    name="消息转发",
    description="在不同平台/群聊之间转发消息",
    usage=f"{_help}",
    type="application",  # library
    homepage="https://github.com/X-Zero-L/nonebot-plugin-message-bridge",
    config=Config,
    supported_adapters=inherit_supported_adapters("nonebot_plugin_alconna", "nonebot_plugin_uninfo"),
    # supported_adapters={"~onebot.v11"}, # 仅 onebot 应取消注释
    extra={"author": "X-Zero-L <<EMAIL>>"},
)

from nonebot_plugin_alconna import Alconna, CustomNode, Reference, Reply, Target, UniMsg, on_alconna
from nonebot_plugin_alconna.uniseg import UniMessage
from nonebot_plugin_apscheduler import scheduler
from nonebot_plugin_chatrecorder import get_message_records, get_messages_plain_text
from nonebot_plugin_uninfo import Uninfo
from nonebot_plugin_uninfo.orm import get_session_model
from pydantic import BaseModel, Field

from .chat_summary import build_summary_list, get_summary

message_bridge = on_message(priority=1, block=False)

origin_groups = ["1022691140", "778701522"]
target_groups = []  # ["778701522"]

locks: dict[str, asyncio.Lock] = {}


async def merge_forward(msgs: list, uid: str, name: str) -> Reference:
    return Reference(
        nodes=[
            CustomNode(
                uid=uid,
                content=UniMessage(msg),
                name=name,
            )
            for msg in msgs
        ]
    )


class MessageWithInfo(BaseModel):
    message: UniMessage
    session: Uninfo
    timestamp: datetime.datetime = Field(
        default_factory=lambda: datetime.datetime.now(tz=datetime.timezone(datetime.timedelta(hours=8)))
    )

    class Config:
        arbitrary_types_allowed = True


class MessageHistory(BaseModel):
    history: list[MessageWithInfo] = []
    min_size: int = 20

    def add_message(self, message: MessageWithInfo):
        self.history.append(message)

    async def get_forward(self) -> Reference:
        nodes = []
        for msg in self.history:
            nodes.append(CustomNode(uid=msg.session.user.id, content=msg.message, name=msg.session.user.name))
        return Reference(nodes=nodes)

    # get plain info for summary
    def get_plain_info(self) -> str:
        s = "".join(
            (
                f'"{msg.session.user.name}({msg.session.user.id})" '
                f"在{msg.timestamp.strftime('%Y-%m-%d %H:%M:%S')}说:"
                f"{msg.message.extract_plain_text().strip()}\n"
            )
            for msg in self.history
            if msg.message.extract_plain_text().strip()
        )
        return s

    async def get_summary(self) -> Reference:
        result = await get_summary(self.get_plain_info())
        rlist = await build_summary_list(result)
        return Reference(nodes=[CustomNode(content=UniMessage(i)) for i in rlist])

    def clear(self):
        self.history = []

    class Config:
        arbitrary_types_allowed = True


msg_dict: dict[str, MessageHistory] = {}


def get_lock(group_id: str) -> asyncio.Lock:
    """获取用户锁"""
    if group_id not in locks:
        locks[group_id] = asyncio.Lock()
    return locks[group_id]


async def summary_for_group(
    group_id: str, time_start: datetime.datetime | None = None, time_stop: datetime.datetime | None = None
):
    logger.info(f"开始生成群聊记录总结，群聊ID：{group_id}")
    time_start = (
        datetime.datetime.now(tz=datetime.timezone(datetime.timedelta(hours=8))).replace(
            minute=0, second=0, microsecond=0
        )
        - datetime.timedelta(hours=1)
        if time_start is None
        else time_start
    )
    time_stop = datetime.datetime.now(tz=datetime.timezone(datetime.timedelta(hours=8))).replace(
        minute=0, second=0, microsecond=0
    )
    messages = await get_message_records(scene_ids=[group_id], time_start=time_start, time_stop=time_stop)
    message_with_info_list = []
    for message in messages:
        smodel = await get_session_model(message.session_persist_id)
        message_with_info_list.append(
            MessageWithInfo(
                message=UniMessage(message.plain_text),
                session=await smodel.to_session(),
            )
        )

    def get_plain_info(message_with_info: MessageWithInfo) -> str:
        return f'"{message_with_info.session.user.name}({message_with_info.session.user.id})" 在{message_with_info.timestamp.strftime("%Y-%m-%d %H:%M:%S")}说:{message_with_info.message.extract_plain_text().strip()}\n'

    logfire.info(f"共有{len(messages)}条记录", messages=messages)
    result = await get_summary("".join(get_plain_info(i) for i in message_with_info_list))
    rlist = await build_summary_list(result)
    rlist = [
        f"以下是{time_start.strftime('%Y-%m-%d %H:%M:%S')}~{time_stop.strftime('%Y-%m-%d %H:%M:%S')}的群聊记录总结",
        *rlist,
    ]
    await UniMessage(
        Reference(nodes=[CustomNode(content=UniMessage(i), uid="0", name="群聊记录总结") for i in rlist])
    ).send(target=Target(group_id))


# v2 summary,直接使用chatrecorder获取已有记录，而不是手动维护消息记录
async def summary_v2():
    for group_id in origin_groups:
        async with get_lock(group_id):
            await summary_for_group(group_id)


# @scheduler.scheduled_job("cron", hour="*", minute="0", second="0")
# async def summary_v2_job():
#    await summary_v2()


@message_bridge.handle()
async def _(msg: UniMsg, session: Uninfo):
    if not session.group:
        return
    group_id = session.group.id
    if group_id not in origin_groups:
        return
    async with get_lock(group_id):
        if group_id not in msg_dict:
            msg_dict[group_id] = MessageHistory()
        if msg.has(Reference):
            logger.info("收到合并转发消息，跳过")
            # msg_dict[group_id].add_message(MessageWithInfo(message="一条合并转发消息，你看不到喵", session=session))
        elif msg.has(Reply):
            logger.info("收到回复消息，跳过")
            msg_dict[group_id].add_message(MessageWithInfo(message=msg, session=session))
        else:
            msg_dict[group_id].add_message(MessageWithInfo(message=msg, session=session))
        if len(msg_dict[group_id].history) >= msg_dict[group_id].min_size:
            pass
            """
            result = await msg_dict[group_id].get_forward()
            for gid in target_groups:
                await UniMessage(result).send(target=Target(gid))
            msg_dict[group_id].clear()
            """


"""
summary_command = on_alconna(
    Alconna(
        "总结",
    ),
    use_cmd_start=True,
    priority=5,
    block=True,
    aliases={"群聊记录总结"},
)


@summary_command.handle()
async def _(session: Uninfo):
    await summary_for_group(session.group.id)
"""
