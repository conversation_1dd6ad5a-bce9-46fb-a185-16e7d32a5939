{"https://pd.qq.com/s/cy6traegg?businessType=9\\n永无广告": {"title": "【腾讯频道邀请】欢迎加入基地频道频道！", "description": "未知描述", "image": "https://groupprohead.gtimg.cn/673664574033097989/100?t=1730720323978", "time": 1751722197.8006074}, "https://y.music.163.com/m/song?id=1976814366&amp;userid=591615832&amp;dlt=0846\"&#44;\"musicUrl\":\"http://music.163.com/song/media/outer/url?id=1976814366&amp;userid=591615832\"&#44;\"preview\":\"http://p2.music.126.net/KCkDh-y2dySXIh-LkRG0lQ==/109951167830554729.jpg?imageView=1&amp;thumbnail=1220z2466&amp;type=webp&amp;quality=80\"&#44;\"tag\":\"网易云音乐\"&#44;\"tagIcon\":\"https://i.gtimg.cn/open/app_icon/00/49/50/85/100495085_100_m.png\"&#44;\"title\":\"ヤミナベ!!!!": {"title": "ヤミナベ!!!! (feat. 初音ミク)（黑暗火锅） - cosMo@暴走P/初音ミク - 单曲 - 网易云音乐", "description": "歌曲名《ヤミナベ!!!! (feat. 初音ミク)》，别名《黑暗火锅》，由 cosMo@暴走P、初音ミク 演唱，收录于《ヤミナベ!!!!》专辑中，《ヤミナベ!!!! (feat. 初音ミク)》下载，《ヤミナベ!!!! (feat. 初音ミク)》在线试听，更多ヤミナベ!!!! (feat. 初音ミク)相关歌曲推荐，尽在网易云音乐", "image": "http://p2.music.126.net/KCkDh-y2dySXIh-LkRG0lQ==/109951167830554729.jpg", "time": 1751200148.4445164}, "https://y.music.163.com/m/song?id=1830036458&amp;userid=389934880&amp;dlt=0846\"&#44;\"musicUrl\":\"http://music.163.com/song/media/outer/url?id=1830036458&amp;userid=389934880\"&#44;\"preview\":\"http://p2.music.126.net/cBSAUaXZdm1CVx8Ekj7YOg==/109951165810845336.jpg?imageView=1&amp;thumbnail=1220z2464&amp;type=webp&amp;quality=80\"&#44;\"tag\":\"网易云音乐\"&#44;\"tagIcon\":\"https://i.gtimg.cn/open/app_icon/00/49/50/85/100495085_100_m.png\"&#44;\"title\":\"メンタルチェンソー": {"title": "メンタルチェンソー（精神电锯） - P丸様。/かいりきベア - 单曲 - 网易云音乐", "description": "歌曲名《メンタルチェンソー》，别名《精神电锯》，由 P丸様。、かいりきベア 演唱，收录于《Sunny!!》专辑中，《メンタルチェンソー》下载，《メンタルチェンソー》在线试听，更多メンタルチェンソー相关歌曲推荐，尽在网易云音乐", "image": "http://p1.music.126.net/cBSAUaXZdm1CVx8Ekj7YOg==/109951165810845336.jpg", "time": 1751200372.7428045}, "https://y.music.163.com/m/song?id=36103435&amp;userid=1325646902&amp;dlt=0846\"&#44;\"musicUrl\":\"http://music.163.com/song/media/outer/url?id=36103435&amp;userid=1325646902\"&#44;\"preview\":\"https://p2.music.126.net/gCGcCdrnln9ws6j4tXq1eQ==/109951169179648367.jpg?imageView=1&amp;thumbnail=1440z3024&amp;type=webp&amp;quality=80\"&#44;\"tag\":\"网易云音乐\"&#44;\"tagIcon\":\"https://i.gtimg.cn/open/app_icon/00/49/50/85/100495085_100_m.png\"&#44;\"title\":\"Caliburne": {"title": "Caliburne ~Story of the Legendary sword~ - Project Grimoire - 单曲 - 网易云音乐", "description": "歌曲名《Caliburne ~Story of the Legendary sword~》，由 Project Grimoire 演唱，收录于《Grimoire of Emerald》专辑中，《Caliburne ~Story of the Legendary sword~》下载，《Caliburne ~Story of the Legendary sword~》在线试听，更多Caliburne ~Story of the Legendary sword~相关歌曲推荐，尽在网易云音乐", "image": "http://p1.music.126.net/gCGcCdrnln9ws6j4tXq1eQ==/109951169179648367.jpg", "time": 1751201983.7627428}, "https://y.music.163.com/m/song?id=2075557994&amp;userid=1325646902&amp;dlt=0846\"&#44;\"musicUrl\":\"http://music.163.com/song/media/outer/url?id=2075557994&amp;userid=1325646902\"&#44;\"preview\":\"http://p1.music.126.net/QqKuevqcCZ7BWtY53Chb8g==/109951168860116011.jpg?imageView=1&amp;thumbnail=1440z3024&amp;type=webp&amp;quality=80\"&#44;\"tag\":\"网易云音乐\"&#44;\"tagIcon\":\"https://i.gtimg.cn/open/app_icon/00/49/50/85/100495085_100_m.png\"&#44;\"title\":\"Anhedonia\"&#44;\"uin\":1466392697}}&#44;\"prompt\":\"&#91;分享&#93;Anhedonia\"&#44;\"ver\":\"*******\"&#44;\"view\":\"music\"}]": {"title": "Anhedonia - a_hisa - 单曲 - 网易云音乐", "description": "歌曲名《Anhedonia》，由 a_hisa 演唱，收录于《Luminous》专辑中，《Anhedonia》下载，《Anhedonia》在线试听，更多Anhedonia相关歌曲推荐，尽在网易云音乐", "image": "http://p2.music.126.net/QqKuevqcCZ7BWtY53Chb8g==/109951168860116011.jpg", "time": 1751202519.7457998}, "https://y.music.163.com/m/song?id=28403160&amp;uct2=pRhp65wzpPtgCSEKC2Qt1w%3D%3D&amp;fx-wechatnew=t1&amp;fx-wxqd=c&amp;fx-wordtest=&amp;fx-listentest=t3&amp;H5_DownloadVIPGift=&amp;dlt=0846&amp;app_version=9.3.30\"&#44;\"musicUrl\":\"http://music.163.com/song/media/outer/url?id=28403160&amp;userid=389934880&amp;sc=wm&amp;tn=\"&#44;\"preview\":\"https://p2.music.126.net/vrOG0hAhslI4djtZdMgE6g==/6044015418094303.jpg?imageView=1&amp;thumbnail=2800z1840&amp;type=webp&amp;quality=80\"&#44;\"tag\":\"网易云音乐\"&#44;\"tagIcon\":\"https://i.gtimg.cn/open/app_icon/00/49/50/85/100495085_100_m.png\"&#44;\"title\":\"トルコ行進曲": {"title": "トルコ行進曲 - オワタ＼(^o^)（土耳其进行曲-完蛋啦＼(^o^)） - 96猫 - 单曲 - 网易云音乐", "description": "歌曲名《トルコ行進曲 - オワタ＼(^o^)》，别名《土耳其进行曲-完蛋啦＼(^o^)》，由 96猫 演唱，收录于《WHICH》专辑中，《トルコ行進曲 - オワタ＼(^o^)》下载，《トルコ行進曲 - オワタ＼(^o^)》在线试听，更多トルコ行進曲 - オワタ＼(^o^)相关歌曲推荐，尽在网易云音乐", "image": "http://p1.music.126.net/vrOG0hAhslI4djtZdMgE6g==/6044015418094303.jpg", "time": 1751202829.564684}, "https://github.com/lechmazur/writing?tab=readme-ov-file": {"title": "GitHub - lechmazur/writing: This benchmark tests how well LLMs incorporate a set of 10 mandatory story elements (characters, objects, core concepts, attributes, motivations, etc.) in a short creative story", "description": "This benchmark tests how well LLMs incorporate a set of 10 mandatory story elements (characters, objects, core concepts, attributes, motivations, etc.) in a short creative story - lechmazur/writing", "image": "https://opengraph.githubassets.com/ceaf4c2a535240d83cd356a059ffba113fb444afba5021a1f073ec37d89e40e5/lechmazur/writing", "time": 1751203644.1623826}, "https://y.music.163.com/m/song?id=1493976323&amp;userid=591615832&amp;dlt=0846\"&#44;\"musicUrl\":\"http://music.163.com/song/media/outer/url?id=1493976323&amp;userid=591615832\"&#44;\"preview\":\"http://p1.music.126.net/-p01TaQIFhbnQfM9K-vMjA==/109951166716419473.jpg?imageView=1&amp;thumbnail=1220z2466&amp;type=webp&amp;quality=80\"&#44;\"tag\":\"网易云音乐\"&#44;\"tagIcon\":\"https://i.gtimg.cn/open/app_icon/00/49/50/85/100495085_100_m.png\"&#44;\"title\":\"Eltina\"&#44;\"uin\":1877116372}}&#44;\"prompt\":\"&#91;分享&#93;Eltina\"&#44;\"ver\":\"*******\"&#44;\"view\":\"music\"}]": {"title": "Eltina - Feryquitous - 单曲 - 网易云音乐", "description": "歌曲名《<PERSON><PERSON>》，由 Feryquitous 演唱，收录于《<PERSON><PERSON>》专辑中，《<PERSON><PERSON>》下载，《<PERSON><PERSON>》在线试听，更多Eltina相关歌曲推荐，尽在网易云音乐", "image": "http://p1.music.126.net/-p01TaQIFhbnQfM9K-vMjA==/109951166716419473.jpg", "time": 1751205583.9013758}, "https://y.music.163.com/m/song?id=1960384346&amp;uct2=oowe6lKtqDu9cFHD8S%2BPWQ%3D%3D&amp;fx-wechatnew=t1&amp;fx-wxqd=c&amp;fx-wordtest=&amp;fx-listentest=t3&amp;H5_DownloadVIPGift=&amp;playerUIModeId=76001&amp;PlayerStyles_SynchronousSharing=t3&amp;dlt=0846&amp;app_version=9.3.20\"&#44;\"preview\":\"https://pic.ugcimg.cn/29cc35a6bfb4e68fdfbc5dbf68421f6a/jpg1\"&#44;\"tag\":\"网易云音乐\"&#44;\"tagIcon\":\"https://i.gtimg.cn/open/app_icon/00/49/50/85/100495085_100_m.png\"&#44;\"title\":\"夏霞": {"title": "夏霞 (Acoustic ver.)（夏日薄雾） - あたらよ - 单曲 - 网易云音乐", "description": "歌曲名《夏霞 (Acoustic ver.)》，别名《夏日薄雾》，由 あたらよ 演唱，收录于《Acoustic Session》专辑中，《夏霞 (Acoustic ver.)》下载，《夏霞 (Acoustic ver.)》在线试听，更多夏霞 (Acoustic ver.)相关歌曲推荐，尽在网易云音乐", "image": "http://p2.music.126.net/HymDERNi6HQa0PHVxhvstQ==/109951167607871650.jpg", "time": 1751205754.6441393}, "https://github.com/TermoraDev/termora": {"title": "GitHub - TermoraDev/termora: Termora is a terminal emulator and SSH client for Windows, macOS and Linux.", "description": "Termora is a terminal emulator and SSH client for Windows, macOS and Linux. - TermoraDev/termora", "image": "https://opengraph.githubassets.com/8ff5a5eab554ae4abb73892711a1fe1fd4eb18dffcb5109c8767327628368827/TermoraDev/termora", "time": 1751210329.660177}, "https://github.com/mit-han-lab/nunchaku/blob/main/README_ZH.md": {"title": "nunchaku/README_ZH.md at main · mit-han-lab/nunchaku · GitHub", "description": "[ICLR2025 Spotlight] SVDQuant: Absorbing Outliers by Low-Rank Components for 4-Bit Diffusion Models - nunchaku/README_ZH.md at main · mit-han-lab/nunchaku", "image": "https://opengraph.githubassets.com/55a8994269c901110c27220acf4c2153bae1f5d37d631f4b47f8a5c2743085cd/mit-han-lab/nunchaku", "time": 1751231759.902428}, "https://y.music.163.com/m/song?id=1989905180&amp;userid=484487323&amp;dlt=0846\"&#44;\"musicUrl\":\"http://music.163.com/song/media/outer/url?id=1989905180&amp;userid=484487323\"&#44;\"preview\":\"https://p1.music.126.net/QIy3uo31jR4faBDjardKFQ==/109951167971238239.jpg?imageView=1&amp;thumbnail=1080z2153&amp;type=webp&amp;quality=80\"&#44;\"tag\":\"网易云音乐\"&#44;\"tagIcon\":\"https://i.gtimg.cn/open/app_icon/00/49/50/85/100495085_100_m.png\"&#44;\"title\":\"おくすり飲んで寝よう": {"title": "おくすり飲んで寝よう (feat. 初音ミク)（吃了药就去睡觉吧~） - もちうつね/初音ミク - 单曲 - 网易云音乐", "description": "歌曲名《おくすり飲んで寝よう (feat. 初音ミク)》，别名《吃了药就去睡觉吧~》，由 もちうつね、初音ミク 演唱，收录于《おくすり飲んで寝よう (feat. 初音ミク)》专辑中，《おくすり飲んで寝よう (feat. 初音ミク)》下载，《おくすり飲んで寝よう (feat. 初音ミク)》在线试听，更多おくすり飲んで寝よう (feat. 初音ミク)相关歌曲推荐，尽在网易云音乐", "image": "http://p2.music.126.net/QIy3uo31jR4faBDjardKFQ==/109951167971238239.jpg", "time": 1751255385.9239025}, "https://y.music.163.com/m/song?id=2086726248&amp;userid=484487323&amp;dlt=0846\"&#44;\"preview\":\"https://pic.ugcimg.cn/f23bd22d18ad4b7c363bbab8ebabbc24/jpg1\"&#44;\"tag\":\"网易云音乐\"&#44;\"tagIcon\":\"https://i.gtimg.cn/open/app_icon/00/49/50/85/100495085_100_m.png\"&#44;\"title\":\"風呂入るプロファイル": {"title": "風呂入るプロファイル（洗澡澡） - もちうつね - 单曲 - 网易云音乐", "description": "歌曲名《風呂入るプロファイル》，别名《洗澡澡》，由 もちうつね 演唱，收录于《風呂入るプロファイル》专辑中，《風呂入るプロファイル》下载，《風呂入るプロファイル》在线试听，更多風呂入るプロファイル相关歌曲推荐，尽在网易云音乐", "image": "http://p2.music.126.net/OZCvtfBrjHKGhRIB2D31lA==/109951168952280159.jpg", "time": 1751255420.831232}, "https://upmind.com/": {"title": "All-In-One Billing and Automation Platform | Upmind", "description": "Upmind is an all-in-one platform for business automation, eCommerce and billing solutions that comes with provisioning, integrations, client management and more.", "image": "https://framerusercontent.com/images/OvApse4P9s5xpJV0Pxuxhbv9Rk.webp", "time": 1751266587.9502344}, "https://api.xiaoheihe.cn/v3/bbs/app/api/web/share?link_id=76a0707f139e\"&#44;\"preview\":\"https://pic.ugcimg.cn/3070b979f9ad41b2f3c7bc795c543338/jpg1\"&#44;\"tag\":\"小黑盒\"&#44;\"tagIcon\":\"https://open.gtimg.cn/open/app_icon/05/91/08/06/1105910806_100_m.png?t=1750906950\"&#44;\"title\":\"小岛工作室快闪详情公布（上海站），良笑GoodSmile✖小…\"&#44;\"uin\":1260572994}}&#44;\"prompt\":\"&#91;分享&#93;小岛工作室快闪详情公布（上海站），良笑GoodSmile✖小…\"&#44;\"ver\":\"*******\"&#44;\"view\":\"news\"}]": {"title": "高能玩家聚集地 - 小黑盒", "description": "小黑盒", "image": "https://imgheybox.max-c.com/oa/2024/11/25/********************************.png", "time": 1751280864.9373777}, "https://github.com/soevielofficial/nte-assets": {"title": "GitHub - soevielofficial/nte-assets: Repository containing assets from the game Neverness to Everness.", "description": "Repository containing assets from the game Neverness to Everness. - soevielofficial/nte-assets", "image": "https://opengraph.githubassets.com/0fa7e0779277b3dc8bc2bb72d039eb1191e109a928e5e21bc1914a189877ee52/soevielofficial/nte-assets", "time": 1751509529.891875}, "https://www.pixiv.net/artworks/132205784": {"title": "未知标题", "description": "この作品 「みずえなみず(肌色多め+🔞)まとめ②」 は 「R-18」「みずえな」 等のタグがつけられた「𝕞𝕦𝕥𝕦」さんのイラストです。", "image": "https://s.pximg.net/www/images/pixiv_logo20250128.png", "time": 1751519914.8353658}, "https://y.music.163.com/m/song?id=26438337&amp;userid=591615832&amp;dlt=0846\"&#44;\"preview\":\"https://pic.ugcimg.cn/4523977c94bf2736fa331330f24e0d53/jpg1\"&#44;\"tag\":\"网易云音乐\"&#44;\"tagIcon\":\"https://i.gtimg.cn/open/app_icon/00/49/50/85/100495085_100_m.png\"&#44;\"title\":\"M\"&#44;\"uin\":1877116372}}&#44;\"prompt\":\"&#91;分享&#93;M\"&#44;\"ver\":\"*******\"&#44;\"view\":\"news\"}]": {"title": "M - サカナクション - 单曲 - 网易云音乐", "description": "歌曲名《M》，由 サカナクション 演唱，收录于《sakanaction》专辑中，《M》下载，《M》在线试听，更多M相关歌曲推荐，尽在网易云音乐", "image": "http://p1.music.126.net/o5KCqGVE4TGua3ob66N2SA==/109951171282712666.jpg", "time": 1751540521.8794332}, "https://api.xiaoheihe.cn/v3/bbs/app/api/web/share?link_id=c4cd708327b6\"&#44;\"preview\":\"https://pic.ugcimg.cn/d155dfe9ab56860228d37213ac84d814/jpg1\"&#44;\"tag\":\"小黑盒\"&#44;\"tagIcon\":\"https://open.gtimg.cn/open/app_icon/05/91/08/06/1105910806_100_m.png?t=1750906950\"&#44;\"title\":\"恋爱AVG新作《中国式地雷女》商店页面公开！\"&#44;\"uin\":1260572994}}&#44;\"prompt\":\"&#91;分享&#93;恋爱AVG新作《中国式地雷女》商店页面公开！\"&#44;\"ver\":\"*******\"&#44;\"view\":\"news\"}]": {"title": "高能玩家聚集地 - 小黑盒", "description": "小黑盒", "image": "https://imgheybox.max-c.com/oa/2024/11/25/********************************.png", "time": 1751541368.492317}, "https://api.xiaoheihe.cn/v3/bbs/app/api/web/share?link_id=eea270836712\"&#44;\"preview\":\"https://pic.ugcimg.cn/3070b979f9ad41b2f3c7bc795c543338/jpg1\"&#44;\"tag\":\"小黑盒\"&#44;\"tagIcon\":\"https://open.gtimg.cn/open/app_icon/05/91/08/06/1105910806_100_m.png?t=1750906950\"&#44;\"title\":\"我管你这那的！《怪物猎人：荒野》实行封号“连坐”制度\"&#44;\"uin\":1010344691}}&#44;\"prompt\":\"&#91;分享&#93;我管你这那的！《怪物猎人：荒野》实行封号“连坐”制度\"&#44;\"ver\":\"*******\"&#44;\"view\":\"news\"}]": {"title": "高能玩家聚集地 - 小黑盒", "description": "小黑盒", "image": "https://imgheybox.max-c.com/oa/2024/11/25/********************************.png", "time": 1751552210.7364726}, "https://github.com/getAsterisk/claudia": {"title": "GitHub - getAsterisk/claudia: A powerful GUI app and Toolkit for Claude Code - Create custom agents, manage interactive Claude Code sessions, run secure background agents, and more.", "description": "A powerful GUI app and Toolkit for Claude Code - Create custom agents, manage interactive Claude Code sessions, run secure background agents, and more. - getAsterisk/claudia", "image": "https://opengraph.githubassets.com/98bcd53541228125c709f18e0ca9c8c5d5a3bde59da6b006608a717b6b8d197d/getAsterisk/claudia", "time": **********.041986}, "https://github.com/hexgrad/kokoro": {"title": "GitHub - hexgrad/kokoro: https://hf.co/hexgrad/Kokoro-82M", "description": "https://hf.co/hexgrad/Kokoro-82M. Contribute to hexgrad/kokoro development by creating an account on GitHub.", "image": "https://opengraph.githubassets.com/265510e6e3931a1b434b97e98637c4c75a16cb1413e88783715026150dcfee4f/hexgrad/kokoro", "time": **********.0155525}, "https://dopa-game.jp/?utm_source=google&amp;utm_medium=cpc&amp;utm_campaign=search_new_brand_single&amp;argument=XNYk30Vl&amp;dmai=a664b50afbc326&amp;gad_source=1&amp;gad_campaignid=***********&amp;gbraid=0AAAAApb741BUc0Wrw4NLoMY_G_nEjGqg0&amp;gclid=CjwKCAjwx8nCBhAwEiwA_z__04D0_BUlPLZt71pGg53ki1QxH_K-jMrnXJAGtnk39l54fKZ-gNJKwhoC3_sQAvD_BwE": {"title": "DOPAオリパ | ネットオリパ・オンラインオリパ", "description": "【公式】ネットオリパ・オンラインオリパのDOPA(ドーパ)！SNSの当選報告が大量で安心、1口100円以下のオリパなど豊富なラインナップで初めての方でも思う存分にオリパをお楽しみいただけます。ポケカ、ワンピ、遊戯王、ヴァイス、ユニアリなどの楽しいオリパを毎日大放出！LINE友達登録で最大70%OFFのクーポンを配布中！", "image": "https://dopa-game.jp/dopa_app_large.png", "time": 1751566799.8026204}, "https://api.xiaoheihe.cn/v3/bbs/app/api/web/share?link_id=91687084870a\"&#44;\"preview\":\"https://pic.ugcimg.cn/2c5623dfec9deeabb696e89d2f182600/jpg1\"&#44;\"tag\":\"小黑盒\"&#44;\"tagIcon\":\"https://open.gtimg.cn/open/app_icon/05/91/08/06/1105910806_100_m.png?t=1750906950\"&#44;\"title\":\"你是？\"&#44;\"uin\":1260572994}}&#44;\"prompt\":\"&#91;分享&#93;你是？\"&#44;\"ver\":\"*******\"&#44;\"view\":\"news\"}]": {"title": "高能玩家聚集地 - 小黑盒", "description": "小黑盒", "image": "https://imgheybox.max-c.com/oa/2024/11/25/********************************.png", "time": 1751641306.88285}, "https://api.xiaoheihe.cn/v3/bbs/app/api/web/share?link_id=c6de7085013e\"&#44;\"preview\":\"https://pic.ugcimg.cn/3705061585feb144b29716cb5c59d2e8/jpg1\"&#44;\"tag\":\"小黑盒\"&#44;\"tagIcon\":\"https://open.gtimg.cn/open/app_icon/05/91/08/06/1105910806_100_m.png?t=1750906950\"&#44;\"title\":\"穿着玩\"&#44;\"uin\":1260572994}}&#44;\"prompt\":\"&#91;分享&#93;穿着玩\"&#44;\"ver\":\"*******\"&#44;\"view\":\"news\"}]": {"title": "高能玩家聚集地 - 小黑盒", "description": "小黑盒", "image": "https://imgheybox.max-c.com/oa/2024/11/25/********************************.png", "time": 1751650283.294307}, "https://trace.moe/": {"title": "未知标题", "description": "Search Anime by ScreenShot. Lookup the exact moment and the episode.", "image": "https://trace.moe/favicon128.png", "time": 1751691553.652586}, "https://github.com/52funny/pikpakcli": {"title": "GitHub - 52funny/pikpakcli: pikpak command line tool. Including download, upload, share and so on.", "description": "pikpak command line tool. Including download, upload, share and so on. - 52funny/pikpakcli", "image": "https://opengraph.githubassets.com/79173589f5f71a3b24dcf242bd27af0d5cd25a7edcb5ea08d7826ffccfac0449/52funny/pikpakcli", "time": 1751715588.7881827}}