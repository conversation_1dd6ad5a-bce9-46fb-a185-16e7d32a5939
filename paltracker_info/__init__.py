from nonebot import get_plugin_config, require

require("nonebot_plugin_alconna")
from nonebot.adapters import Bo<PERSON>, Event
from nonebot.params import Depends
from nonebot.plugin import PluginMetadata
from nonebot_plugin_alconna import (
    Alconna,
    AlconnaMatches,
    AlcResult,
    Args,
    Arparma,
    CommandResult,
    Match,
    MsgId,
    Option,
    on_alconna,
)
from nonebot_plugin_alconna import (
    Image as AlconnaImage,
)
from pydantic import BaseModel

from .. import aiorequests
from ..txt2img import Txt2Img
from .config import Config

__plugin_meta__ = PluginMetadata(
    name="paltracker_info",
    description="",
    usage="",
    config=Config,
)

config = get_plugin_config(Config)


class RequestBodyForCheckItem(BaseModel):
    player_name: str | None = None
    change_count: int | None = None
    item_id: str | None = None
    time: str | None = None
    is_gain: bool | None = None
    is_lose: bool | None = None
    is_no_change: bool | None = None


class ItemChange(BaseModel):
    player_name: str
    change_count: int
    item_id: str
    time: str


check_item_command = on_alconna(
    Alconna(
        "帕鲁开盒",
        Option("name|--name|-n", Args["player_name?", str]),
        Option("change_count", Args["change_count?", int]),
        Option("item_id|-i|--item", Args["item_id?", str]),
        Option("time", Args["time?", str]),
        Option("--filter|-f|filter", Args["mode?", str]),
        Option("--last|-l|last", Args["last?", int]),
        Option("--help|-h|help|帮助", Args["help?", str]),
        Option("--debug|-d|debug", Args["debug?", str]),
        Option("--limit|-L|limit", Args["limit?", int]),
    ),
    use_cmd_start=True,
    priority=5,
    block=True,
    aliases={"check_item", "paltracker", "paltracker_info", "pb", "/pb"},
)


@check_item_command.handle()
async def _(bot: Bot, event: Event, res: CommandResult, result: Arparma = AlconnaMatches()):
    options = res.result.options
    player_name, change_count, item_id, time, is_gain, is_lose, is_no_change = (
        None,
        None,
        None,
        None,
        None,
        None,
        None,
    )
    debug = False
    last = None
    limit = None
    if result.find("help"):
        await check_item_command.finish(
            """Palworld EP Server CoreProtect:

Params:
  pb help | pb --last <int> | pb --name <str> [--item <str>] [--filter <str>] [--limit <int>] [--debug]

  help, --help, -h: 帮助
  --last, -l: 服务器最新物品异动记录. range[1, 25] default: 20
  --name, -n: 玩家名字
  --item, -i: 物品ID
  --filter, -f: 过滤：查询获取/失去/全部的物品异动. enum(gain, g, lose, l, all, a)
  --limit, -L: 限制返回记录数.
  --debug, -d: 调试模式, 返回原始数据.

Examples:
  pb help
  pb --last 10
  pb --name ABC
  pb -n ABC --item money
  pb -n ABC -i money --filter gain
  pb -i money --limit 20
  pb -n ABC -i money --filter all --limit 20 --debug

Utilities：
  > item name2id: https://pwmodding.wiki/docs/game-data/item-table 
  > pal name: https://pwmodding.wiki/docs/game-data/monster-table
  > pal skills: https://pastebin.com/raw/2yqbNPVz 
  > pal passive skills: https://pastejustit.com/rcn3v68tly
""".strip()
        )
    if result.find("name"):
        player_name = result.query[str]("name.player_name")
    if result.find("change_count"):
        change_count = result.query[int]("change_count.change_count")
    if result.find("item_id"):
        item_id = result.query[str]("item_id.item_id")
    if result.find("time"):
        time = result.query[str]("time.time")
    if result.find("filter"):
        mode = result.query[str]("filter.mode")
        if mode in ["gain", "g", "G"]:
            is_gain = True
        elif mode in ["lose", "l", "L"]:
            is_lose = True
            is_gain = None
        elif mode in ["no_change", "n", "N"]:
            is_no_change = True
            is_gain = None
        elif mode in ["all", "a", "A"]:
            is_gain = None
            is_lose = None
            is_no_change = None
    if result.find("last"):
        last = result.query[int]("last.last")
        if last is None:
            last = 20
    if result.find("debug"):
        debug = True
    if result.find("limit"):
        limit = result.query[int]("limit.limit")
    if is_lose:
        is_gain = None
        is_no_change = None
    api_base_raw = "http://debian-max:1899/check_item?"
    api_base = api_base_raw
    if player_name is not None:
        api_base += f"player_name={player_name}&"
    if change_count is not None:
        api_base += f"change_count={change_count}&"
    if item_id is not None:
        api_base += f"item_id={item_id}&"
    if time is not None:
        api_base += f"time={time}&"
    if is_gain:
        api_base += f"is_gain={is_gain}&"
    if is_lose:
        api_base += f"is_lose={is_lose}&"
    if is_no_change:
        api_base += f"is_no_change={is_no_change}&"
    if last is not None:
        api_base = f"{api_base_raw}"
    if debug:
        api_base += f"debug={debug}&"
    print(api_base)
    if last and (last < 1 or last > 25):
        await check_item_command.finish("last 参数必须在 1 到 25 之间")
    if not last and not any([player_name, item_id]):
        await check_item_command.finish("请至少提供一个参数")
    response = await aiorequests.get(api_base)
    json_response = await response.json()
    data = [ItemChange(**item) for item in json_response]
    if not data:
        await check_item_command.finish("未找到任何记录")
    if last is not None:
        data = data[-last:]
    elif limit is not None:
        data = data[-limit:]

    def build_str(item: ItemChange):
        if item.change_count > 0:
            return f"{item.player_name} 在 {item.time} 获得了 {item.change_count} 个 {item.item_id}"
        else:
            return f"{item.player_name} 在 {item.time} 失去了 {-item.change_count} 个 {item.item_id}"

    response = []
    if not debug:
        data_group_by_time_and_name = {}
        for item in data:
            key = (item.time, item.player_name)
            if key not in data_group_by_time_and_name:
                data_group_by_time_and_name[key] = []
            data_group_by_time_and_name[key].append(item)
        for key in data_group_by_time_and_name:
            uname = key[1]
            time = key[0]
            items = data_group_by_time_and_name[key]
            items_str = ", ".join(
                [f"{item.item_id} {'+' if item.change_count > 0 else ''}{item.change_count}" for item in items]
            )
            response.append(f"{uname} 在 {time}： {items_str}")
    else:
        response = [build_str(item) for item in data]
    if len(response) > 0:
        await check_item_command.send("记录已经获取，正在转为图片，请稍等...")
        txt2img = Txt2Img()
        txt2img.set_font_size(24)
        title = "Palworld EP Server CoreProtect"
        pic = txt2img.draw(title, "\n".join(response))
        msg_builder = AlconnaImage(raw=pic)
    else:
        msg_builder = "\n".join(response)
    await check_item_command.finish(msg_builder)
