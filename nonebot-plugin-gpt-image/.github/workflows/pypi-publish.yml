name: Publish

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

jobs:
  pypi-publish:
    name: Upload release to PyPI
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Install uv and set the python version
      uses: astral-sh/setup-uv@v6
      with:
        python-version: "3.12"
      
    - name: Build a binary wheel and a source tarball
      run: >-
        uv build 

    - name: Publish distribution to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        password: ${{ secrets.PYPI_API_TOKEN }}
