default_install_hook_types: [pre-commit, prepare-commit-msg]
ci:
  autofix_commit_msg: ":rotating_light: auto fix by pre-commit hooks"
  autofix_prs: true
  autoupdate_branch: master
  autoupdate_schedule: monthly
  autoupdate_commit_msg: ":arrow_up: auto update by pre-commit hooks"
repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.8
    hooks:
      - id: ruff
        args: [--fix]
        stages: [pre-commit]
      - id: ruff-format
        stages: [pre-commit]

  - repo: https://github.com/nonebot/nonemoji
    rev: v0.1.4
    hooks:
      - id: nonemoji
        stages: [prepare-commit-msg]
