import base64

from openai import OpenAI

client = OpenAI(base_url="https://api.bltcy.ai/v1", api_key="sk-KMnrtH4NAZgFJYQkD8Eb3dC8Eb2846C98bC32eD00085Df38")

response = client.chat.completions.create(
    model="gpt-4o-mini",
    messages=[{"role": "user", "content": "Generate an image of gray tabby cat hugging an otter with an orange scarf"}],
    tools=[{"type": "image_generation"}],
    tool_choice="image_generation",
)

# Save the image to a file
image_data = [output.result for output in response.output if output.type == "image_generation_call"]

if image_data:
    image_base64 = image_data[0]
    with open("otter.png", "wb") as f:
        f.write(base64.b64decode(image_base64))
